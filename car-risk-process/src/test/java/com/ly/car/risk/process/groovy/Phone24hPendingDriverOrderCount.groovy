package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.time.DateUtils
import org.assertj.core.util.Lists
import org.slf4j.Logger
import org.slf4j.LoggerFactory

 /**
 * 当前订单手机号24小时存在待补款订单关联司机车牌数
 *
 * ycw/ycs_phone_24h_pending_driver_order_count
 */
class Phone24hPendingDriverOrderCount {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();

        String passengerPhone = (String) params.get("passengerPhone")

        Date endTime = new Date();
        Date startTime = DateUtils.addHours(endTime, -24);
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<String> productLineList = Lists.newArrayList("YCW","YCS")

        long orders = bean.phonePendingDriverOrderCount(passengerPhone, productLineList, startTime, endTime);

        resultMap.put("num", orders);
        return resultMap;
    }

}
