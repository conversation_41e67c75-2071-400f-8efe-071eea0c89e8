package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger

//package com.ly.car.risk.process.groovy

import org.slf4j.LoggerFactory

/**
 * 单个司机24小时退款累计金额
 * driver_24h_sfc_refund_money
 */
class Driver24hSfcRefundMoney {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String carNum = (String)params.get("carNum");
        if(StringUtils.isBlank(carNum)){
            return resultMap;
        }
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.car24HourTotalOrder(carNum,"YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        BigDecimal refundMoney = new BigDecimal(0);
        for(CarRiskOrderDetail orderDetail : orders){
            if(null != orderDetail.getRefundMoney()){
                refundMoney = refundMoney.add(orderDetail.getRefundMoney());
            }
        }
        resultMap.put("num",refundMoney.doubleValue());
        return resultMap;
    }

}
