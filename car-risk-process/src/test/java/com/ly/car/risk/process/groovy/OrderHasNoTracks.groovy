package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * @Description: 订单无轨迹
 * order_has_no_tracks
 * @Author: jay.he
 * @Date: 2025-09-11 10:00
 * @Version: 1.0
 * */
class OrderHasNoTracks {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        boolean result = false;
        String orderId = (String) params.get("orderId");
        if (!StringUtils.isBlank(orderId)) {
            RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
            result = bean.orderHasNoTracks(orderId);
        }
        //满足条件：1，不满足：0
        if (result) {
            resultMap.put("num", 1);
        } else {
            resultMap.put("num", 0);
        }
        return resultMap;
    }
}
