package com.ly.car.risk.process;

import com.google.common.collect.Lists;
import com.ly.car.risk.process.client.ShortUrlClient;
import com.ly.car.risk.process.service.dto.order.BaseOrderInfo;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Description of RiskTestModule
 *
 * <AUTHOR>
 * @date 2024/4/28
 * @desc 测试模块
 */
@SpringBootTest(classes = {RiskProcessApplication.class})
@RunWith(SpringRunner.class)
public class RiskTestModule {

    @Resource
    private ShortUrlClient shortUrlClient;

    @Test
    public void testShortUrl(){
        CarOrderDetail orderDetail = new CarOrderDetail();
        orderDetail.setOrderId("cxtestOrderId4");
        orderDetail.setOrderChannel(10177);
        String shortUrl = shortUrlClient.getShortUrl(orderDetail);
        System.out.println(shortUrl);
    }


    @Test
    public void testGroovy() throws IOException, NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        String fileName = "DriverSurchargeGtBookRate.groovy";
        String filePath = "src/test/java/com/ly/car/risk/process/groovy/" + fileName;
        String method = "check";
        Map<String, Object> params = new HashMap<>();
        params.put("memberId", "317186462413");
        params.put("passengerPhone", "18662202587");
        params.put("carNum", "苏U88888");
        CarOrderDetail carOrderDetail = new CarOrderDetail();
        carOrderDetail.setOrderId("YCS20240708141340QPSV4622");
        carOrderDetail.setBaseInfo(new BaseOrderInfo());
        carOrderDetail.getBaseInfo().setSurcharge(new BigDecimal("120"));
        params.put("order", carOrderDetail);
        GroovyClassLoader groovyClassLoader = new GroovyClassLoader();
        Class<?> groovyClass = groovyClassLoader.parseClass(new java.io.File(filePath));
        GroovyObject groovyObject = (GroovyObject) groovyClass.newInstance();

        Map<String, Object> mapResult = (Map<String, Object>) groovyObject.invokeMethod(method, params);
        System.out.println(mapResult);
    }
    
    @Test
    public void testOfflineGroovy() throws IOException, NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        String fileName = "DriverRealRangeAmount.groovy";
        String filePath = "src/test/java/com/ly/car/risk/process/offline/groovy/" + fileName;
        String method = "check";
        Map<String, Object> params = new HashMap<>();
        params.put("memberId", "317186462413");
        params.put("passengerPhone", "18662202587");
        params.put("carNum", "苏U88888");
        params.put("productLineList", Lists.newArrayList("YCW", "YCS"));
        params.put("startTime", "2025-03-10 00:00:00");
        params.put("endTime", "2025-03-11 00:00:00");
        params.put("channels", Lists.newArrayList("433"));
        params.put("supplierCodeList", Lists.newArrayList("DidiPlatform"));
        
        GroovyClassLoader groovyClassLoader = new GroovyClassLoader();
        Class<?> groovyClass = groovyClassLoader.parseClass(new java.io.File(filePath));
        GroovyObject groovyObject = (GroovyObject) groovyClass.newInstance();
        
        Map<String, Object> mapResult = (Map<String, Object>) groovyObject.invokeMethod(method, params);
        System.out.println(mapResult);
    }
}