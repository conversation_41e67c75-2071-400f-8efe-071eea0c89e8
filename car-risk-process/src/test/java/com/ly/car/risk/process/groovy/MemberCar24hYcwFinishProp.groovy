package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.math.RoundingMode
import java.util.stream.Collectors

//package com.ly.car.risk.process.groovy

/**
 * 当前用户24小时内关联单个司机完单占比 网约车
 * member_car_24h_ycw_finish_prop*/
class MemberCar24hYcwFinishProp {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String memberId = (String) params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user24HourFinishOrder(memberId, "YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        Map<String, List<CarRiskOrderDetail>> orderMap = orders.stream()
                .filter({ p -> StringUtils.isNotBlank(p.getCarNum())})
                .collect(Collectors.groupingBy({ p -> p.getCarNum() }));
        int maxCount = 0;
        for (Map.Entry<String, List<CarRiskOrderDetail>> entry : orderMap.entrySet()) {
            if (entry.value.size() > 1 && entry.value.size() > maxCount) {
                maxCount = entry.value.size();
            }
        }
        Double rate = new BigDecimal(maxCount).divide(new BigDecimal(orders.size()), 2, RoundingMode.HALF_UP).doubleValue();

        resultMap.put("num", rate);
        resultMap.put("rate", rate);
        return resultMap;
    }

}
