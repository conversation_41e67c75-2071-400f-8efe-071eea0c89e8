package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 订单完成实际时长（分钟）
 * order_actual_duration_short
 */
class OrderCompleteActualDuration {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String orderId = (String) params.get("orderId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        BigDecimal minus = bean.orderCompleteActualDuration(orderId);

        resultMap.put("num", minus);
        return resultMap;
    }

}
