package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 当前订单乘车人手机号24小时内关联下单用户数（memberID）数 顺风车
 * phone_member_24h_ycs_cancel_num
 */
class PhoneMember24hYcsCancelNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String passengerPhone = (String)params.get("passengerPhone")
        if(StringUtils.isBlank(passengerPhone)){
            return resultMap;
        }
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.phone24HourCancelOrder(passengerPhone,"YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int)orders.stream().filter({p-> StringUtils.isNotBlank(p.getMemberId())})
                .map({ p->p.getMemberId()}).distinct().count();
        resultMap.put("num",count);
        return resultMap;
    }

}
