package com.ly.car.risk.process.strategy;

import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.response.RiskSceneResult;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.utils.DateUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * RiskStrategyHandler测试类
 * 重点测试freezeTime不早于历史记录的逻辑
 */
@ExtendWith(MockitoExtension.class)
class RiskStrategyHandlerTest {

    @Mock
    private RiskHitService riskHitService;

    @InjectMocks
    private RiskStrategyHandler riskStrategyHandler;

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    @Test
    void testEnsureFreezeTimeNotEarlierThanHistory() {
        // 测试确保freezeTime不早于历史记录的方法
        
        UnifyCheckRequest request = new UnifyCheckRequest();
        request.setScene("8-2"); // 特定场景
        request.setOrderId("TEST_ORDER_001");
        
        Date currentTime = new Date();
        Date currentFreezeTime = DateUtil.addMinutes(currentTime, 60); // 1小时后
        Date historyFreezeTime = DateUtil.addMinutes(currentTime, 120); // 2小时后
        
        String currentFreezeTimeStr = DateUtil.getNewFormatDateString(currentFreezeTime);
        
        // 模拟历史记录查询
        when(riskHitService.queryLatestFreezeEndTimeByOrderId("TEST_ORDER_001"))
                .thenReturn(historyFreezeTime);
        
        // 执行方法
        String finalFreezeTime = riskHitService.ensureFreezeTimeNotEarlierThanHistory(request, currentFreezeTimeStr);
        
        // 验证结果：应该返回历史记录的较大时间
        assertEquals(DateUtil.getNewFormatDateString(historyFreezeTime), finalFreezeTime);
    }

    @Test
    void testDefaultFreezeTimeWithHistoryComparison() {
        // 测试兜底冻结时间与历史记录比较的逻辑
        
        UnifyCheckRequest request = new UnifyCheckRequest();
        request.setScene("8-2");
        request.setOrderId("TEST_ORDER_002");
        
        // 模拟兜底冻结时间（订单完成时间+24小时）
        Date orderFinishTime = new Date();
        Date defaultFreezeTime = DateUtil.addHours(orderFinishTime, 24);
        String defaultFreezeTimeStr = DateUtil.getNewFormatDateString(defaultFreezeTime);
        
        // 模拟历史记录有更大的冻结时间
        Date historyFreezeTime = DateUtil.addHours(orderFinishTime, 48); // 48小时后
        when(riskHitService.queryLatestFreezeEndTimeByOrderId("TEST_ORDER_002"))
                .thenReturn(historyFreezeTime);
        
        // 执行方法
        String finalFreezeTime = riskHitService.ensureFreezeTimeNotEarlierThanHistory(request, defaultFreezeTimeStr);
        
        // 验证结果：应该使用历史记录的较大时间
        assertEquals(DateUtil.getNewFormatDateString(historyFreezeTime), finalFreezeTime);
        assertTrue(DateUtil.parseNewFormatDateString(finalFreezeTime).after(defaultFreezeTime));
    }

    @Test
    void testDriverRiskFreezeTimeWithHistoryComparison() {
        // 测试司机风控冻结时间与历史记录比较的逻辑
        
        UnifyCheckRequest request = new UnifyCheckRequest();
        request.setScene("21-1");
        request.setOrderId("TEST_ORDER_003");
        
        // 模拟司机风控冻结时间
        Date todayMorning = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
        Date driverFreezeTime = DateUtil.addMinutes(todayMorning, 1440); // 24小时后
        String driverFreezeTimeStr = DateUtil.getNewFormatDateString(driverFreezeTime);
        
        // 模拟历史记录有更大的冻结时间
        Date historyFreezeTime = DateUtil.addMinutes(todayMorning, 2880); // 48小时后
        when(riskHitService.queryLatestFreezeEndTimeByOrderId("TEST_ORDER_003"))
                .thenReturn(historyFreezeTime);
        
        // 执行方法
        String finalFreezeTime = riskHitService.ensureFreezeTimeNotEarlierThanHistory(request, driverFreezeTimeStr);
        
        // 验证结果：应该使用历史记录的较大时间
        assertEquals(DateUtil.getNewFormatDateString(historyFreezeTime), finalFreezeTime);
        assertTrue(DateUtil.parseNewFormatDateString(finalFreezeTime).after(driverFreezeTime));
    }

    @Test
    void testNonSpecificSceneNoComparison() {
        // 测试非特定场景不进行历史记录比较
        
        UnifyCheckRequest request = new UnifyCheckRequest();
        request.setScene("5-1"); // 非特定场景
        request.setOrderId("TEST_ORDER_004");
        
        Date currentTime = new Date();
        Date currentFreezeTime = DateUtil.addMinutes(currentTime, 60);
        String originalFreezeTime = DateUtil.getNewFormatDateString(currentFreezeTime);
        
        // 执行方法
        String finalFreezeTime = riskHitService.ensureFreezeTimeNotEarlierThanHistory(request, originalFreezeTime);
        
        // 验证结果：非特定场景应该返回原始值
        assertEquals(originalFreezeTime, finalFreezeTime);
    }

    @Test
    void testNullHistoryRecord() {
        // 测试没有历史记录的情况
        
        UnifyCheckRequest request = new UnifyCheckRequest();
        request.setScene("8-2");
        request.setOrderId("TEST_ORDER_005");
        
        Date currentTime = new Date();
        Date currentFreezeTime = DateUtil.addMinutes(currentTime, 60);
        String originalFreezeTime = DateUtil.getNewFormatDateString(currentFreezeTime);
        
        // 模拟没有历史记录
        when(riskHitService.queryLatestFreezeEndTimeByOrderId("TEST_ORDER_005"))
                .thenReturn(null);
        
        // 执行方法
        String finalFreezeTime = riskHitService.ensureFreezeTimeNotEarlierThanHistory(request, originalFreezeTime);
        
        // 验证结果：没有历史记录时应该返回当前值
        assertEquals(originalFreezeTime, finalFreezeTime);
    }

    @Test
    void testCurrentTimeIsLarger() {
        // 测试当前时间比历史记录大的情况
        
        UnifyCheckRequest request = new UnifyCheckRequest();
        request.setScene("8-2");
        request.setOrderId("TEST_ORDER_006");
        
        Date currentTime = new Date();
        Date currentFreezeTime = DateUtil.addMinutes(currentTime, 120); // 2小时后
        Date historyFreezeTime = DateUtil.addMinutes(currentTime, 60);  // 1小时后
        
        String currentFreezeTimeStr = DateUtil.getNewFormatDateString(currentFreezeTime);
        
        // 模拟历史记录查询
        when(riskHitService.queryLatestFreezeEndTimeByOrderId("TEST_ORDER_006"))
                .thenReturn(historyFreezeTime);
        
        // 执行方法
        String finalFreezeTime = riskHitService.ensureFreezeTimeNotEarlierThanHistory(request, currentFreezeTimeStr);
        
        // 验证结果：当前时间更大时应该返回当前时间
        assertEquals(currentFreezeTimeStr, finalFreezeTime);
    }
}
