package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveRecord
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class CancelOfflineBizCheck {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String orderId = (String) params.get("orderId")
        if(StringUtils.isBlank(orderId)){
            return resultMap;
        }
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<SensitiveRecord> records = bean.order24hourSensitiveMatchRecord(orderId);
        if (CollUtil.isEmpty(records)) {
            return resultMap;
        }
        int count = (int) records.stream().map({ p -> Objects.equals(p.getWordType(), 5) }).count();
        resultMap.put("num", count);
        return resultMap;
    }
}
