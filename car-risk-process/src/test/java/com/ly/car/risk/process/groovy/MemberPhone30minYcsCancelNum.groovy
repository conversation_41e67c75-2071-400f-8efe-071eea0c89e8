package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 当前用户近30分钟内用户取消订单关联乘车人手机号数 顺风车
 * member_phone_30min_ycs_cancel_num
 */
class MemberPhone30minYcsCancelNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String memberId = (String)params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user30MinCancelOrder(memberId,"YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int)orders.stream().filter({p-> StringUtils.isNotBlank(p.getPassengerPhone())})
                .map({ p->p.getPassengerPhone()}).distinct().count();
        resultMap.put("num",count);
        return resultMap;
    }

}
