package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 当前用户近24小时同设备号完单数
 * user_24h_finish_order_same_device_num
 */
class User24hourFinishOrderSameDeviceNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String memberId = (String) params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user24hourFinishOrderSameDeviceCount(memberId, "YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }

        resultMap.put("num", orders.size());
        return resultMap;
    }

}
