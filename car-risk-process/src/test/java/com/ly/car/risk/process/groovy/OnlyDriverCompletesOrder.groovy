package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 乘客无操作、全程司机操作完单
 * only_driver_completes_order
 */
class OnlyDriverCompletesOrder {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String orderId = (String) params.get("orderId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        boolean result = bean.onlyDriverCompletesOrder(orderId);
        if(result){
            resultMap.put("num", 1);
        }else{
            resultMap.put("num", 0);
        }
        return resultMap;
    }

}
