package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 当前设备关联司机近24小时提现金额
 * driver_24h_withdraw_amount_same_device_num
 */
class Driver24hWithdrawAmountSameDeviceNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String deviceId = (String) params.get("deviceId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.driver24HourWithdrawAmountSameDevice(deviceId);
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        BigDecimal amount = new BigDecimal(0);
        for (CarRiskOrderDetail orderDetail : orders) {
            if (null != orderDetail.getAmount()) {
                amount = amount.add(orderDetail.getAmount());
            }
        }
        resultMap.put("num", amount.doubleValue());
        return resultMap;
    }

}
