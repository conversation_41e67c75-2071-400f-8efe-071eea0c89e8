package com.ly.car.risk.process.groovy


import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 真车主自动完单Tag标签
 * car_owner_auto_finish_order_tag
 */
class DriverAutoFinishOrderTag {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        int hitAutoFinishTag = 0;
        String orderId = (String) params.get("orderId");
        if (!StringUtils.isBlank(orderId)) {
            RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
            hitAutoFinishTag = bean.carOwnerAutoFinishOrderTag(orderId);
        }
        //自动完单Tag标签 1:命中  0：未命中
        resultMap.put("num", hitAutoFinishTag);
        return resultMap;
    }

}
