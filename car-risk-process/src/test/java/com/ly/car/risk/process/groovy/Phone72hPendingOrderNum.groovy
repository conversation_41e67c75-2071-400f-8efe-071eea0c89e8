package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.time.DateUtils
import org.assertj.core.util.Lists
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 *
 /**
 * 当前订单手机号3天内待补款订单数
 *
 * countPhone24SupplementaryAmountOrder
 * ycw/ycs_phone_72h_pending_order_count
 */
class Phone72hPendingOrderNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();

        String passengerPhone = (String) params.get("passengerPhone")

        Date endTime = new Date();
        Date startTime = DateUtils.addHours(endTime, -72);
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<String> productLineList = Lists.newArrayList("YCW","YCS")
        List<CarRiskOrderDetail> orders = bean.phonePendingOrderNum(passengerPhone, productLineList, startTime, endTime);

        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }

        resultMap.put("num", orders.size());
        return resultMap;
    }

}
