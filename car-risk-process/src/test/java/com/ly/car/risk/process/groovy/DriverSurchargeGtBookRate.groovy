package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.dto.order.CarOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.math.RoundingMode

/**
 * 订单附加费大于预估金额比例
 * driver_surcharge_gt_book_rate
 */
class DriverSurchargeGtBookRate {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        CarOrderDetail carNum = (CarOrderDetail) params.get("order")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.driverSurchargeGtBookRate(carNum);
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }

        CarRiskOrderDetail orderDetail = orders.get(0)
        if (orderDetail.getSurcharge() == null || orderDetail.getBookUserAmount() == null || Objects.equals(orderDetail.getBookUserAmount(), BigDecimal.ZERO)) {
            return resultMap;
        }

        BigDecimal rate = orderDetail.getSurcharge().divide(orderDetail.getBookUserAmount(), 0, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));

        resultMap.put("num", rate);
        return resultMap;
    }

}
