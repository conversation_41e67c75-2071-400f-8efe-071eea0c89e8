package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 当前近24小时司机获取补贴、奖励金额
 * driver_24h_bill_order_amount_num
 */
class Driver24hBillOrderAmountNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String carNum = (String) params.get("carNum")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.driver24hourBillOrderAmount(carNum);
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }

        BigDecimal amount = new BigDecimal(0);
        for (CarRiskOrderDetail orderDetail : orders) {
            if (null != orderDetail.getAmount()) {
                amount = amount.add(orderDetail.getAmount());
            }
        }
        resultMap.put("num", amount.doubleValue());
        return resultMap;
    }

}
