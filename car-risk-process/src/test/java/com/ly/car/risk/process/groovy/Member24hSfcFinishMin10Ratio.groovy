package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil

//package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.math.RoundingMode

/**
 * 用户24小时内预估公里数<10公里完单订单占比
 * member_24h_sfc_finish_min10_ratio
 */
class Member24hSfcFinishMin10Ratio {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String memberId = (String)params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user24HourFinishOrder(memberId,"YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int)orders.stream().filter({ p -> p.getEstimateDistance()>0 && p.getEstimateDistance() < 10000 }).count();
        Double rate = new BigDecimal(count).divide(new BigDecimal(orders.size()),2, RoundingMode.HALF_UP).doubleValue();
        resultMap.put("num", rate);
        resultMap.put("rate", rate);
        return resultMap;
    }

}
