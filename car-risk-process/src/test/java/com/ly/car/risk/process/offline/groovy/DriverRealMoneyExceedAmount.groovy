package com.ly.car.risk.process.offline.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarOfflineRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.OfflineRiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 当前订单实际金额与预估金额差值金额大于n元
 * driver_real_money_exceed_amount
 */
class DriverRealMoneyExceedAmount {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        List<String> productLineList = (List<String>) params.get("productLineList");
        List<String> channels = (List<String>) params.get("channels");
        List<String> supplierCodeList = (List<String>) params.get("supplierCodeList");
        String startTime = (String) params.get("startTime");
        String endTime = (String) params.get("endTime");
        Double amount = 20D;

        if (null == amount) {
            return resultMap;
        }
        OfflineRiskMetricsService bean = (OfflineRiskMetricsService) SpringContextUtil.getBean("offlineRiskMetricsService");
        List<CarOfflineRiskOrderDetail> orders = bean.driverRealMoneyExceedAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        resultMap.put("orders", orders);
        return resultMap;
    }

}
