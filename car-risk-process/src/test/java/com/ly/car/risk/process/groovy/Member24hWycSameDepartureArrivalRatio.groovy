package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger

//package com.ly.car.risk.process.groovy

import org.slf4j.LoggerFactory

import java.math.RoundingMode
import java.util.stream.Collectors

/**
 * 用户24小时内出发地与目的地一致（起止地相同）订单占比
 * member_24h_wyc_sameDepartureArrival_ratio
 */
class Member24hWycSameDepartureArrivalRatio {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String memberId = (String) params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user24HourTotalOrder(memberId,"YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int size = 0;
        Map<String, List<CarRiskOrderDetail>> orderMap = orders.stream().collect(Collectors.groupingBy({ p -> p.getDepartureAddress() + p.getArrivalAddress() }));
        for (Map.Entry<String, List<CarRiskOrderDetail>> entry : orderMap.entrySet()) {
            int sameAddressSize = entry.value.size()
            if(sameAddressSize > 1){
                size = size + sameAddressSize;
            }
        }
        Double rate = new BigDecimal(size).divide(new BigDecimal(orders.size()), 2, RoundingMode.HALF_UP).doubleValue();

        resultMap.put("num", rate);
        resultMap.put("rate", rate);
        return resultMap;
    }

}
