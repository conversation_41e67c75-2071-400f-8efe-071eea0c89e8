package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil

//package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 24小时内司机完单金额少于10元的订单数
 * wyc_dirver_24h_min10_order_num
 */
class WycDriver24hMin10OrderNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String carNum = (String)params.get("carNum")
        if(StringUtils.isBlank(carNum)){
            return resultMap;
        }
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.car24HourFinishOrder(carNum,"YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int)orders.stream().filter({ p -> p.getAmount().compareTo(new BigDecimal(10)) < 0 }).count();
        resultMap.put("num",count);
        return resultMap;
    }

}
