package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import com.ly.dsf.utils.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

//package com.ly.car.risk.process.groovy

/**
 * 当前用户近30分钟内订单状态为司机已接单订单数 顺风车
 * member_car_30min_ycs_order_num
 */
class MemberCar30minYcsOrderNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String memberId = (String)params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user30MinTotalOrder(memberId,"YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int)orders.stream().filter({p-> StringUtils.isNotBlank(p.getCarNum())}).count();
        resultMap.put("num",count);
        return resultMap;
    }

}
