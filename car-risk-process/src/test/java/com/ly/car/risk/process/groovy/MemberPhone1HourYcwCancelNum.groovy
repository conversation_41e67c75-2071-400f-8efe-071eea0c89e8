package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

//package com.ly.car.risk.process.groovy

/**
 * 当前用户近1小时内用户取消订单关联乘车人手机号数 网约车
 * member_phone_1h_ycw_cancel_num
 */
class MemberPhone1HourYcwCancelNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String memberId = (String)params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user1HourCancelOrder(memberId,"YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        Map<String, List<CarRiskOrderDetail>> orderMap = orders.stream()
                .filter({ p -> StringUtils.isNotBlank(p.getPassengerPhone())})
                .collect(Collectors.groupingBy({ p -> p.getPassengerPhone() }));
        int maxCount = 0;
        for (Map.Entry<String, List<CarRiskOrderDetail>> entry : orderMap.entrySet()) {
            if (entry.value.size() > maxCount) {
                maxCount = entry.value.size();
            }
        }
        resultMap.put("num", maxCount);
        return resultMap;
    }

}
