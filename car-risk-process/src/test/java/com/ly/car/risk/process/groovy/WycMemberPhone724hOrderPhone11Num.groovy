package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors


/**
 * 网约车24小时内同用户同手机号号码段（前7位）一致下单手机号去重数量
 * wyc_memberid_phone7_24h_order_phone11_num
 */
class WycMemberPhone724hOrderPhone11Num {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String memberId = (String)params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user24HourTotalOrder(memberId,"YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        Map<String, List<CarRiskOrderDetail>> orderMap = orders.stream()
                .filter({ p -> StringUtils.isNotBlank(p.getPassengerPhone()) && p.getPassengerPhone().length() >= 7 })
                .collect(Collectors.groupingBy({ p -> p.getPassengerPhone().substring(0,7) }));
        List<CarRiskOrderDetail> samePhone7Orders = new ArrayList<>();
        for (Map.Entry<String, List<CarRiskOrderDetail>> entry : orderMap.entrySet()) {
            if (entry.value.size() > 1) {
                samePhone7Orders.addAll(entry.getValue());
            }
        }
        int num = (int)samePhone7Orders.stream().map({p->p.getPassengerPhone()}).distinct().count();
        resultMap.put("num", num);
        return resultMap;
    }

}
