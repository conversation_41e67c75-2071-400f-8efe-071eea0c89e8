package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 24小时内同一乘客连续三次支付同一司机
 * same_passenger_payment_same_driver_thrice_within_24_hour
 */
class SamePassengerPaymentSameDriverThriceWithin24Hour {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String orderId = (String) params.get("orderId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        boolean result = bean.samePassengerPaymentSameDriverThriceWithin24Hour(orderId);
        if(result){
            resultMap.put("num", 1);
        }else{
            resultMap.put("num", 0);
        }
        return resultMap;
    }

}
