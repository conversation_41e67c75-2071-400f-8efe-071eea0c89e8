package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService

//package com.ly.car.risk.process.groovy

import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 顺风车用户近24小时内创单数
 * sfc_member_24h_create_order_num
 */
class SfcMember24hCreateOrderNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String memberId = (String)params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user24HourTotalOrder(memberId,"YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        resultMap.put("num",orders.size());
        return resultMap;
    }

}
