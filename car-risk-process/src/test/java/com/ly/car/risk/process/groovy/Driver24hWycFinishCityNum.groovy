package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 单个司机24小时内完单累计出发城市数
 * driver_24h_wyc_finish_city_num
 */
class Driver24hWycFinishCityNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String carNum = (String)params.get("carNum");
        if(StringUtils.isBlank(carNum)){
            return resultMap;
        }
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.car24HourFinishOrder(carNum,"YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int)orders.stream().map({ p -> p.getDepartureCityCode() }).distinct().count();
        resultMap.put("num",count);
        return resultMap;
    }

}
