package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.SfcRiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 订单完单预估时长大于30min，且实际时长是预估时长的200%
 * order_duration_exception
 */
class Hour24DriverOrderAmountGraterThan100CountGraterThan1 {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String orderId = (String) params.get("orderId")
        SfcRiskMetricsService bean = (SfcRiskMetricsService) SpringContextUtil.getBean("sfcRiskMetricsService");
        boolean result = bean.hour24DriverOrderAmountGraterThan100CountGraterThan1(orderId);
        if(result){
            resultMap.put("num", 1);
        }else{
            resultMap.put("num", 0);
        }
        return resultMap;
    }

}
