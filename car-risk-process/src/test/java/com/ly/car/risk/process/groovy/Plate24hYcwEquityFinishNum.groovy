package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

/**
 * 当前司机24小时命中权益完单次数
 * plate_24h_ycw_equity_finish_num
 */
class Plate24hYcwEquityFinishNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String carNum = (String) params.get("carNum")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.car24HourFinishRightsOrder(carNum, "YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        resultMap.put("num", orders.size());
        return resultMap;
    }

}
