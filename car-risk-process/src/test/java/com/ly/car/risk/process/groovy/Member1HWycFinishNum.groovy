package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil

//package com.ly.car.risk.process.groovy

import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 单用户近一小时内完单数量
 * member_1h_wyc_finish_num
 */
class Member1HWycFinishNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String,Object> check(Map<String, Object> params) {
        Map<String,Object> resultMap = new HashMap<>();
        String memberId = (String)params.get("memberId")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.user1HourFinishOrder(memberId,"YCW");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        resultMap.put("num",orders.size());
        return resultMap;
    }

}
