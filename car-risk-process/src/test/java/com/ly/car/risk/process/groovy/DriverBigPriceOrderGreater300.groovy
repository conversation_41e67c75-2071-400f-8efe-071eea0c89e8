package com.ly.car.risk.process.groovy


import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 完单订单金额过大(大于300)
 * car_finish_order_big_price_greater_300
 */
class DriverBigPriceOrderGreater300 {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        BigDecimal amount = BigDecimal.ZERO;
        String orderId = (String) params.get("orderId");
        if (!StringUtils.isBlank(orderId)) {
            RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
            amount = bean.singleOrderAmountGreater300(orderId);
        }
        //订单金额
        resultMap.put("num", amount);
        return resultMap;
    }

}
