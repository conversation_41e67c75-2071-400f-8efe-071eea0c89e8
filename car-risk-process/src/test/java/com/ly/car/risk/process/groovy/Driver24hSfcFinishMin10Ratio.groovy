import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderDetailMapper
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import com.ly.car.risk.process.utils.LoggerUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.math.RoundingMode


/**
 * 司机24小时内预估公里数<10公里完单订单占比
 * driver_24h_sfc_finish_min10_ratio
 */
class Driver24hSfcFinishMin10Ratio {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String carNum = (String) params.get("carNum");
        if(StringUtils.isBlank(carNum)){
            return resultMap;
        }
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.car24HourFinishOrder(carNum,"YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        int count = (int)orders.stream().filter({ p -> p.getEstimateDistance()>0 && p.getEstimateDistance() < 10000 }).count();
        Double rate = new BigDecimal(count).divide(new BigDecimal(orders.size()),2, RoundingMode.HALF_UP).doubleValue();
        resultMap.put("num", rate);
        resultMap.put("rate", rate);
        return resultMap;
    }

}
