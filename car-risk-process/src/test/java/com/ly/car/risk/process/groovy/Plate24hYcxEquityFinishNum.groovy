package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
/**
 * 当前司机24小时命中权益完单次数
 * plate_24h_ycx_equity_finish_num
 */
class Plate24hYcxEquityFinishNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String carNum = (String) params.get("carNum")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.car24HourFinishRightsOrder(carNum, "YCX");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }
        resultMap.put("num", orders.size());
        return resultMap;
    }

}
