package com.ly.car.risk.process.groovy


import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 完单订单-预估30分钟以上&&实际运行时间小于预估时间50%以上
 * order_estimate_greater_30m_realtime_lower_50_percent
 */
class DriverFinishOrderRealTimeLowerPercentNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String orderId = (String) params.get("orderId");
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        boolean result = bean.orderEstimateGreater30FinishBelow50Percent(orderId);
        //满足条件：1，不满足：0
        if (result) {
            resultMap.put("num", 1);
        } else {
            resultMap.put("num", 0);
        }
        return resultMap;
    }

}
