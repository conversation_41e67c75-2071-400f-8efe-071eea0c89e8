package com.ly.car.risk.process.groovy

import cn.hutool.core.collection.CollUtil
import com.ly.car.risk.process.component.SpringContextUtil
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 萌艇当前司机完单小于6分钟的订单数
 * driver_24h_finish_less_6min_num
 */
class Driver24hFinishLess6minNum {

    Logger logger = LoggerFactory.getLogger(getClass());

    Map<String, Object> check(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        String carNum = (String) params.get("carNum")
        RiskMetricsService bean = (RiskMetricsService) SpringContextUtil.getBean("riskMetricsService");
        List<CarRiskOrderDetail> orders = bean.driver24hourFinishOrderLess6minCount(carNum, "YCS");
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }

        resultMap.put("num", orders.size());
        return resultMap;
    }

}
