<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.car.risk.process.repo.risk.mapper.RiskHitMapper">

    <select id="queryLatestFreezeEndTimeByOrderId" resultType="java.util.Date">
        SELECT freeze_end_time
        FROM risk_hit
        WHERE order_id = #{orderId}
          AND freeze_end_time IS NOT NULL
        ORDER BY freeze_end_time DESC, create_time DESC
        LIMIT 1
    </select>

</mapper>
