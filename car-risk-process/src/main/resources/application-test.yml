config:
  data-sources:
    TCTravelMktCarRiskMetrics:
      alias: riskMetrics
  kafka:
    sz-server: kafka-test-d.kafka.svc.datacloudqa.17usoft.com:9092
    channel_badDebts_consumer: false
    dis-offline-consumer: false
    finish-driver-consumer: false
    index-sync: false
  urls:
    driverBlack: http://tourwirelessapi.t.17usoft.com/traffic/api/risk/add/driver/black/
    driverBlackNum: http://tourwirelessapi.t.17usoft.com/traffic/api/risk/driver/black/times/
    driverBlackJudge: http://tourwirelessapi.t.17usoft.com/traffic/api/risk/driver/black/type/
    pointQueryUrl: http://tourwirelessapi.t.17usoft.com/traffic/api/map/get/track/
    #会员公共接口
    memberUrl: http://mkcloud.17usoft.com/membermain/
    sendMail: http://tccommon.17usoft.com/generalPlatformGateway/
    marketingRisk: http://10.172.21.192:8080
    #大数据turboMq集群
    trainMqServer: mqnameserver.17usoft.com:9877;mqnameserverbak.17usoft.com:9877
    trainMqServerStart: false
    #大数据turboMq集群
    bigDataMqServer: mqnameserver.17usoft.com:9877;mqnameserverbak.17usoft.com:9877
    bigDataMqServerStart: false
    activityConsumer: false
    #苏州公共业务集群
    publicMqServer: mqnameserver.qa.17usoft.com:9876
    # 天冕
    queryRiskUrl: https://rskprd-fat.tianmiantech.com/dust/api/v1/verify/thirdVerifyInfo
    # 顺风车
    sfcUrl: http://tcwireless.t.17usoft.com/car_order/
    driverLocation: http://servicegw.t.ly.com/gateway/car.service/stage/resource/driverlocation/
    #背景审查
    driverReview: http://bds.t.17usoft.com/tcodpapi/driver/review
    commonUrl: http://tourwirelessapi.t.17usoft.com
    #外呼
    callApi: http://genesys.cct.17usoft.com/api/AutoCallApi/SendCall
    callApiRecord: http://genesys.cct.17usoft.com/api/AutoCallApi/GetCallRecord
    phoneValidUrl: http://mkcloud.17usoft.com/markettiantingapi/vacantNumber/valid
    assistServiceDsfName: dsf.flight.marketing.assistservice
    assistServiceVersion: 0.2.2.2
    orderServiceDsfName: dsf.car.shared.mobility.supply.order.core
    orderServiceVersion: 1.0.0.0
    carOrderServiceDsfName: dsf.car.order.service
    carOrderServiceVersion: 0.0.0.1
    cityClientUrl: http://servicegw.qa.ly.com/gateway/flight.configcore/qa/baseresource/findcities
    cityClientToken: 2a2e5a37-cd80-483b-bd77-cccbe59e56ae
    workOrderSaveUrl: http://tcwlservice.t.17usoft.com/workorder/managerapi/service/saveOrder
    pushServiceDsfName: dsf.travelsystem.sms.platform.core
    pushServiceDsfVersion: 0.0.0.5
    safetyYcScene: 安全中心-安全联系人用车提醒
    safetyYcDomain: USECAR_SECURITY
    virtualPhoneCallDsfName: dsf.car.base.service.virtualphone
    virtualPhoneCallDsfVersion: 0.4.0.0
    sfcCancelDutyAssignUrl: http://tourwirelessapi.t.17usoft.com/spiderman/api/risk/responsibility/judge/
    yncCancelDutyAssignUrl: http://bds.t.17usoft.com/tideserver/recommend/core/carreject
    yncCancelDutyAssignToken: e1808dfd47e342338edce4762a4a6e10
    carTradeServiceDsfName: dsf.car.trade.core
    carTradeServiceVersion: 0.0.0.1
    imMsgQueryUrl: https://wx.t.17u.cn/car-inter-h5/car_im/sfcImMsg/queryMsgRecord
    shortLinkTransformUrl: http://sapi.17usoft.com/tcsa-api/services/wsc/create
    wechatOrderDetailUrl: https://wx.qa.17u.cn/carfe/wechat/shareDetail?orderSerialNo=%s
    appOrderDetailUrl: https://wx.qa.17u.cn/carfe/app/shareDetail?orderSerialNo=%s
    madaOrderDetailUrl: https://bcxzp2.jgshare.cn/AaRY?orderSerialNo=%s&refId=
    hellobikeBaseUrl: https://fat-hello-openapi.hellobike.com/openapi
    hellobikeAppKey: tytx-T8odQR1X
    hellobikeSecret: c17bdf6e53204cc9969a904f2339f363
    carOrderCoreDsfName: dsf.car.order.core
    carOrderCoreVersion: 0.2.1.3
    driverLocationTrackUrl: http://servicegw.qa.ly.com/gateway/car.mobility.supply.order/qa/order/driverlocationtrack/
    driverLocationTrackToken: 06e54110-f6c3-4a54-8f44-378365ab0617
    furionTagQueryUrl: http://bds.17usoft.com/furionserver/tag/context
    supplierUrl: https://tcwireless.qa.17usoft.com/supply/basedata/basedata/listSupplier
  turbo-mq:
    orderStateChangeTopic: car_trade_topic_order_state_change
    orderStateChangeConsumerGroup: risk_car_order_state_change_consumer_group_qa
    bigDataOrderSyncTopic: travel_hitchhiking_create_order_member_topic
    bigDataOrderSyncConsumerGroup: risk_car_big_data_order_sync_consumer_group_qa
    virtualCallSyncSwitch: true
    virtualCallSyncTopic: apprd_pnpv2_topic_secret_report_delay
    virtualCallConsumerGroup: virtual_call_sync_consumer_delay_group_test
  lbs:
    navigateCostUrl: http://inner-api.travel.qa.17usoft.com/car/lbs/baseapi/v1/location/driver/cost
    appKey: