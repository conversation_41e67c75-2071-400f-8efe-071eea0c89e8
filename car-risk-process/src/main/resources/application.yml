###
server:
  port: ${PORT0:8080}
  servlet:
    context-path: /car_risk_process

###
spring:
  profiles:
    active: @profile.name@
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  devtools:
    restart:
      enabled: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
logging:
  config: classpath:logback-spring.xml

###
config:
  data-sources:
    TECarRisk:
      alias: risk
    TECarShardingOrder:
      alias: shardingOrder
    TCInternalCarOrder:
      alias: order
    DCDB_TCInternalCarOrder:
      alias: dcdbOrder
    TCInternalCarResource:
      alias: resource
    TEWirelessTraffic:
      alias: traffic
    TEHitchOrder:
      alias: hitchOrder
    TECarSaasTicket:
      alias: mtTicket
    DCDB_TECarResource:
      alias: dcdbResource
    TCTravelCarRisk:
      alias: dcdbCarRisk
  monitor:
    checkMode: INVOKE_API
  kafka:
    group-name: car_risk_process_consumer_group
    dis-offline-topic: car_distribution_offline_topic
    channel_badDebts_topic: car_channel_badDebts_risk_topic
    finish_driver_topic: car_finish_driver_risk_topic
    index_sync_topic: car_risk_index_sync_topic
  click-house:
    insertUrl: ************:17133
    searchUrl: ************:17133
    user: car_monitor_admin
    pwd: car_monitor_admin
send:
  mail: http://tccommon.17usoft.com/generalPlatformGateway/
