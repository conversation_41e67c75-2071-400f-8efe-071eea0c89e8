package com.ly.car.risk.process.supplier.Tsan;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.rsp.SupplierSyncRsp;
import com.ly.car.risk.process.repo.risk.mapper.entity.TcSupplierWorkOrder;
import com.ly.car.risk.process.supplier.SupplierClient;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import java.math.BigDecimal;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SupplierT3Client implements SupplierClient {

    @Value("${spring.profiles.active}")
    private String env;

    /**
     * 违规信息同步
     * */
    public JSONObject violationSync(T3ViolationSyncParam param){
        String url = TsanEnv.get(env).host + T3ApiUrlConstant.VIOLATION_SYNC.url;
        param.setChannel(TsanEnv.get(env).channel);
        param.setTimestamp(System.currentTimeMillis());
        param.setSign(T3SignAdapter.signAdapter(param,TsanEnv.get(env).token));
        String result = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(param), null, 500L);
        return JSONObject.parseObject(result);
    }


    /**申诉结果同步*/
    public JSONObject violationResultSync(T3ViolationResultSyncParam param){
        String url = TsanEnv.get(env).host + T3ApiUrlConstant.VIOLATION_APPEAL_RESULT_SYNC.url;
        param.setChannel(TsanEnv.get(env).channel);
        param.setTimestamp(System.currentTimeMillis());
        param.setSign(T3SignAdapter.signAdapter(param,TsanEnv.get(env).token));
        String result = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(param), null, 500L);
        return JSONObject.parseObject(result);
    }
//
//    /**工单信息同步*/
//    public JSONObject workOrderSync(T3WorkOrderParam param){
//
//        return JSONObject.parseObject(result);
//    }

    @Override
    public SupplierSyncRsp syncWorkerOrder(TcSupplierWorkOrder tcSupplierWorkOrder) {
        SupplierSyncRsp rsp = new SupplierSyncRsp();
        T3WorkOrderParam param = new T3WorkOrderParam();
        param.setOperateType(0);
        param.setPlatformOrderNo(tcSupplierWorkOrder.getSupplierOrderId());
        param.setPlatformWorkOrderNo("");
        param.setCpWorkOrderNo(tcSupplierWorkOrder.getCpWorkOrderNo());
        Integer code = Objects.equals(tcSupplierWorkOrder.getWoCateCode(), 2) ? 3 : tcSupplierWorkOrder.getWoCateCode();
        param.setWoCateCode(code + "");
        param.setEventTime(tcSupplierWorkOrder.getEventTime().getTime());
        param.setMsg(tcSupplierWorkOrder.getMsg());
        T3WorkOrderParam.JudgeInfo judgeInfo = new T3WorkOrderParam.JudgeInfo();
        judgeInfo.setUnionId(tcSupplierWorkOrder.getUnionId());
        judgeInfo.setJudgeType(6);
//            judgeInfo.setReason();
        T3WorkOrderParam.PriceChangeDetail priceChangeDetail = new T3WorkOrderParam.PriceChangeDetail();
        priceChangeDetail.setChangeFee(String.valueOf(tcSupplierWorkOrder.getChangeFee().multiply(new BigDecimal(100)).setScale(0)));
        priceChangeDetail.setChangeOrderFee("0");
        priceChangeDetail.setChangeAttachFee("0");
        priceChangeDetail.setChangeCrossCityFee("0");
        priceChangeDetail.setChangeFestivalFee("0");
        priceChangeDetail.setChangeParkingFee("0");
        priceChangeDetail.setChangeHighwayFee("0");
        priceChangeDetail.setChangeRoadBrigeFee("0");
        priceChangeDetail.setChangeOtherFee("0");
        judgeInfo.setPriceChangeDetail(priceChangeDetail);
        param.setJudgeInfo(judgeInfo);
        String url = TsanEnv.get(env).host + T3ApiUrlConstant.WORK_ORDER_RESULT_SYNC.url;
        param.setChannel(TsanEnv.get(env).channel);
        param.setTimestamp(System.currentTimeMillis());
        param.setSign(T3SignAdapter.signAdapter(param,TsanEnv.get(env).token));
        LoggerUtils.info(log,"T3工单同步请求{},请求地址{}",JsonUtils.json(param),url);
        String result = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(param), null, 500L);
        LoggerUtils.info(log,"T3工单同步返回{}",result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if(jsonObject != null && jsonObject.getInteger("code") == 10000 && jsonObject.getJSONObject("data") != null){
            String platformWorkOrderNo = jsonObject.getJSONObject("data").getString("platformWorkOrderNo");
            rsp.setSuccess(true);
            rsp.setData(platformWorkOrderNo);
            return rsp;
        }
        return null;
    }
}
