package com.ly.car.risk.process.service.groovy;

import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskField;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskScene;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskStrategy;

import java.util.List;
import java.util.Map;

/**
 * 风控引擎计算 core
 * */
public interface RiskEngine {

    void executeCustomer(RiskScene riskScene, Map<String,String> data, Map<String,List<HitStrategyDTO>> hitInfo);

    /**
     * 特征/抽象指标的计算提取
     * */
    void executeAbstraction(RiskScene riskScene, Map<String,String> data, Map<String,Object> returnMap, List<RiskField> riskFields);


    /**
     * 规则计算
     * */
    void executeRule(List<RiskStrategy> strategyList, Map<String,Object> data,Map<String,List<HitStrategyDTO>> hitInfo,List<RiskField> riskFields);

    /**
     * 特殊规则实现
     * */
    void executeSpecialRule();
}
