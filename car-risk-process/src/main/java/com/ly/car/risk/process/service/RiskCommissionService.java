package com.ly.car.risk.process.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.ly.car.common.bean.constant.PlatformOrderConstants;
import com.ly.car.risk.entity.DistributionOfflineRisk;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.process.client.SupplierApiClient;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.api.ListByMemberIdParam;
import com.ly.car.risk.process.api.MemberQueryParam;
import com.ly.car.risk.process.api.dto.MemberData;
import com.ly.car.risk.process.bean.BillDateConverter;
import com.ly.car.risk.process.api.MemberApi;
import com.ly.car.risk.process.api.rsp.MemberQueryResponse;
import com.ly.car.risk.process.repo.risk.mapper.DistributionOfflineRiskMapper;
import com.ly.car.risk.process.repo.risk.mapper.DistributionRiskManageMapper;
import com.ly.car.risk.process.service.dto.CommissionDto;
import com.ly.car.risk.process.service.dto.RiskRsp;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.entity.OrderPrice;
import com.ly.car.sharding.order.entity.OrderSupplierBill;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.sharding.order.mapper.OrderPriceMapper;
import com.ly.car.sharding.order.mapper.OrderSupplierBillMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskCommissionService {

    @Resource
    private DistributionOfflineRiskMapper distributionOfflineRiskMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private DistributionRiskManageMapper distributionRiskManageMapper;
    @Resource
    private OrderPriceMapper        orderPriceMapper;
    @Resource
    private SupplierApiClient       supplierApiClient;
    @Resource
    private OrderSupplierBillMapper orderSupplierBillMapper;
    @Resource
    private MemberApi memberApi;
    @Resource
    private RiskOrderManageService riskOrderManageService;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Value("${send.mail}")
    private String sendMailUrl;

    public void action(List<String> orderId){
        List<DistributionOfflineRisk> offlineRisks = new ArrayList<>();
        if(orderId == null || orderId.size() == 0){
            //当天查询前一天所有的司推乘订单
            String startDate = DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD) + " 00:00:00";
            String endDate = DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD) + " 23:59:59";
            offlineRisks = distributionOfflineRiskMapper.selectList(
                    new QueryWrapper<DistributionOfflineRisk>().between("create_time",startDate,endDate)
            );
        } else {
            offlineRisks = distributionOfflineRiskMapper.selectList(
                    new QueryWrapper<DistributionOfflineRisk>().in("order_id",orderId)
            );
        }

        if(offlineRisks == null || offlineRisks.size() == 0){
            log.info("[RiskCommissionService] [action] [][]当前无订单需要风控");
            return;
        }

        //转map
        Map<String,DistributionOfflineRisk> allOfflineRiskMap = offlineRisks.stream().collect(Collectors.toMap(DistributionOfflineRisk::getOrderId,v->v,(old, cur)->old));
        Map<String,DistributionOfflineRisk> offlineRiskMap = new HashMap<>();
        //获取所有设备且是新客订单
        List<String> deviceIds = offlineRisks.stream()
                .filter(risk -> StringUtils.isNotBlank(risk.getDeviceId()))
                .filter(risk -> risk.getIsNewOrder() == 1)
                .map(DistributionOfflineRisk::getDeviceId)
                .collect(Collectors.toList());

        //查询订单信息
        List<String> orderIds = offlineRisks.stream().map(DistributionOfflineRisk::getOrderId).collect(Collectors.toList());
        List<OrderInfo> allOrder = orderInfoMapper.findByOrderIds(orderIds);
        //过滤完单的
        Map<String,OrderInfo> orderMap = allOrder.stream().filter(e->e.getStatus() == 300 || e.getStatus() == 400)
                .collect(Collectors.toMap(OrderInfo::getOrderId, v->v,(old, cur)->old));

        //过滤嘀嗒出租车
        offlineRisks = offlineRisks.stream().filter(e->orderMap.get(e.getOrderId()) != null)
                .filter(e->StringUtils.isNotBlank(orderMap.get(e.getOrderId()).getSupplierCode()) && !orderMap.get(e.getOrderId()).getSupplierCode().startsWith("DiDaTaxi"))
                .filter(e->orderMap.get(e.getOrderId()) != null)
                .collect(Collectors.toList());

        //移除嘀嗒的
        for(Map.Entry<String, DistributionOfflineRisk> entry:allOfflineRiskMap.entrySet()){
            OrderInfo orderInfo = orderMap.get(entry.getKey());
            if(!(orderInfo != null && StringUtils.isNotBlank(orderInfo.getSupplierCode()) && orderInfo.getSupplierCode().startsWith("DiDaTaxi"))){
                offlineRiskMap.put(entry.getKey(), entry.getValue());
            }
        }

        //查询地址信息
        List<OrderAddress> orderAddressList = orderAddressMapper.findByOrderIds(orderIds);
        Map<String,OrderAddress> addressMap = orderAddressList.stream().collect(Collectors.toMap(OrderAddress::getOrderId,v->v,(old, cur)->old));

        //查询价格信息
        List<OrderPrice> orderPrices = orderPriceMapper.findByOrderIds(orderIds);
        Map<String,OrderPrice> priceMap = orderPrices.stream().collect(Collectors.toMap(OrderPrice::getOrderId,v->v,(old, cur)->old));

        //查询供应商单据信息
        List<OrderSupplierBill> orderSupplierBills = orderSupplierBillMapper.findByOrderIds(orderIds);
        Map<String,OrderSupplierBill> supplierBillMap = orderSupplierBills.stream().collect(Collectors.toMap(OrderSupplierBill::getOrderId,v->v,(old,cur)->old));

        //获取统一配置的信息
        int newUserFinish = 3;//新客完单数
        BigDecimal payAmount = new BigDecimal("1");//订单金额
        BigDecimal finishRate = new BigDecimal("0.5");//完单比例
        int orderTime = 5;//订单行程时长
        BigDecimal newUserRate = new BigDecimal("0.5");//新客占比
        BigDecimal actualKilo = new BigDecimal("2");//实际里程
        int endAddressNum = 5000;//重点5公里范围
        int finishNum = 3;//订单数
        String workId = "";

        int payLinkNum = 10;//邀请用户完单大于等于10单
        BigDecimal actualKiloRate = new BigDecimal("0.5");
        BigDecimal integerNumRate = new BigDecimal("0.8");
        BigDecimal finishRate011 = new BigDecimal("0.5");
        int finishNum011 = 10;
        int finishNum009 = 10;
        try {
            String offLineConfig= ConfigCenterClient.get("driver_to_user_risk");
            if(StringUtils.isNotBlank(offLineConfig)){
                JSONObject jsonObject = JSONObject.parseObject(offLineConfig);
                newUserFinish = jsonObject.getInteger("newUserFinish");
                payAmount = jsonObject.getBigDecimal("payAmount");
                finishRate = jsonObject.getBigDecimal("finishRate");
                orderTime = jsonObject.getInteger("orderTime");
                newUserRate = jsonObject.getBigDecimal("newUserRate");
                actualKilo = jsonObject.getBigDecimal("actualKilo");
                endAddressNum = jsonObject.getInteger("endAddressNum");
                finishNum = jsonObject.getInteger("driverSameOrder");
                workId = jsonObject.getString("workId");
                payLinkNum = jsonObject.getInteger("payLinkNum");

                actualKiloRate = jsonObject.getBigDecimal("actualKiloRate");
                integerNumRate = jsonObject.getBigDecimal("integerNumRate");
                finishRate011 = jsonObject.getBigDecimal("finishRate011");
                finishNum011 = jsonObject.getInteger("finishNum011");
                finishNum009 = jsonObject.getInteger("finishNum009");

            }
        }catch (Exception ex){
            log.error("[userRisk] [error] [{}] [{}] 获取司推乘信息异常：{}",  ex);
        }

        //先给个需要插入的list
        Map<String, DistributionRiskManage> manageMap = convertManage(offlineRisks,addressMap,orderMap);
        //第一条策略
        Map<String,List<DistributionOfflineRisk>> deviceMap = new HashMap<>();
        //第二条策略
        Map<String,List<DistributionOfflineRisk>> disUserMap = new HashMap<>();
        //第五条策略
        Map<String,List<DistributionOfflineRisk>> driverMap = new HashMap<>();
        //第六条策略，支付账号
        Map<String,List<DistributionOfflineRisk>> payAccountMap = new HashMap<>();
        //第10条策略，当前订单关联用户命中风险标签（机器行为、聚集小号）
        Map<String,DistributionOfflineRisk> riskCenterMap = new HashMap<>();
        //转换不同维度map
        offlineRisks.stream().forEach(risk -> {
            convertMap(risk,deviceMap,disUserMap,driverMap,payAccountMap,riskCenterMap);
        });
        //以设备维度来看当前
        setManageByDivice(deviceMap,newUserFinish,addressMap,manageMap);
        //先以分销员的维度来判断是否命中规则
        setManageByDistribute(disUserMap,orderMap,addressMap,manageMap,payAmount,finishRate011
                            ,orderTime,actualKilo,newUserRate,supplierBillMap,finishNum011,finishNum009,integerNumRate,actualKiloRate);
        //再以司机维度区分，再以分销员区分
        setManageByDriver(driverMap,orderMap,addressMap,manageMap,endAddressNum,finishNum);
        //以支付账号维度区分
        setManageByAccount(payAccountMap,payLinkNum,manageMap);
        //风险标签
//        setManageByRiskTag(manageMap,riskCenterMap);

        //最后将有命中规则的订单存入库中
        List<DistributionRiskManage> hitOrderAll = new ArrayList<>();
        for(Map.Entry<String, DistributionRiskManage> entry:manageMap.entrySet()){
            if(StringUtils.isNotBlank(entry.getValue().getRuleNoList())){
                if(StringUtils.isNotBlank(entry.getValue().getLinkOrder())){
                    List<String> strList = Arrays.asList(entry.getValue().getLinkOrder().split(","));
                    Set<String> noRepeat = new HashSet<>(strList);
                    strList = new ArrayList<>(noRepeat);
                    if(strList.size() > 30){
                        log.info("[RiskCommissionService] [action] [][]当前订单为:"+entry.getKey()+"关联订单为:"+JsonUtils.json(entry.getValue()));
                    }
                    String str = StringUtils.join(strList,",");
                    if(str.length() > 1000){
                        entry.getValue().setLinkOrder(str.substring(0,1000));
                    } else {
                        entry.getValue().setLinkOrder(str);
                    }

                }

                MemberQueryResponse infoByMemberId = getInfoByMemberId(entry.getValue().getMemberId());
                if(infoByMemberId != null && infoByMemberId.getData() != null){
                    entry.getValue().setUserPhone(infoByMemberId.getData().getMobile());
                }

                //插入
//                distributionRiskManageMapper.insert(entry.getValue());
                distributionRiskManageService.addByRule(entry.getValue().getOrderId(),entry.getValue().getRuleNoList(),3,1,
                        entry.getValue().getLinkOrder(),entry.getValue(),entry.getValue().getRiskLevel());
                RiskOrderManage orderManage = new RiskOrderManage();
                orderManage.setOrderId(entry.getValue().getOrderId());
                orderManage.setIsRisk(1);
                orderManage.setRuleNo(entry.getValue().getRuleNoList());
                orderManage.setRiskTypeName("专车场景");
                riskOrderManageService.addRiskOrder(orderManage);
                hitOrderAll.add(entry.getValue());
            }
        }

        //开始转换数据
        List<RiskRsp> riskRsps = new ArrayList<>();
        hitOrderAll.stream().forEach(hitOrder -> {
            RiskRsp riskRsp = new RiskRsp();
            riskRsp.setOrderId(hitOrder.getOrderId());
            riskRsp.setDecisionDime(DateUtil.date2String(orderMap.get(hitOrder.getOrderId()).getDecisionTime()));
            riskRsp.setFinishTime(DateUtil.date2String(orderMap.get(hitOrder.getOrderId()).getFinishTime()));
            riskRsp.setStartAddress(addressMap.get(hitOrder.getOrderId()).getStartAddress());
            riskRsp.setEndAddress(addressMap.get(hitOrder.getOrderId()).getEndAddress());
            riskRsp.setPayAmount(String.valueOf(priceMap.get(hitOrder.getOrderId()).getRealityAmount()));
            riskRsp.setActualKilo(String.valueOf(supplierBillMap.get(hitOrder.getOrderId()).getDistance()));
            riskRsp.setIsNewUser(offlineRiskMap.get(hitOrder.getOrderId()).getIsNewOrder() == 0?"老客":"新客");
            riskRsp.setDeviceId(offlineRiskMap.get(hitOrder.getOrderId()).getDeviceId());
            riskRsp.setDistributeName(offlineRiskMap.get(hitOrder.getOrderId()).getDistributeName());
            riskRsp.setSupplierName(orderMap.get(hitOrder.getOrderId()).getSupplierCodeFull());
            riskRsp.setCommissionAmount(String.valueOf(offlineRiskMap.get(hitOrder.getOrderId()).getCommissionAmount()));
            riskRsp.setStatus(PlatformOrderConstants.Status.getName(orderMap.get(hitOrder.getOrderId()).getStatus()).status);
            riskRsp.setDriverCardNo(offlineRiskMap.get(hitOrder.getOrderId()).getDriverCardNo());
            riskRsp.setDiscountAmount(String.valueOf(orderMap.get(hitOrder.getOrderId()).getDiscountAmount()));
            riskRsp.setPayId(offlineRiskMap.get(hitOrder.getOrderId()).getPayAccount());
            riskRsp.setCityName(addressMap.get(hitOrder.getOrderId()).getStartCityName());
            riskRsp.setRevenueAmount(String.valueOf(offlineRiskMap.get(hitOrder.getOrderId()).getRevenueAmount()));
            riskRsp.setChannelName(offlineRiskMap.get(hitOrder.getOrderId()).getChannelName());
            riskRsp.setRuleNoList(hitOrder.getRuleNoList());
            riskRsp.setDistributorsName(offlineRiskMap.get(hitOrder.getOrderId()).getDistributorsName());
            if(hitOrder.getCityId() == 317){
                riskRsps.add(riskRsp);
            }
        });

        //生成excel并发送邮件,这里要组合数据订单号、接单时间、完单时间、起始地、目的地、支付金额、实际里程、是否新客、
        // 设备ID、分销员、供应商、返佣金额、订单状态、司机车牌、优惠券金额、支付ID、完单城市
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOutputStream, RiskRsp.class).registerConverter(new BillDateConverter()).sheet("sheet1")
                .doWrite(riskRsps);

        long disSize = riskRsps.stream().map(RiskRsp::getDistributeName).distinct().parallel().count();

        BigDecimal commissionAmount = BigDecimal.ZERO;
        //生成佣金字节流
        CommissionDto commissionDto = getCommissionExcel();
        String emailBody = "当前命中订单数量为:"+riskRsps.size()+",关联分销员数为:"+disSize+",T+3返佣金额为："+commissionDto.getCommissionAmount();

        //直接字节流输出
        sendMail(byteArrayOutputStream,emailBody,workId,commissionDto.getByteArrayOutputStream());

    }


    /**
     * 返佣列表要再来一遍，只要回去流就好了
     * */
    public CommissionDto getCommissionExcel(){
        CommissionDto commissionDto = new CommissionDto();
        //查询前第三天返佣金额，发送邮件
        String startDate = DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD) + " 00:00:00";
        String endDate = DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD) + " 23:59:59";
        Date commissionDateStart = DateUtil.addDay(DateUtil.string2Date(startDate),-3);
        Date commissionDateEnd = DateUtil.addDay(DateUtil.string2Date(endDate),-3);
        List<DistributionRiskManage> manageList = distributionRiskManageMapper.selectList(
                new QueryWrapper<DistributionRiskManage>().between("create_time",commissionDateStart,commissionDateEnd)
        );
        //给个返佣列表的
        List<RiskRsp> riskRsps = new ArrayList<>();
        if(manageList != null && manageList.size() > 0){
            List<String> commissionOrderIds = manageList.stream().map(DistributionRiskManage::getOrderId).collect(Collectors.toList());
            List<DistributionOfflineRisk> riskList = distributionOfflineRiskMapper.selectList(
                    new QueryWrapper<DistributionOfflineRisk>().in("order_id",commissionOrderIds)
            );
            //查一下返佣的订单
            List<OrderInfo> commissionOrder = orderInfoMapper.findByOrderIds(commissionOrderIds);
            Map<String,OrderInfo> commissionOrderMap = commissionOrder.stream().collect(Collectors.toMap(OrderInfo::getOrderId,v->v,(old,cur)->old));
            //查询地址信息
            List<OrderAddress> orderAddressList = orderAddressMapper.findByOrderIds(commissionOrderIds);
            Map<String,OrderAddress> addressMap = orderAddressList.stream().collect(Collectors.toMap(OrderAddress::getOrderId,v->v,(old, cur)->old));
            //查询价格信息
            List<OrderPrice> orderPrices = orderPriceMapper.findByOrderIds(commissionOrderIds);
            Map<String,OrderPrice> priceMap = orderPrices.stream().collect(Collectors.toMap(OrderPrice::getOrderId,v->v,(old, cur)->old));
            //查询供应商单据信息
            List<OrderSupplierBill> orderSupplierBills = orderSupplierBillMapper.findByOrderIds(commissionOrderIds);
            Map<String,OrderSupplierBill> supplierBillMap = orderSupplierBills.stream().collect(Collectors.toMap(OrderSupplierBill::getOrderId,v->v,(old,cur)->old));
            //风控列表转一下
            Map<String,DistributionRiskManage> manageMap = manageList.stream().collect(Collectors.toMap(DistributionRiskManage::getOrderId,v->v,(old,cur)->old));
            riskList.stream().forEach(risk -> {
                RiskRsp rsp = new RiskRsp();
                rsp.setOrderId(risk.getOrderId());
                rsp.setCommissionAmount(String.valueOf(risk.getCommissionAmount()));
                rsp.setDecisionDime(DateUtil.date2String(commissionOrderMap.get(risk.getOrderId()).getDecisionTime()));
                rsp.setFinishTime(DateUtil.date2String(commissionOrderMap.get(risk.getOrderId()).getFinishTime()));
                rsp.setStartAddress(addressMap.get(risk.getOrderId()).getStartAddress());
                rsp.setEndAddress(addressMap.get(risk.getOrderId()).getEndAddress());
                rsp.setPayAmount(String.valueOf(priceMap.get(risk.getOrderId()).getRealityAmount()));
                rsp.setActualKilo(String.valueOf(supplierBillMap.get(risk.getOrderId()).getDistance()));
                rsp.setIsNewUser(risk.getIsNewOrder() == 0?"老客":"新客");
                rsp.setDeviceId(risk.getDeviceId());
                rsp.setDistributeName(risk.getDistributeName());
                rsp.setSupplierName(commissionOrderMap.get(risk.getOrderId()).getSupplierCodeFull());
                rsp.setCommissionAmount(String.valueOf(risk.getCommissionAmount()));
                rsp.setStatus(PlatformOrderConstants.Status.getName(commissionOrderMap.get(risk.getOrderId()).getStatus()).status);
                rsp.setDriverCardNo(risk.getDriverCardNo());
                rsp.setDiscountAmount(String.valueOf(commissionOrderMap.get(risk.getOrderId()).getDiscountAmount()));
                rsp.setPayId(risk.getPayAccount());
                rsp.setCityName(addressMap.get(risk.getOrderId()).getStartCityName());
                rsp.setRevenueAmount(String.valueOf(risk.getRevenueAmount()));
                rsp.setChannelName(risk.getChannelName());
                rsp.setRuleNoList(manageMap.get(risk.getOrderId()).getRuleNoList());
                rsp.setDistributorsName(risk.getDistributorsName());
                if(risk.getCityId() == 317){
                    riskRsps.add(rsp);
                }
                riskRsps.add(rsp);
            });
        }
        BigDecimal commissionAmount = riskRsps.stream().map(RiskRsp::getCommissionAmount)
                .map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add);
        commissionDto.setCommissionAmount(commissionAmount);
        log.info("返佣金额总和:"+commissionAmount);
        ByteArrayOutputStream commissionByteArray = new ByteArrayOutputStream();
        EasyExcel.write(commissionByteArray, RiskRsp.class).registerConverter(new BillDateConverter()).sheet("sheet1")
                .doWrite(riskRsps);
        commissionDto.setByteArrayOutputStream(commissionByteArray);
        return commissionDto;
    }


    /**
     * 邮件发送
     * */
    public void sendMail(ByteArrayOutputStream byteArrayOutputStream,String emailBody,String workId,ByteArrayOutputStream commissionByteArray){
        //生成附件字节流
        JSONObject fileJson = new JSONObject();
        fileJson.put("fileName","风控单据"+DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD));
        fileJson.put("fileSuffix","xlsx");
        fileJson.put("fileContent",getBase64Str(byteArrayOutputStream));

        JSONObject fileJson2 = new JSONObject();
        fileJson2.put("fileName","返佣订单"+DateUtil.date2String(DateUtil.addDay(new Date(),-3),DateUtil.DATE_PATTERN_YYYY_MM_DD));
        fileJson2.put("fileSuffix","xlsx");
        fileJson2.put("fileContent",getBase64Str(commissionByteArray));
        JSONArray array = new JSONArray();
        array.add(fileJson);
        array.add(fileJson2);

        //头部
        Map<String,String> headerMap = new HashMap<>();
        headerMap.put("Service-Name","interior-email");
        headerMap.put("Method-Name","sendByWorkId");
        headerMap.put("Content-Type","application/json");
        headerMap.put("Generalplatform-Token","247aa5234fb54426a1739907c4ec8251");

        //主题数据
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("account","tcwireless.quick.car");
        jsonObject.put("password","");
        jsonObject.put("workId",workId);
        jsonObject.put("attachments",array);
        jsonObject.put("templateId","");

        //邮件主题数据
        JSONObject paramJson = new JSONObject();
        paramJson.put("emailSubject","返佣风控");
        paramJson.put("emailBody",emailBody);//这边是需要模板的

        jsonObject.put("parameter",paramJson);

        //发送
        log.info("[RiskCommissionService] [action] [][]邮件发送信息:"+ JsonUtils.json(jsonObject));
        String post = OkHttpClientUtil.getInstance().post(sendMailUrl, jsonObject.toJSONString(), headerMap, 10l);
        log.info("[RiskCommissionService] [action] [][]邮件发送完成:"+post);
    }

    public String getBase64Str(ByteArrayOutputStream byteArrayOutputStream){
        byte[] bytes = byteArrayOutputStream.toByteArray();
        // 进行Base64转码
        String base64Str = Base64.getEncoder().encodeToString(bytes);
        return base64Str;
    }

    public void setManageByAccount(Map<String,List<DistributionOfflineRisk>> payAccountMap,int payAccount,Map<String,DistributionRiskManage> manageMap){
        try {
            for(Map.Entry<String, List<DistributionOfflineRisk>> entry:payAccountMap.entrySet()){
                //通过memberId进行分组，求count
                Map<String, DistributionOfflineRisk> collect = entry.getValue().stream().filter(e->StringUtils.isNotBlank(e.getMemberId())).collect(Collectors.toMap(DistributionOfflineRisk::getMemberId, v -> v, (old, cur) -> old));
                if(collect.size() > payAccount-1){
                    if(entry.getValue().size() > payAccount || entry.getValue().size() == payAccount){
                        List<DistributionOfflineRisk> hitRisks = entry.getValue();
                        hitRisks.stream().forEach(hit->{
                            List<String> orderIds = hitRisks.stream().map(DistributionOfflineRisk::getOrderId).collect(Collectors.toList());
                            DistributionRiskManage manage = manageMap.get(hit.getOrderId());
                            orderIds.remove(hit.getOrderId());
                            if(StringUtils.isNotBlank(manage.getLinkOrder())){
                                manage.setLinkOrder(manage.getLinkOrder() +","+ StringUtils.join(orderIds,","));
                            } else {
                                manage.setLinkOrder(StringUtils.join(orderIds,","));
                            }
                            if(StringUtils.isNotBlank(manage.getRuleNoList())){
                                manage.setRuleNoList(manage.getRuleNoList() +","+"006");
                            } else {
                                manage.setRuleNoList("006");
                            }
                        });
                    }
                }
            }
        } catch (Exception e) {
            log.error("支付账号维度风控失败:",e);
        }

    };

    public void setManageByRiskTag(Map<String,DistributionRiskManage> manageMap,Map<String,DistributionOfflineRisk> riskMap){
        for(Map.Entry<String, DistributionOfflineRisk> entry:riskMap.entrySet()){
            DistributionRiskManage manage = manageMap.get(entry.getKey());
            if(manage != null && entry.getValue().getOutRiskTag() > 0){
                if(StringUtils.isNotBlank(manage.getRuleNoList())){
                    manage.setRuleNoList(manage.getRuleNoList() +","+"010");
                } else {
                    manage.setRuleNoList("010");
                }
            }
        }
    }

    public void setManageByDivice(Map<String,List<DistributionOfflineRisk>> deviceMap,int newUserFinish,
                                  Map<String,OrderAddress> addressMap,Map<String,DistributionRiskManage> manageMap){
        for(Map.Entry<String, List<DistributionOfflineRisk>> entry:deviceMap.entrySet()){
            //这里一会儿改为批量v
            List<DistributionOfflineRisk> riskByDeviceIds = distributionOfflineRiskMapper.selectList(
                    new QueryWrapper<DistributionOfflineRisk>().eq("device_id",entry.getKey())
                            .eq("is_new_order",1)
            );
            List<String> orderIds = riskByDeviceIds.stream().map(DistributionOfflineRisk::getOrderId).collect(Collectors.toList());
            List<OrderInfo> orderInfos = orderInfoMapper.findByOrderIds(orderIds);
            Map<String,OrderInfo> orderInfoMap = orderInfos.stream()
                    .filter(e->e.getStatus() == 300 || e.getStatus() == 400)
                    .collect(Collectors.toMap(OrderInfo::getOrderId,v->v,(old, cur)->old));
            riskByDeviceIds = riskByDeviceIds.stream().filter(e->orderInfoMap.get(e.getOrderId()) != null).collect(Collectors.toList());

            //对本批次订单进行分销员维度区分，与全部订单进行匹配，本批次为命中订单，全部的为关联订单
            Map<String,List<DistributionOfflineRisk>> localDistributeMap = entry.getValue().stream()
                    .filter(risk -> risk.getIsNewOrder() == 1)
                    .collect(Collectors.groupingBy(dis->dis.getDistributeId()));

            //再根据分销员分组
            Map<String,List<DistributionOfflineRisk>> distributeMap = riskByDeviceIds.stream()
                    .collect(Collectors.groupingBy(dis->dis.getDistributeId()));


            //这个是全部的分销订单
            for(Map.Entry<String, List<DistributionOfflineRisk>> disEntry : localDistributeMap.entrySet()){
                //当前用户设备ID关联新客完单大于等于3单，且为同一分销员邀请的用户
                if(distributeMap.get(disEntry.getKey()) != null &&
                        (distributeMap.get(disEntry.getKey()).size() > newUserFinish || distributeMap.get(disEntry.getKey()).size() == newUserFinish)){
                    //用当前批次订单
                    List<DistributionOfflineRisk> hitRisk = disEntry.getValue();
                    List<String> distributeOrderIds = distributeMap.get(disEntry.getKey()).stream().map(DistributionOfflineRisk::getOrderId).collect(Collectors.toList());
                    hitRisk.stream().forEach(risk -> {
                        DistributionRiskManage manage = manageMap.get(risk.getOrderId());
                        List<String> linkOrder = new ArrayList<>();
                        linkOrder.addAll(distributeOrderIds);
                        linkOrder.remove(risk.getOrderId());
                        if(StringUtils.isNotBlank(manage.getRuleNoList()) && manage.getRuleNoList().contains("001")){
                            //当时这边会有重复的订单
                        } else {
                            if(StringUtils.isNotBlank(manage.getLinkOrder())){
                                manage.setLinkOrder(manage.getLinkOrder() +","+ StringUtils.join(linkOrder,","));
                            } else {
                                manage.setLinkOrder(StringUtils.join(linkOrder,","));
                            }
                            if(StringUtils.isNotBlank(manage.getRuleNoList())){
                                manage.setRuleNoList(manage.getRuleNoList() +","+"001");
                            } else {
                                manage.setRuleNoList("001");
                            }
                            manage.setHitTime(new Date());
                        }
                    });
                }
            }
        }
    }

    public void setManageByDriver(Map<String,List<DistributionOfflineRisk>> driverMap,Map<String,OrderInfo> orderMap,
                                      Map<String,OrderAddress> addressMap,Map<String,DistributionRiskManage> manageMap,
                                  int endAddressNum,int finishNum){
        for(Map.Entry<String, List<DistributionOfflineRisk>> entry:driverMap.entrySet()){
            //这是第二层分销员的划分
            Map<String,List<DistributionOfflineRisk>> distributeMap = new HashMap<>();
            entry.getValue().stream().forEach(risk -> {
                if(distributeMap.get(risk.getDistributeId()) != null){
                    distributeMap.get(risk.getDistributeId()).add(risk);
                } else {
                    List<DistributionOfflineRisk> risks = new ArrayList<>();
                    risks.add(risk);
                    distributeMap.put(risk.getDistributeId(),risks);
                }
            });
            //遍历第二层,这个是司机->分销员维度
            for(Map.Entry<String, List<DistributionOfflineRisk>> disEntry : distributeMap.entrySet()){
                List<Map<String,List<String>>> kiloMapList = new ArrayList<>();
                List<DistributionOfflineRisk> distributionOfflineRisks = disEntry.getValue();
                //计算每个订单分别与其他订单的距离，这个map存的是每个订单关联的其他订单
                Map<String,List<String>> kiloMap = new HashMap<>();
                for(DistributionOfflineRisk risk1 : distributionOfflineRisks){
                    List<String> kiloList = new ArrayList<>();

                    for(DistributionOfflineRisk risk2 : distributionOfflineRisks){
                        if(risk1.getOrderId().equals(risk2.getOrderId())){
                            continue;
                        }
                        OrderAddress orderAddress1 = addressMap.get(risk1.getOrderId());
                        OrderAddress orderAddress2 = addressMap.get(risk2.getOrderId());
                        double distance = CoordUtil.getDistance(orderAddress1.getEndLng().doubleValue(),
                                orderAddress1.getEndLat().doubleValue(),
                                orderAddress2.getEndLng().doubleValue(),
                                orderAddress2.getEndLat().doubleValue());
                        if(new BigDecimal(distance).compareTo(new BigDecimal(endAddressNum)) < 0){
                            kiloList.add(risk2.getOrderId());
                            kiloMap.put(risk1.getOrderId(),kiloList);
                        }
                    }
                }
                //这边得到每一笔订单满足第五条规则的关联订单，本订单为key,关联订单为value
                for(Map.Entry<String, List<String>> mapEntry : kiloMap.entrySet()){
                    if(mapEntry.getValue().size() < finishNum){
                        continue;
                    }
                    List<String> values = Lists.newArrayList(mapEntry.getValue());
                    DistributionRiskManage manage = manageMap.get(mapEntry.getKey());
                    if(StringUtils.isNotBlank(manage.getLinkOrder())){
                        manage.setLinkOrder(manage.getLinkOrder() +","+ StringUtils.join(values,","));
                    } else {
                        manage.setLinkOrder(StringUtils.join(values,","));
                    }
                    if(StringUtils.isNotBlank(manage.getRuleNoList())){
                        manage.setRuleNoList(manage.getRuleNoList() +","+"005");
                    } else {
                        manage.setRuleNoList("005");
                    }
                }
            }
        }
    }

    public void setManageByDistribute(Map<String,List<DistributionOfflineRisk>> disUserMap,Map<String,OrderInfo> orderMap,
                                      Map<String,OrderAddress> addressMap,Map<String,DistributionRiskManage> manageMap,
                                      BigDecimal payAmount,BigDecimal finishRate011,int orderTime,BigDecimal actualKilo,
                                      BigDecimal newUserRate,Map<String,OrderSupplierBill> supplierBillMap,int finishNum011,
                                      int finishNum009,BigDecimal integerNumRate,BigDecimal actualKiloRate){
        for (Map.Entry<String, List<DistributionOfflineRisk>> entry:disUserMap.entrySet()){
            int rateNum = 0;
            int newOrderNum = 0;
            int integerNum = 0;
            int actualKiloRate2 = 0;
            //命中这个规则的订单，这个是第二条规则当天分销员邀请的用户订单支付金额=1元的订单占当日所有邀请用户完单的比例大于等于50%
            Map<String,String> hitOrder2 = new HashMap<>();
            Map<String,String> hitOrder3 = new HashMap<>();
            Map<String,String> hitOrder4 = new HashMap<>();
            for(DistributionOfflineRisk risk : entry.getValue()){
                OrderInfo orderInfo = orderMap.get(risk.getOrderId());
                if(orderInfo.getTotalAmount().compareTo(payAmount) == 0 && (orderInfo.getStatus() == 400 || orderInfo.getStatus() == 300)){
                    hitOrder2.put(orderInfo.getOrderId(),orderInfo.getOrderId());
                    rateNum = rateNum + 1;
                }
                if(supplierBillMap.get(risk.getOrderId()) != null && supplierBillMap.get(risk.getOrderId()).getDistance().compareTo(actualKilo) < 0
                        && (orderInfo.getStatus() == 400 || orderInfo.getStatus() == 300)){
                    hitOrder4.put(orderInfo.getOrderId(),orderInfo.getOrderId());
                    newOrderNum = newOrderNum + 1;
                    actualKiloRate2 = actualKiloRate2 + 1;
                }
                //判断是否整数
                try {
                    if(orderInfo.getTotalAmount() != null && (String.valueOf(orderInfo.getTotalAmount()).split(".").length == 1 ||
                            String.valueOf(orderInfo.getTotalAmount()).split("\\.")[1].equals("00"))){
                        integerNum = integerNum + 1;
                    }
                } catch (Exception e) {
                    log.error("[RiskCommissionService] [action] [][] 报错订单:{}",JsonUtils.json(orderInfo));
                }
            }
            //看看分销员维度的订单有没有问题，第二条规则是有关联订单的
            BigDecimal rate2 = new BigDecimal(rateNum).divide(new BigDecimal(entry.getValue().size()),4,BigDecimal.ROUND_CEILING);
            log.info("[RiskCommissionService] [action] [][] 分销员{}维度011比例:{},当前分销员共有:{},命中条件的有:{}",entry.getKey(),rate2,entry.getValue().size(),rateNum);
            //当天分销员邀请的用户订单支付金额=1元的订单占当日所有邀请用户完单的比例大于等于50%，且邀请完单用户大于等于10
            if(rateNum>0 && finishRate011.compareTo(rate2)< 0 && entry.getValue().size() > finishNum011-1){
                log.info("[RiskCommissionService] [action] [][] 分销员{}维度011,当前所有订单{}",entry.getKey(),JsonUtils.json(entry.getValue()));
                //命中第二条规则
                for(DistributionOfflineRisk risk : entry.getValue()){
                    List<String> values = entry.getValue().stream().map(DistributionOfflineRisk::getOrderId).collect(Collectors.toList());
                    values.remove(risk.getOrderId());
                    DistributionRiskManage manage = manageMap.get(risk.getOrderId());
                    if(StringUtils.isNotBlank(manage.getRuleNoList())){
                        manage.setRuleNoList(manage.getRuleNoList() +","+"011");
                    } else {
                        manage.setRuleNoList("011");
                    }
                    if(StringUtils.isNotBlank(manage.getLinkOrder())){
                        manage.setLinkOrder(manage.getLinkOrder() +","+ StringUtils.join(values,","));
                    } else {
                        manage.setLinkOrder(StringUtils.join(values,","));
                    }
                    log.info("[RiskCommissionService] [action] [][] 分销员{}维度011,当前订单{},命中订单{}",entry.getKey(),risk.getOrderId(),JsonUtils.json(values));
                    manageMap.put(risk.getOrderId(),manage);
                }
            }

            //当前分销员邀请的用户订单实际里程为小于2公里占比大于等于50%，且邀请用户完单大于等于10单，且完单用户支付金额为整数占比大于80% 009
            if(newOrderNum > (finishNum009-1)
                    && new BigDecimal(integerNum).divide(new BigDecimal(entry.getValue().size()),2,BigDecimal.ROUND_CEILING).compareTo(integerNumRate) > 0
                    && new BigDecimal(String.valueOf(actualKiloRate2)).divide(new BigDecimal(entry.getValue().size()),2,BigDecimal.ROUND_CEILING).compareTo(actualKiloRate) > 0){
                for(DistributionOfflineRisk risk : entry.getValue()){
                    List<String> values = Lists.newArrayList(hitOrder4.values());
                    values.remove(risk.getOrderId());
                    DistributionRiskManage manage = manageMap.get(risk.getOrderId());
                    if(StringUtils.isNotBlank(manage.getRuleNoList())){
                        manage.setRuleNoList(manage.getRuleNoList() +","+"009");
                    } else {
                        manage.setRuleNoList("009");
                    }
                    if(StringUtils.isNotBlank(manage.getLinkOrder())){
                        manage.setLinkOrder(manage.getLinkOrder() +","+ StringUtils.join(values,","));
                    } else {
                        manage.setLinkOrder(StringUtils.join(values,","));
                    }
                    manageMap.put(risk.getOrderId(),manage);
                }
            }


            //看第三条,第四条规则，直接是单独的订单
            BigDecimal rate4 = new BigDecimal(newOrderNum).divide(new BigDecimal(entry.getValue().size()),BigDecimal.ROUND_CEILING);
            for(DistributionOfflineRisk risk : entry.getValue()){
                DistributionRiskManage manage = manageMap.get(risk.getOrderId());
                if(StringUtils.isNotBlank(hitOrder3.get(risk.getOrderId()))){
                    if(StringUtils.isNotBlank(manage.getRuleNoList())){
                        manage.setRuleNoList(manage.getRuleNoList() +","+"003");
                    } else {
                        manage.setRuleNoList("003");
                    }
                    manageMap.put(risk.getOrderId(),manage);
                }

//                if(StringUtils.isNotBlank(hitOrder4.get(risk.getOrderId())) && newUserRate.compareTo(rate4) < 0){
//                    if(StringUtils.isNotBlank(manage.getRuleNoList())){
//                        manage.setRuleNoList(manage.getRuleNoList() +","+"004");
//                    } else {
//                        manage.setRuleNoList("004");
//                    }
//                    List<String> hit4OrderIds = Lists.newArrayList(hitOrder4.values());
//                    hit4OrderIds.remove(risk.getOrderId());
//                    if(StringUtils.isNotBlank(manage.getLinkOrder())){
//                        manage.setLinkOrder(manage.getLinkOrder() +","+ StringUtils.join(hit4OrderIds,","));
//                    } else {
//                        manage.setLinkOrder(StringUtils.join(hit4OrderIds,","));
//                    }
//                    manageMap.put(risk.getOrderId(),manage);
//                }
            }
        }
    }

    /**
     *  转换不同维度map
     * */
    public void convertMap(DistributionOfflineRisk risk,
                           Map<String,List<DistributionOfflineRisk>> deviceMap,
                           Map<String,List<DistributionOfflineRisk>> disUserMap,
                           Map<String,List<DistributionOfflineRisk>> driverMap,
                           Map<String,List<DistributionOfflineRisk>> payAccountMap,
                           Map<String,DistributionOfflineRisk> riskCenterMap){
        //有设备id且是新客订单
        if(StringUtils.isNotBlank(risk.getDeviceId()) && risk.getIsNewOrder() == 1){
            if(deviceMap.get(risk.getDeviceId()) != null){
                deviceMap.get(risk.getDeviceId()).add(risk);
            } else {
                List<DistributionOfflineRisk> risks = new ArrayList<>();
                risks.add(risk);
                deviceMap.put(risk.getDeviceId(),risks);
            }
        }

        if(disUserMap.get(risk.getDistributeId()) != null){
            disUserMap.get(risk.getDistributeId()).add(risk);
        } else {
            List<DistributionOfflineRisk> risks = new ArrayList<>();
            risks.add(risk);
            disUserMap.put(risk.getDistributeId(),risks);
        }

        if(StringUtils.isNotBlank(risk.getDriverCardNo())){
            if(driverMap.get(risk.getDriverCardNo()) != null){
                driverMap.get(risk.getDriverCardNo()).add(risk);
            } else {
                List<DistributionOfflineRisk> risks = new ArrayList<>();
                risks.add(risk);
                driverMap.put(risk.getDriverCardNo(),risks);
            }
        }

        if(StringUtils.isNotBlank(risk.getPayAccount())){
            if(payAccountMap.get(risk.getPayAccount()) != null){
                payAccountMap.get(risk.getPayAccount()).add(risk);
            } else {
                List<DistributionOfflineRisk> risks = new ArrayList<>();
                risks.add(risk);
                payAccountMap.put(risk.getPayAccount(),risks);
            }
        }
        if(risk.getOutRiskTag() > -1){
            riskCenterMap.put(risk.getOrderId(),risk);
        }
    }

    /**这边要先为每一条生成风控单，后面不满足直接去除*/
    public Map<String,DistributionRiskManage> convertManage(List<DistributionOfflineRisk> riskList,Map<String,OrderAddress> addressMap,Map<String,OrderInfo> orderMap){
        Map<String,DistributionRiskManage> map = new HashMap<>();
        for(DistributionOfflineRisk risk : riskList){
            DistributionRiskManage manage = new DistributionRiskManage();
            manage.setDistributeId(risk.getDistributeId());
            manage.setOrderId(risk.getOrderId());
            manage.setCityId(risk.getCityId());
            manage.setCityName(risk.getCityName());
            manage.setCreateTime(new Date());
            manage.setUpdateTime(new Date());
            manage.setMainScene(3);
            manage.setChildScene(1);
            manage.setRiskLevel(5);
            manage.setHitTime(new Date());
            manage.setCityId(addressMap.get(risk.getOrderId()).getStartCityId());
            manage.setDistributeName(risk.getDistributeName());
            manage.setDistributorName(risk.getDistributorsName());
            manage.setDriverCardNo(risk.getDriverCardNo());
            String supplierCodeFull = orderMap.get(risk.getOrderId()).getSupplierCodeFull();
            if(StringUtils.isNotBlank(supplierCodeFull)){
                String supplierName = supplierApiClient.getSupplierCodeMap().get(supplierCodeFull);
                manage.setSupplierCode(supplierCodeFull);
                manage.setSupplierName(supplierName);
                manage.setPayAccount(risk.getPayAccount());
                manage.setMemberId(orderMap.get(risk.getOrderId()).getMemberId());
                manage.setUnionId(orderMap.get(risk.getOrderId()).getUniId());
                manage.setPhone(orderMap.get(risk.getOrderId()).getPassengerCellphone());
                manage.setFinishTime(orderMap.get(risk.getOrderId()).getFinishTime());
                manage.setOrderCreateTime(orderMap.get(risk.getOrderId()).getCreateTime());
                manage.setFinishDate(DateUtil.date2String(orderMap.get(risk.getOrderId()).getFinishTime(),"yyyyMMdd"));
            }
            map.put(risk.getOrderId(),manage);
        }
        return map;
    }

    public static boolean isNumeric(String str) {
        for (int i = str.length(); --i >= 0; ) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    MemberQueryResponse getInfoByMemberId(String memberId)
    {
        MemberQueryResponse memberInfoByMemberId = memberApi.getMemberInfoByKey(new MemberQueryParam(memberId, 33,1));
        try {
            if(memberInfoByMemberId == null || StringUtils.isBlank(memberInfoByMemberId.getData().getMobile())){
                List<MemberData> listByMemberId = memberApi.getListByMemberId(new ListByMemberIdParam(memberId)).getData();
                for(MemberData data : listByMemberId){
                    if(data.getMemberId().equals(memberId)){
                        continue;
                    }
                    memberInfoByMemberId = memberApi.getMemberInfoByKey(new MemberQueryParam(String.valueOf(data.getMemberId()), 0,1));
                }
            }
        } catch (Exception e) {
            log.error("司推乘风控获取手机号出错");
        }
        return memberInfoByMemberId;
    }
}
