package com.ly.car.risk.process.service.rule.safe;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.api.EsQueryClient;
import com.ly.car.risk.process.client.OrderClient;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.repo.mtticket.entity.DriverInfo;
import com.ly.car.risk.process.repo.mtticket.entity.RideOrderInfo;
import com.ly.car.risk.process.repo.mtticket.mapper.MtDriverInfoMapper;
import com.ly.car.risk.process.repo.mtticket.mapper.RideOrderInfoMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.ability.AutoCallService;
import com.ly.car.risk.process.service.dto.DriverWarningDTO;
import com.ly.car.risk.process.service.dto.MtDriverConfig;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.DriverLocationResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMapCache;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SfcSafetyWarningService {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private SaveScoredSortedSetService saveScoredSortedSetService;
    @Resource(name = "executorService")
    private ExecutorService executorService;
    @Resource
    private EsQueryClient esQueryClient;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private RideOrderInfoMapper rideOrderInfoMapper;
    @Resource(name = "binlogProducer")
    private MqRiskProducer mqRiskProducer;
    @Resource
    private MtDriverInfoMapper mtDriverInfoMapper;
    @Resource
    private AutoCallService autoCallService;
    @Resource(name = "riskSecurityProducer")
    private MqRiskProducer  riskSecurityProducer;
    @Resource
    private CarOrderService carOrderService;
    @Resource
    private OrderClient     orderClient;

    public void computeMoving(){
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(RedisKeyConstants.SFC_MOVING_ORDER);
        //移除3天前的订单
        long initMs = TimeUtil.initMs();
        long threeDayMs = TimeUtil.threeDayMs();
        scoredSortedSet.removeRangeByScore(initMs,true,threeDayMs,true);
        //获取前一分钟的开始和结束时间
        String minute = DateUtil.date2String(DateUtil.addMinute(new Date(),-1),"yyyy-MM-dd HH:mm");
        long startMs =  DateUtil.string2Date(minute+":00").getTime();
        String endMinute = DateUtil.date2String(new Date(),"yyyy-MM-dd HH:mm");
        long endMs =  DateUtil.string2Date(endMinute+":00").getTime();
        LoggerUtils.info(log, "顺风车安全预警,获取权重{}～{}间的订单", startMs, endMs);
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, endMs, true);
        List<String> orderIds = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            String orderId = (String) obj.getValue();
            orderIds.add(orderId);
        }
        if(CollectionUtils.isEmpty(orderIds)){
            LoggerUtils.info(log,"顺风车安全预警,当前无订单需处理");
            return;
        }
        LoggerUtils.info(log,"顺风车安全预警, 需处理订单:{}个，订单号为:{}", orderIds.size(),JSON.toJSONString(orderIds));

        HashMap<String, String> logMap = LoggerUtils.getLogMap();
        for(String orderId : orderIds){
            CompletableFuture.runAsync(() -> {
                LoggerUtils.initLogMap(logMap);
                LoggerUtils.getLogMap().put("filter2",orderId);
                try {
                    //获取预估距离，没有的话就全部移除，都不走了
                    RBucket<String> bucket = redissonClient.getBucket(RedisKeyConstants.SFC_ESTIMATE_PATH + orderId);
                    if(bucket == null || StringUtils.isBlank(bucket.get())){
                        scoredSortedSet.remove(orderId);
                        LoggerUtils.info(log,"顺风车安全预警，order:{},未获取到预估距离，不再处理",orderId);
                        return;
                    }

                    LoggerUtils.info(log,"顺风车安全预警，order:{}, 预估距离{}",orderId, bucket.get());
                    //查下顺风车
                    String supplierCode = "";
                    String supplierOrderId = "";
                    String passengerCellphone = "";
                    Date passengerOnCarTime = null;
                    Date useTime = null;
                    BigDecimal startLng = null;
                    BigDecimal endLng = null;
                    BigDecimal startLat = null;
                    BigDecimal endLat = null;
                    String startAddress = "";
                    String endAddress = "";

                    if (OrderUtils.isNewOrder(orderId)) {
                        CarOrderDetail sfcOrder = carOrderService.queryOrderDetail(orderId);

                        if (null == sfcOrder) {
                            LoggerUtils.info(log, "顺风车安全预警，order:{},未查询到对应订单，不再处理", orderId);
                            scoredSortedSet.remove(orderId);
                            return;
                        }

                        //查询当前状态是否是已经完单
                        if (sfcOrder.getOrderState() == OrderState.TRIP_FINISHED.getCode()
                                || sfcOrder.getOrderState() == OrderState.ORDER_CLOSED.getCode()
                                || sfcOrder.getOrderState() == OrderState.CANCELED.getCode()) {
                            LoggerUtils.info(log, "顺风车安全预警，order:{},订单已完结，不再处理", orderId);
                            scoredSortedSet.remove(orderId);
                            return;
                        }

                        supplierOrderId = sfcOrder.getSupplierOrderId();

                        supplierCode = sfcOrder.getCarInfo().getSupplierCode();
                        passengerCellphone = sfcOrder.getPassengerInfo().getPassengerCellPhone();
                        passengerOnCarTime = sfcOrder.getBaseInfo().getGmtPassengerBoard();
                        useTime = sfcOrder.getBaseInfo().getGmtUsage();
                        startLng = sfcOrder.getOrderTrip().getDepartureLng();
                        endLng = sfcOrder.getOrderTrip().getArrivalLng();
                        startLat = sfcOrder.getOrderTrip().getDepartureLat();
                        endLat = sfcOrder.getOrderTrip().getArrivalLat();
                        startAddress = sfcOrder.getOrderTrip().getDepartureAddress();
                        endAddress = sfcOrder.getOrderTrip().getArrivalAddress();

                    }else {
                        SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(orderId);
                        //查询当前状态是否是已经完单
                        if(sfcOrder.getStatus() == 300){
                            LoggerUtils.info(log, "顺风车安全预警，order:{},订单已完结，不再处理", orderId);
                            scoredSortedSet.remove(orderId);
                            return;
                        }
                        if(sfcOrder.getStatus() == 1000){
                            LoggerUtils.info(log, "顺风车安全预警，order:{},订单已取消，不再处理", orderId);
                            scoredSortedSet.remove(orderId);
                            return;
                        }

                        supplierCode = sfcOrder.getSupplierCode();
                        supplierOrderId = sfcOrder.getSupplierOrderId();
                        passengerCellphone = sfcOrder.getPassengerCellphone();
                        passengerOnCarTime = sfcOrder.getPassengerOnCarTime();
                        useTime= sfcOrder.getUseTime();

                        OrderAddress orderAddress = orderAddressMapper.findByOrderId(orderId);
                        startLng = orderAddress.getStartLng();
                        endLng = orderAddress.getEndLng();
                        startLat = orderAddress.getStartLat();
                        endLat = orderAddress.getEndLat();
                        startAddress = orderAddress.getStartAddress();
                        endAddress = orderAddress.getEndAddress();
                    }

                    String estimatePath = bucket.get();
                    String locationPoint = "";
                    if (OrderUtils.isNewOrder(orderId)) {
                        DriverLocationResponse res = orderClient.driverLocation(orderId);
                        if (null != res && res.isSuccess()) {
                            locationPoint = res.getLongitude().toString() + "," + res.getLatitude().toString();
                        }
                    } else {
                        if (supplierCode.startsWith("MadaSaas")) {
                            RideOrderInfo rideOrderInfo = rideOrderInfoMapper.selectOne(new QueryWrapper<RideOrderInfo>()
                                    .eq("order_no", supplierOrderId)
                                    .last("limit 1")
                            );
                            if (rideOrderInfo == null || StringUtils.isBlank(rideOrderInfo.getDriverMobile())) {
                                //万一萌艇订单里面没有司机号码就算了
                                //更新行程中订单下次需要执行的时间
                                saveScoredSortedSetService.save(RedisKeyConstants.SFC_MOVING_ORDER,
                                        3 * 24 * 60 * 60L, orderId, DateUtil.addMinute(new Date(), 5).getTime());
                                return;
                            }
                            //再通过driver_mobile查询下司机信息
                            DriverInfo driverInfo = mtDriverInfoMapper.selectOne(new QueryWrapper<DriverInfo>().eq("mobile", rideOrderInfo.getDriverMobile()).last("limit 1"));
                            if (driverInfo != null) {
                                LoggerUtils.info(log,"查询萌艇订单位置:{}", orderId);
                                locationPoint = esQueryClient.queryMtLocation(rideOrderInfo.getDriverId());
                            }
                        } else {
                            locationPoint = esQueryClient.queryCurrentLocation(orderId);
                        }
                    }

                    if(StringUtils.isBlank(locationPoint)){
                        LoggerUtils.info(log, "顺风车安全预警，order:{},未查询到司机当前位置，不再处理", orderId);
                        scoredSortedSet.remove(orderId);
                        return;
                    }
                    //判断当前时间是否未超过用车时间
                    if(null == passengerOnCarTime || passengerOnCarTime.before(DateUtil.string2Date("2000-01-01 00:00:00"))){
                        LoggerUtils.info(log, "顺风车安全预警，order:{},乘客未上车，5分钟后再校验", orderId);
                        //更新行程中订单下次需要执行的时间
                        saveScoredSortedSetService.save(RedisKeyConstants.SFC_MOVING_ORDER,
                                3 * 24 * 60 * 60L,orderId, DateUtil.addMinute(new Date(),5).getTime());
                        return;
                    }
                    LoggerUtils.info(log, "顺风车安全预警,order:{},当前位置{}", orderId, locationPoint);
                    String lng = locationPoint.split(",")[0];
                    String lat = locationPoint.split(",")[1];
                    //存储当前司机位置
                    RMapCache<String, String> mapCache = redissonClient.getMapCache(RedisKeyConstants.ORDER_DRIVER_LOCATION_MINUTES);
                    String locationStr = mapCache.get(orderId);
                    LoggerUtils.info(log, "顺风车安全预警,order:{},获取上一循环当前位置:{}", orderId, locationStr);

                    if(StringUtils.isNotBlank(locationStr)){

                        //当前不为空的话就计算规则，暂时能计算的是
                        //1.计算是否未移动,当前位置与五分钟之前的位置相差是不是小于100米,处理是否异常停留
                        double distance = CoordUtil.getDistance(lng, lat, locationStr.split(",")[0], locationStr.split(",")[1]);
                        double startDistance = CoordUtil.getDistance(lng, lat, String.valueOf(startLng), String.valueOf(startLat));
                        double endDistance = CoordUtil.getDistance(lng, lat, String.valueOf(endLng),String.valueOf(endLat));
                        if(new BigDecimal(startDistance).compareTo(new BigDecimal("500")) >0 && new BigDecimal(endDistance).compareTo(new BigDecimal("500")) > 0) {
                            if (new BigDecimal(distance).compareTo(new BigDecimal("10")) < 0) {
                                //这个时候塞入缓存，前端页面需要展示
                                redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING + orderId).put("aq003", DateUtil.date2String(new Date()) + "," + "车辆长时间停留");
                                redissonClient.getKeys().expire(RedisKeyConstants.HIT_SAFE_WARNING + orderId, 6, TimeUnit.MINUTES);
                                LoggerUtils.info(log,"顺风车安全预警,order:{},命中异常停留,上一循环位置:{}", orderId, locationStr);

                                riskSecurityProducer.send(MqTagEnum.car_risk_warn_record,JsonUtils.json(convertParam(orderId,1,"SFC")),0);
                                LoggerUtils.info(log,"顺风车安全预警,order:{},命中异常停留,给tag:{}发送信息处理后续流程",orderId,MqTagEnum.car_risk_warn_record.name());

                                if(supplierCode.startsWith("MadaSaas") || supplierCode.startsWith("zhizhuxia") || supplierCode.startsWith("miaoda")){
                                    DriverWarningDTO dto = new DriverWarningDTO(supplierOrderId,"sjaq004");
                                    mqRiskProducer.send(MqTagEnum.car_risk_driver_ex_notify,JsonUtils.json(dto), 0L);
                                    LoggerUtils.info(log,"顺风车安全预警,order:{},异常停留,给tag:{}发送信息处理后续司机通知",orderId,MqTagEnum.car_risk_driver_ex_notify.name());
                                }
                                if(supplierCode.startsWith("zhizhuxia") || supplierCode.startsWith("miaoda")){
                                    Map<String,String> paramMap = new HashMap<>();
                                    paramMap.put("@startstation",startAddress);
                                    paramMap.put("@endstation",endAddress);
                                    paramMap.put("@trainno",DateUtil.date2String(useTime));
                                    autoCallService.sendCall(orderId,"AO_117_1692100714966", passengerCellphone,paramMap);
                                }
                            }
                        }
                        //2.计算车速过快
                        BigDecimal speed =new BigDecimal(distance).divide(new BigDecimal("1000"))
                                .divide(new BigDecimal("6"),2,BigDecimal.ROUND_CEILING).multiply(new BigDecimal("60")).setScale(2,BigDecimal.ROUND_CEILING);
                        if(speed.compareTo(new BigDecimal("144")) > 0){
                            //这个时候塞入缓存，前端页面需要展示
                            redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING+orderId).put("aq002",DateUtil.date2String(new Date())+","+"车辆行驶速度过快");
                            redissonClient.getKeys().expire(RedisKeyConstants.HIT_SAFE_WARNING+orderId,6,TimeUnit.MINUTES);
                            LoggerUtils.info(log, "顺风车安全预警,order:{},命中车速过快,当前速度:{}", orderId, speed);

                            riskSecurityProducer.send(MqTagEnum.car_risk_warn_record,JsonUtils.json(convertParam(orderId,2,"SFC")),0);
                            if(supplierCode.startsWith("MadaSaas") || supplierCode.startsWith("zhizhuxia") || supplierCode.startsWith("miaoda")){
                                DriverWarningDTO dto = new DriverWarningDTO(supplierOrderId,"aq002");
                                mqRiskProducer.send(MqTagEnum.car_risk_driver_ex_notify,JsonUtils.json(dto), 0L);
                                LoggerUtils.info(log,"顺风车安全预警,order:{},车速过快,给tag:{}发送信息处理后续司机通知",orderId,MqTagEnum.car_risk_driver_ex_notify.name());
                            }
                        }
                        //处理路线偏移
                        dealAqMoving(orderId,lng,lat,estimatePath,supplierCode,supplierOrderId,passengerCellphone,useTime,startLng,endLng,startLat,endLat,startAddress,endAddress);

                        //执行完后 后面未完单则要继续计算，所以还是要塞入缓存的
                        mapCache.put(orderId,locationPoint,10, TimeUnit.MINUTES);
                    } else {
                        LoggerUtils.info(log, "顺风车安全预警,order:{},本次为第一次执行,存储当前位置入缓存后，等待下次执行", orderId);
                        //当前为空的话塞入缓存,说明是第一次，更新下下次需要执行的时间
                        mapCache.put(orderId,locationPoint,10, TimeUnit.MINUTES);
                    }

                    //更新行程中订单下次需要执行的时间
                    saveScoredSortedSetService.save(RedisKeyConstants.SFC_MOVING_ORDER,
                            3 * 24 * 60 * 60L,orderId, DateUtil.addMinute(new Date(),5).getTime());

                    redissonClient.getKeys().expire(RedisKeyConstants.ORDER_DRIVER_LOCATION_MINUTES, 10, TimeUnit.MINUTES);
                    LoggerUtils.info(log,"顺风车安全预警,order:{}处理完毕,更新缓存5分钟后再次执行",orderId);

                } catch (Exception e) {
                    LoggerUtils.error(log, "顺风车安全预警,order:{},处理中发生异常", e, orderId);
                } finally {
                    LoggerUtils.removeAll();
                }
            },executorService).handle((result, e) -> {
                if (e != null) {
                    LoggerUtils.error(log,"CompletableFuture处理异常{}",e);
                }
                return result;
            });
        }
    }

    //处理aq001
    public void dealAqMoving(String orderId, String lng, String lat, String pointStr
            , String supplierCode, String supplierOrderId, String passengerCellphone, Date useTime
            , BigDecimal startLng, BigDecimal endLng, BigDecimal startLat, BigDecimal endLat, String startAddress, String endAddress) {

        LoggerUtils.info(log, "顺风车安全预警,order:{}，处理aq001开始,pointStr：{}", orderId, pointStr);
        //计算路线偏移，当前位置在预估路径最近的距离
        if(StringUtils.isBlank(pointStr)){
            return;
        }
        List<String> locationPoint = new ArrayList<>(Arrays.asList(pointStr.split(";")));
        List<String> removeAll = new ArrayList<>();//移除途径点
        int decrNum = 0;
        //判断连续减的拐 点后是否是连续增以及超过10个都是连续增的话则判断路线偏移
        double lastestDistance = 0;
        for(int i=0;i<locationPoint.size()-1;i++){
            //把前面的点先放到数据里面
            removeAll.add(locationPoint.get(i));
            double movingDistanceBefore = CoordUtil.getDistance(lng, lat, locationPoint.get(i).split(",")[0], locationPoint.get(i).split(",")[1]);
            double movingDistanceAfter = CoordUtil.getDistance(lng, lat, locationPoint.get(i+1).split(",")[0], locationPoint.get(i+1).split(",")[1]);
            //看看是否是连续减，连续减说明是持续靠近，需要找到最近的点
            if(movingDistanceAfter < movingDistanceBefore){
                decrNum = decrNum + 1;
            } else {
                //到这里说明不是持续靠近了
                decrNum = 0;
            }
            if(i>0 && decrNum != 0){
                continue;
            }
//                log.info("[SfcSafetyWarningService][computeMoving][{}][]顺风车安全预警任务启动-经纬度出现拐点{}",orderId,orderId);
            //到了这边说明是突然有了拐点，这个时候要看下后面是不是连续增大
            int incrNum2 = 0;
            for(int j=i;j<locationPoint.size()-1;j++){
                if(locationPoint.size() - j < 6){
                    //说明马上到终点了，没啥必要继续判断是否偏移，交给其他策略
                    break;
                }
                double movingDistanceBefore2 = CoordUtil.getDistance(lng, lat, locationPoint.get(j).split(",")[0], locationPoint.get(j).split(",")[1]);
                double movingDistanceAfter2 = CoordUtil.getDistance(lng, lat, locationPoint.get(j+1).split(",")[0], locationPoint.get(j+1).split(",")[1]);
                if(movingDistanceBefore2 < movingDistanceAfter2){
                    incrNum2 = incrNum2 + 1;
                } else {
                    incrNum2 = 0;
                }
                if(j != i && incrNum2 == 0){
                    break;
                }
                if(incrNum2 > 5){
                    break;
                }
            }
            if(incrNum2 > 5){
                LoggerUtils.info(log, "顺风车安全预警,order:{},经纬度出现拐点后未出现持续间距增大情况,{}", orderId, pointStr);
                //到这边说明满足了连续减和连续增的情况，说明i这个位置是最短的点
                lastestDistance = movingDistanceBefore;
                break;
            }
        }
        if(new BigDecimal(lastestDistance).compareTo(new BigDecimal("2000")) > 0) {
            //到这边说明满足了连续减和连续增的情况，说明i这个位置是最短的点
            //判断i的距离到当前位置是否小于1000，小于则未偏移，大于则偏移，存入缓存，移除前面的点减少位移
            BigDecimal planeAngle = new BigDecimal(CoordUtil.getDegree(startLng.doubleValue(), startLat.doubleValue(),
                    endLng.doubleValue(), endLat.doubleValue(), Double.parseDouble(lng), Double.parseDouble(lat)));
            if (planeAngle.compareTo(new BigDecimal("90")) > 0) {
                redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING + orderId).put("sjaq006", DateUtil.date2String(new Date()) + "," + "车辆行驶路线偏移");
                redissonClient.getKeys().expire(RedisKeyConstants.HIT_SAFE_WARNING + orderId, 6, TimeUnit.MINUTES);
                LoggerUtils.info(log,"顺风车安全预警,order:{}, 命中路线偏移", orderId);
                riskSecurityProducer.send(MqTagEnum.car_risk_warn_record,JsonUtils.json(convertParam(orderId,0,"SFC")),0);

                if(supplierCode.startsWith("MadaSaas") || supplierCode.startsWith("zhizhuxia") || supplierCode.startsWith("miaoda")){
                    DriverWarningDTO dto = new DriverWarningDTO(supplierOrderId,"aq001");
                    mqRiskProducer.send(MqTagEnum.car_risk_driver_ex_notify,JsonUtils.json(dto), 0L);
                    //发送用户端提示mq
                    if(supplierCode.startsWith("zhizhuxia") || supplierCode.startsWith("miaoda")){
                        Map<String,String> paramMap = new HashMap<>();
                        paramMap.put("@startstation",startAddress);
                        paramMap.put("@endstation",endAddress);
                        paramMap.put("@trainno",DateUtil.date2String(useTime));
                        autoCallService.sendCall(orderId,"AO_117_1692100463942", passengerCellphone,paramMap);
                    }
                }
            }
        }
        locationPoint.removeAll(removeAll);
        //更新预估路径
        redissonClient.getBucket(RedisKeyConstants.SFC_ESTIMATE_PATH + orderId).set(StringUtils.join(locationPoint,";"),1,TimeUnit.DAYS);
        LoggerUtils.info(log, "顺风车安全预警,order:{}, 处理aq001结束,更新预估路径", orderId);
    }

    public JSONObject convertParam(String orderId,Integer type,String productLine){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId",orderId);
        jsonObject.put("warnType",type);
        jsonObject.put("productLine",productLine);
        return jsonObject;
    }


    public MtDriverConfig queryConfig(){
        try {
            String configJson = ConfigCenterClient.get("mt_driver_config");
            if(StringUtils.isNotBlank(configJson)){
                MtDriverConfig config = JSONObject.parseObject(configJson, MtDriverConfig.class);
                return config;
            }
        } catch (Exception e) {
        }
        return null;
    }
}
