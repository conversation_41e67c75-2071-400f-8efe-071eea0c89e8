package com.ly.car.risk.process.controller.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * @author: fulin.li
 * @create-date: 2025/9/10 16:38
 */
@Data
public class SfcRiskJobExecuteReq {

    /**
     * "scene": "8-2","21-1","30-1"
     */
    private List<String> sceneList = Lists.newArrayList("8-2", "21-1", "30-1");

    /**
     * 向前偏移小时
     */
    private Integer offsetHour = 13;

}
