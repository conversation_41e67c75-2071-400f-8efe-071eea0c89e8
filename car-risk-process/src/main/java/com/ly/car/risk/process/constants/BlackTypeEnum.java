package com.ly.car.risk.process.constants;

public enum BlackTypeEnum {

    BAN_DRIVER_ONE(1,"用户要求禁止司机为其服务（司机行为仅对个人伤害大,如司机不服务等）"),
    BAN_DRIVER_SAFE(2,"发生严重安全风险问题（安全问题、服务态度恶劣等）"),
    BAN_DRIVER_OTHER(3,"其他问题（划单、要求加价、线下交易）")
    ;

    private Integer code;
    private String name;

    BlackTypeEnum(Integer code,String name){
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(Integer code){
        for (BlackTypeEnum enumItem : BlackTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getName();
            }
        }
        return null;
    }
}
