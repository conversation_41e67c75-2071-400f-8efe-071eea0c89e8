package com.ly.car.risk.process.service.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.sharding.order.entity.OrderSupplierBill;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class UpdateScoreSortedSetService {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    public void updateSupplierBill(String key, OrderSupplierBill orderSupplierBill){
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(0,scoredSortedSet.size()-1);
        for(ScoredEntry<Object> obj : scoredEntries){
            SendOrderContext context = JSONObject.parseObject(obj.getValue().toString(),SendOrderContext.class);
            if(context.getOrderId().equals(orderSupplierBill.getOrderId())){
                log.info("[UpdateScoreSortedSetService][updateSupplierBill][][]缓存更新orderSupplierBill:{},距离:{}，时长{}",
                        orderSupplierBill.getOrderId(),orderSupplierBill.getDistance(),orderSupplierBill.getDuration());
                scoredSortedSet.removeRangeByScore(obj.getScore(),true,obj.getScore(),true);
                context.setActualDuration(orderSupplierBill.getDuration());
                context.setActualKilo(orderSupplierBill.getDistance());
                scoredSortedSet.add(obj.getScore(), JSON.toJSONString(context));
                break;
            }
        }
        try {
            //重新给个过期时间
            redissonClient.getKeys().expire(key, 3 * 24 * 60 * 60L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[UpdateScoreSortedSetService][updateSupplierBill][][]缓存更新orderSupplierBill重新设置过期时间失败");
        }


    }

    public List<Object> getRedisList(String key){
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(0,scoredSortedSet.size()-1);
        List<Object> orderContextList = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            orderContextList.add(obj.getValue());
        }
        return orderContextList;
    }
}
