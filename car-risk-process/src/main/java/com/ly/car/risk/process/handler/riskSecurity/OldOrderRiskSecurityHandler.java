package com.ly.car.risk.process.handler.riskSecurity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.common.enums.VoiceApiProviderEnum;
import com.ly.car.risk.common.enums.VoiceProductTypeEnum;
import com.ly.car.risk.process.api.TencentCloudApiClient;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.repo.dcdbcarrisk.mapper.RiskChargeVoiceMapper;
import com.ly.car.risk.process.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice;
import com.ly.car.risk.process.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice.Ext;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.RiskWarnService;
import com.ly.car.risk.process.service.VirtualPhoneRecordService;
import com.ly.car.risk.process.service.ability.AutoCallService;
import com.ly.car.risk.process.service.dto.VirtualPhoneRecordRsp;
import com.ly.car.risk.process.service.rule.imGrooup.ImFilterContext;
import com.ly.car.risk.process.service.rule.imGrooup.SensitiveTextService;
import com.ly.car.risk.process.service.sfc.SfcSupplierOrderService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description of OldOrderRiskSecurityHandler
 *
 * <AUTHOR>
 * @date 2024/3/14
 * @desc
 */
@Service
@Slf4j
public class OldOrderRiskSecurityHandler extends AbstractRiskSecurityHandler{

    private VirtualPhoneRecordService virtualPhoneRecordService;
    private SfcOrderMapper sfcOrderMapper;
    private SfcSupplierOrderService sfcSupplierOrderService;
    private RedissonClient redissonClient;
    private MqRiskProducer mqRiskProducer;
    private MqRiskProducer textRiskProducer;
    private TencentCloudApiClient tencentCloudApiClient;
    private SensitiveTextService sensitiveTextService;
    private AutoCallService autoCallService;
    private RiskWarnService       riskWarnService;
    private RiskChargeVoiceMapper riskChargeVoiceMapper;


    @Override
    public String supportOrderType() {
        return "old";
    }

    @Override
    public void doHandler(String body, String tag) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        if(tag.equals(MqTagEnum.car_risk_self_security_task.name())){
            //监听顺风车状态变动，发送当前tag开始监听通话记录
            dealTencentRecTask(body);
        } else if(tag.equals(MqTagEnum.car_self_tencent_get_describe_task.name())){
            riskChargeVoiceMapper = SpringContextUtil.getBean("riskChargeVoiceMapper");
            dealTencentDescTask(body);
        } else if(tag.equals(MqTagEnum.car_risk_self_auto_call_check_task.name())){
            String callId = jsonObject.getString("callId");
            String orderId = jsonObject.getString("orderId");
            autoCallService = SpringContextUtil.getBean("autoCallService");
            Boolean aBoolean = autoCallService.checkAutoCall(callId,orderId);
        } else if(tag.equals(MqTagEnum.car_risk_Self_on_car_sms_send.name())){
            String callId = jsonObject.getString("callId");
            String orderId = jsonObject.getString("orderId");
            String mobile = jsonObject.getString("mobile");
            Integer plateId = jsonObject.getInteger("plateId");
            autoCallService = SpringContextUtil.getBean("autoCallService");
            autoCallService.checkRecord(callId,orderId,mobile,plateId);
        } else if(tag.equals(MqTagEnum.car_risk_warn_record.name())){
            String orderId = jsonObject.getString("orderId");
            Integer warnType = jsonObject.getInteger("warnType");
            String productLine = jsonObject.getString("productLine");
            riskWarnService = SpringContextUtil.getBean("riskWarnService");
            riskWarnService.saveRiskWarn(orderId,warnType,productLine);
        }
    }

    public void dealTencentDescTask(String body){
        JSONObject taskIdJson = JSONObject.parseObject(body);
        long taskId = taskIdJson.getLong("taskId");
        Integer times = taskIdJson.getInteger("times");
        if(times > 3){
            return;
        }
        tencentCloudApiClient = SpringContextUtil.getBean("tencentCloudApiClient");
        JSONObject jsonObject = tencentCloudApiClient.describeTaskStatus(taskId);
        log.info("[][][][]录音转文本返回{}", JsonUtils.json(jsonObject));
        if(jsonObject == null){
            return;
        }
        String text = jsonObject.getString("text");
        if(StringUtils.isNotBlank(text)){
            //发送出去，业务组要存起来
            if(taskIdJson.getString("callId") != null){
                textRiskProducer = SpringContextUtil.getBean("textRiskProducer");
                JSONObject textBody = new JSONObject();
                textBody.put("orderId",taskIdJson.getString("orderId"));
                textBody.put("text",text);
                textBody.put("callId",taskIdJson.getString("callId"));
                textRiskProducer.send(MqTagEnum.car_risk_sfc_text,textBody.toJSONString(),0);
            }
            //过下敏感词
            ImFilterContext context = new ImFilterContext();
            context.setUnionId(taskIdJson.getString("unionId"));
            context.setMemberId(taskIdJson.getString("memberId"));
            context.setText(text);
            context.setSource(taskIdJson.getInteger("source"));//虚拟号
            context.setOrderId(taskIdJson.getString("orderId"));
            context.setDriverCardNo(taskIdJson.getString("driverCardNo"));
            sensitiveTextService = SpringContextUtil.getBean("sensitiveTextService");
            sensitiveTextService.doHandler(context);
            
            // 保存调用统计
            RiskChargeVoice riskChargeVoice = new RiskChargeVoice();
            riskChargeVoice.setOrderNo(taskIdJson.getString("orderId"));
            riskChargeVoice.setSource("");
            riskChargeVoice.setProductLine("");
            riskChargeVoice.setApiProvider(VoiceApiProviderEnum.TX_VOICE.getCode());
            riskChargeVoice.setProductType(VoiceProductTypeEnum.VIRTUAL_PHONE.getCode());
            BigDecimal audioDuration = jsonObject.getBigDecimal("audioDuration");
            riskChargeVoice.setVoiceDuring(audioDuration == null ? BigDecimal.ZERO : audioDuration);
            riskChargeVoice.setCharge(new BigDecimal(0.46/60/60).multiply(riskChargeVoice.getVoiceDuring()).setScale(6, RoundingMode.HALF_UP));
            RiskChargeVoice.Ext ext = new Ext();
            ext.setReq(String.valueOf(taskId));
            riskChargeVoice.setExt(JSON.toJSONString(ext));
            riskChargeVoice.setCreateTime(new Date());
            riskChargeVoice.setUpdateTime(new Date());
            riskChargeVoiceMapper.insert(riskChargeVoice);
        }
    }



    public void dealTencentRecTask(String body){

        Map<String, Boolean> configMap = queryConfig();
        if(configMap != null && !configMap.get("recTask")){
            return;
        }
        JSONObject param = JSONObject.parseObject(body);
        //判断当前时间距离是否超过24h
        Date startTime = param.getDate("startTime");
        String orderId = param.getString("orderId");
        if(DateUtil.addHour(startTime,24).before(new Date())){
            log.info("[][][][]当前订单已过24h，不再监听{}", orderId);
            return;
        }
        sfcOrderMapper = SpringContextUtil.getBean("sfcOrderMapper");
        SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(orderId);
        if(sfcOrder.getStatus() > 20){
            log.info("[][][][]当前订单非已接单，不再监听{}", JsonUtils.json(sfcOrder));
            return;
        }
        sfcSupplierOrderService = SpringContextUtil.getBean("sfcSupplierOrderService");
        SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getByOrderId(sfcOrder.getOrderId(), sfcOrder.getSupplierOrderId());
        if(StringUtils.isBlank(sfcSupplierOrder.getVirtualPhone())){
            log.info("[][][][{}]当前订单无虚拟号，退出监听",orderId);
            return;
        }
        virtualPhoneRecordService = SpringContextUtil.getBean("virtualPhoneRecordService");
        List<VirtualPhoneRecordRsp.PhoneData> phoneDataList = virtualPhoneRecordService.getRecordId(sfcSupplierOrder.getVirtualPhone(), sfcOrder.getOrderId());
        mqRiskProducer = SpringContextUtil.getBean("riskSecurityProducer");
        if(CollectionUtils.isEmpty(phoneDataList)){
            //空的话直接下次循环
            mqRiskProducer.send(MqTagEnum.car_risk_self_security_task,body, DateUtil.addMinute(new Date(),5).getTime());
            log.info("[][][][]当前订单未查到通话记录，发送下次监听消息{}", JsonUtils.json(sfcOrder));
            return;
        }
        mqRiskProducer.send(MqTagEnum.car_risk_self_security_task,body, DateUtil.addMinute(new Date(),5).getTime());
        List<String> downLoadUrl = new ArrayList<>();
        Map<String,String> videoAndUrlMap = new HashMap<>();
        //查询缓存,这边存的是callId
        redissonClient = SpringContextUtil.getBean("riskProcessRedisson");
        RBucket<String> recordList = redissonClient.getBucket("virtualPhone:record:order:" + sfcOrder.getOrderId());
        log.info("[][][][]司机接单后监听通话记录{}",JsonUtils.json(recordList.get()));
        if(recordList != null && recordList.isExists() && StringUtils.isNotBlank(recordList.get())){
            List<String> callIdList = new ArrayList<>(Arrays.asList(recordList.get().split(",")));
            List<VirtualPhoneRecordRsp.PhoneData> phoneRequestList = new ArrayList<>();
            for(VirtualPhoneRecordRsp.PhoneData phoneData :phoneDataList){
                if(callIdList.contains(phoneData.getCallId())){
                    //已经存过了callId,就继续下一个
                    continue;
                }
                //开始请求录音地址
                phoneRequestList.add(phoneData);
            }
            videoAndUrlMap = virtualPhoneRecordService.queryDownLoadUrl(phoneRequestList);
            log.info("[][][][{}]获取录音文件下载链接1{}",orderId,JsonUtils.json(videoAndUrlMap));
            if(videoAndUrlMap == null || videoAndUrlMap.isEmpty()){
                //暂未获取到录音，不放入缓存，下面再试
                return;
            }
            List<String> collect = phoneRequestList.stream().map(VirtualPhoneRecordRsp.PhoneData::getCallId).collect(Collectors.toList());
            callIdList.addAll(collect);
            recordList.set(StringUtils.join(callIdList.stream().distinct().collect(Collectors.toList()),","),3, TimeUnit.DAYS);
        } else {
            videoAndUrlMap = virtualPhoneRecordService.queryDownLoadUrl(phoneDataList);
            log.info("[][][][{}]获取录音文件下载链接2{}",orderId,JsonUtils.json(videoAndUrlMap));
            if(videoAndUrlMap == null || videoAndUrlMap.isEmpty()){
                //暂未获取到录音，不放入缓存，下面再试
                return;
            }
            recordList.set(StringUtils.join(phoneDataList.stream().map(VirtualPhoneRecordRsp.PhoneData::getCallId).collect(Collectors.toList()),","),3, TimeUnit.DAYS);
        }
        //请求腾讯给他们
        tencentCloudApiClient = SpringContextUtil.getBean("tencentCloudApiClient");
        for(Map.Entry<String,String> entry : videoAndUrlMap.entrySet()){
            Long recTaskId = tencentCloudApiClient.createRecTask(0,entry.getValue());
            JSONObject taskJsonObject = new JSONObject();
            taskJsonObject.put("taskId",recTaskId);
            taskJsonObject.put("times",1);
            taskJsonObject.put("unionId",sfcOrder.getUnionId());
            taskJsonObject.put("memberId",String.valueOf(sfcOrder.getMemberId()));
            taskJsonObject.put("orderId",sfcOrder.getOrderId());
            taskJsonObject.put("driverCardNo",sfcSupplierOrder.getPlateNumber());
            taskJsonObject.put("source",2);
            taskJsonObject.put("callId",entry.getKey());
            if(recTaskId != null){
                //发送获取结果mq
                mqRiskProducer.send(MqTagEnum.car_self_tencent_get_describe_task,JsonUtils.json(taskJsonObject),DateUtil.addMinute(new Date(),5).getTime());
            }
        }
//        for(String url : downLoadUrl){
//            Long recTaskId = tencentCloudApiClient.createRecTask(0,url);
//            JSONObject taskJsonObject = new JSONObject();
//            taskJsonObject.put("taskId",recTaskId);
//            taskJsonObject.put("times",1);
//            taskJsonObject.put("unionId",sfcOrder.getUnionId());
//            taskJsonObject.put("memberId",String.valueOf(sfcOrder.getMemberId()));
//            taskJsonObject.put("orderId",sfcOrder.getOrderId());
//            taskJsonObject.put("driverCardNo",sfcSupplierOrder.getPlateNumber());
//            taskJsonObject.put("source",2);
//            if(recTaskId != null){
//                //发送获取结果mq
//                mqRiskProducer.send(MqTagEnum.car_self_tencent_get_describe_task,JsonUtils.json(taskJsonObject),DateUtil.addMinute(new Date(),5).getTime());
//            }
//        }
    }
}