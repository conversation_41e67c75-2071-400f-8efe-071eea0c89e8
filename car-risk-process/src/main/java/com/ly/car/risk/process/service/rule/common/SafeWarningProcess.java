package com.ly.car.risk.process.service.rule.common;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class SafeWarningProcess implements Policy{

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @Override
    public UiResult execute(FilterParams params) {
        RMap<String, String> map = redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING + params.getOrderId());
        if(map == null || map.isEmpty()){
            return UiResult.ok();
        }
        List<SafeWarningResult> returnResult = new ArrayList<>();
        for(Map.Entry<String,String> entry : map.entrySet()){
            SafeWarningResult result = new SafeWarningResult();
            result.setRuleNo(entry.getKey());
            result.setHitTime(entry.getKey().split(",")[0]);
            result.setText(entry.getKey().split(",")[1]);
            String date = DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD);
            Date sixDate = DateUtil.string2Date(date+" 06:00:00");
            Date twentyDate = DateUtil.string2Date(date + "20:00:00");
            if(entry.getKey().equals("aq002")){
                if(new Date().after(sixDate) && new Date().before(twentyDate)){
                    result.setSort(1);
                } else {
                    result.setSort(2);
                }
            }
            if(entry.getKey().equals("aq001")){
                if(new Date().after(sixDate) && new Date().before(twentyDate)){
                    result.setSort(2);
                } else {
                    result.setSort(1);
                }
            }
            if(entry.getKey().equals("aq003")){
                result.setSort(3);
            }
            if(entry.getKey().equals("aq004")){
                result.setSort(4);
            }
            returnResult.add(result);
        }
        RiskResultDTO dto = new RiskResultDTO(1,"风控不通过",null,null);
        dto.setObj(returnResult);
        return UiResult.ok(dto);
    }
}
