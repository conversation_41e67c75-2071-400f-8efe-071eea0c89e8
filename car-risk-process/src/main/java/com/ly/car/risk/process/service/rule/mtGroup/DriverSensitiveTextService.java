package com.ly.car.risk.process.service.rule.mtGroup;

import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.service.dto.SensitiveWordsSimple;
import com.ly.car.risk.process.utils.SensitiveWordUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
@Scope("prototype")
public class DriverSensitiveTextService extends MtFilterHandler{

    @Resource
    private MqSendConvertService mqSendConvertService;


    @Override
    public void doHandler(MtFilterContext context) {
        log.info("[][][][]司机端敏感词识别{}",context.getParam().get("text"));
        RiskResultNewDTO dto = new RiskResultNewDTO();
        Map<String,Object> data = new HashMap<>();
        Map<String,Object> resultMap = new HashMap<>();
        data.put("driverId",context.getParam().get("driverId"));
        data.put("idCard",context.getParam().getString("idCard"));
        dto.setObj(data);
        Object text = context.getParam().get("text");
        if(text != null){
            Map<String, SensitiveWordsSimple> wordsNumMap = SensitiveWordUtil.matchWords(context.getParam().getString("text"));
            if(!wordsNumMap.isEmpty()){
                //发送mq
                dto.setCode(1);
                dto.setMessage("认证失败，需更换照片");
                resultMap.put("sensitiveWord",1);
                data.put("resultMap",resultMap);
                dto.setObj(data);
                mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
                return;
            }
        }
        if(this.nextHandler == null && dto.getCode() == 0){
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
        } else {
            this.nextHandler.doHandler(context);
        }
    }
}
