package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/*2-1*/
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk018Service extends FilterSfcHandler{

    private static final String ruleNo = "018";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        if(context.getRuleOnOff().get(ruleNo) == null || !context.getRuleOnOff().get(ruleNo)){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
            return;
        }
        log.info("[FilterRuleChain][RuleRisk018Service][][]顺风车进入规则018判断{}", JsonUtils.json(context));
        //计算开始时间戳
        List<OrderRiskContext> orderRiskContextList = context.getDriverContextList().stream().filter(data->data.getIntervalTime() != null)
                .filter(data->data.getIntervalTime() < context.getSfcRiskRuleConfig().getTime018())
                .filter(data-> DateUtil.string2Date(data.getFinishTime()).after(TimeUtil.currentDay()))
                .collect(Collectors.toList());
        if((!CollectionUtils.isEmpty(orderRiskContextList)) && orderRiskContextList.size() >= context.getSfcRiskRuleConfig().getOrderNum018()){
            distributionRiskManageService.addByRuleChain(orderRiskContextList,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
            log.info("[RuleRisk018Service][doHandler][][]命中018规则{}",JsonUtils.json(orderRiskContextList));
            RiskResultDTO dto = new RiskResultDTO(405,"风控不通过018",null,null);
            context.getUiResult().setData(dto);
            context.getUiResult().setMsg("风控不通过");
            if(StringUtils.isBlank(context.getRuleNo())){
                context.setRuleNo(ruleNo);
            } else {
                context.setRuleNo(context.getRuleNo() + "," + ruleNo);
            }
            List<String> orderIds = orderRiskContextList.stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList());
            riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
