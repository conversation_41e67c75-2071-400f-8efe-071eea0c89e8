package com.ly.car.risk.process.controller;

import com.ly.car.risk.process.controller.params.CommonParams;
import com.ly.car.risk.process.service.OrderDataService;
import com.ly.dal.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

@RestController
@RequestMapping("/data")
public class DcdbOrderDataController {

    @Resource
    OrderDataService orderDataService;

    @RequestMapping("/orderRisk")
    public String setOrderRisk(@RequestBody CommonParams params){
        if(StringUtils.isNotBlank(params.getStartTime())){
            Date startDate = DateUtil.string2Date(params.getStartTime(),DateUtil.DATE_PATTERN_YYYY_MM_DD);
            Date endDate = DateUtil.string2Date(params.getEndTime(),DateUtil.DATE_PATTERN_YYYY_MM_DD);
            while (!startDate.after(endDate)){
                String startDateTime = DateUtil.date2String(startDate).split(" ")[0] + " 00:00:00";
                String endDateTime = DateUtil.date2String(startDate).split(" ")[0]+" 23:59:59";
                orderDataService.dealOrderRisk(startDateTime,endDateTime);
                startDate = DateUtil.addDay(startDate,1);
            }
            return "success";
        } else {
            //这边每小时跑一次就好了
            String nowDate = DateUtil.date2String(DateUtil.addHour(new Date(),-1));
            String splitTime = nowDate.split(":")[0];
            String startTime = splitTime+":00:00";
            String endTime = splitTime+":59:59";
            orderDataService.dealOrderRisk(startTime,endTime);
            return "success";
        }

    }

}
