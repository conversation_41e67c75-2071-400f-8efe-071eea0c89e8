package com.ly.car.risk.process.supplier.yueyue;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class YueYueAppealParam {

    private String app_id;    //约分配给合作方的唯⼀标识
    private String timestamp;    //请求发送时的秒级时间戳

    private String tenant_id;//租户id
    private Long driverId;//司机id
    private OrderDTO order_info;//渠道订单
    private ViolationDTO violation_info;//渠道违规
    private AppealDTO appeal_info;



    @Data
    public static class OrderDTO{
        private String channel_main_order_id;
        private String channel_order_id;
        private String order_id;
//        private String orderType;
    }

   /**
    * 渠道违规
    * */
    @Data
    public static class ViolationDTO{
        private String violation_id;
        private ViolationAppeal violation_appeal;
    }

    /**
     * 违规申诉信息
     * */
    @Data
    public static class ViolationAppeal{
        private String can_appeal;
        private String can_not_appeal_reason;
        private Integer now_appeal_num;
        private Integer max_appeal_num;
        private Long appeal_end_time;
    }

//    /**
//     * 渠道处置
//     * */
//    @Data
//    public static class DisposeDTOs{
//        private String disposeId;
//        private String disposeStatus;
//        private String refundStatus;//退款状态
//        private Long refundTime;
//        private BigDecimal refundAmount;
//    }

    /**
     * 申诉记录
     * */
    @Data
    public static class AppealDTO{
        private String appeal_id;
        private String appeal_status;//申诉状态（枚举）
        private String appeal_result;//申诉结果说明
        private List<String> appeal_result_photos;//申诉结果凭证图片
        private BigDecimal appeal_amount;//申诉金额
    }
}
