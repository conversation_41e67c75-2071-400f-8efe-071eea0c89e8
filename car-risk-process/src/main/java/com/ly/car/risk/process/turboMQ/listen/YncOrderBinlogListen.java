package com.ly.car.risk.process.turboMQ.listen;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.turboMQ.consumer.BingLogSfcOrderInfoConsumer;
import com.ly.car.risk.process.turboMQ.consumer.BingLogYncOrderInfoConsumer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
/**
 * 监听shardingorder的专车订单表
 * */
@Service
@Slf4j
public class YncOrderBinlogListen implements ApplicationListener<ApplicationStartedEvent>, DisposableBean {

    private static final String GROUP = "risk_group_binglog_yncOrderInfo";
    private static final String TOPIC = "TECarShardingOrder_order_info";

    @Resource
    private UrlsProperties urlsProperties;
    
    private DefaultMQPushConsumer consumer;
    
    @SneakyThrows
    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        if(!urlsProperties.getBigDataMqServerStart()){
            return;
        }
        consumer = new DefaultMQPushConsumer(GROUP);
        consumer.setNamesrvAddr(urlsProperties.getBigDataMqServer());
        //每次拉去N条消息
        consumer.setConsumeMessageBatchMaxSize(1024);
        //设置消费模式=集群
        consumer.setMessageModel(MessageModel.CLUSTERING);
        //订阅PushTopic下tag为push的消息
        consumer.subscribe(TOPIC,"");
        consumer.registerMessageListener(new BingLogYncOrderInfoConsumer());
        consumer.start();
        log.info("[MqStartupRunner][initTurboConsumer][YncOrderConsumer][]启动tuborMQ消费者-专车订单");
    }
    
    @Override
    public void destroy() {
        if(null != consumer){
            consumer.shutdown();
        }
    }
}
