package com.ly.car.risk.process.constants;

import org.apache.commons.lang3.StringUtils;


public enum RiskCustomerTtlEnum {

    one(1, "1天"),
    seven(7, "7天"),
    thirty(30, "一个月"),
    one_year(365, "一年"),
    forever(-1, "永久"),
    ;
    private Integer code;
    private String msg;

    RiskCustomerTtlEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (RiskCustomerTtlEnum enumItem : RiskCustomerTtlEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
    public static Integer getCodeByMsg(String msg) {
        for (RiskCustomerTtlEnum enumItem : RiskCustomerTtlEnum.values()) {
            if (enumItem.getMsg().equals(msg)) {
                return enumItem.getCode();
            }
        }
        return null;
    }
}
