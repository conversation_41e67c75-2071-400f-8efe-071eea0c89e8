package com.ly.car.risk.process.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.order.entity.OrderCoupon;
import com.ly.car.order.entity.OrderWxCard;
import com.ly.car.order.entity.SfcCouponRecord;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.common.constants.CommonConstants;
import com.ly.car.risk.process.client.SupplierApiClient;
import com.ly.car.risk.process.constants.ChannelEnum;
import com.ly.car.risk.process.constants.NewRiskOrderTypeEnum;
import com.ly.car.risk.process.constants.RiskTypeEnum;
import com.ly.car.risk.process.constants.RuleNoMapRiskTypeEnum;
import com.ly.car.risk.process.repo.order.mapper.OrderCouponMapper;
import com.ly.car.risk.process.repo.order.mapper.OrderWxCardMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcCouponRecordMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderAddressMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.DriverCheatSyncOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverCheatSyncOrder;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.service.dto.RiskOrderRsp;
import com.ly.car.risk.process.service.dto.order.BaseOrderInfo;
import com.ly.car.risk.process.service.dto.order.CarInfo;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.dto.order.CarPassenger;
import com.ly.car.risk.process.service.dto.order.OrderTripInfo;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.sfc.SfcSupplierOrderService;
import com.ly.car.risk.process.utils.AmountUtil;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderDriver;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RiskOrderManageService {

    public static List<String> riskOrderRuleList = Arrays.asList("007","008","012","013","015","016","017","006"
            ,"018","025","026","027","028","029","032","034","035","036","037","zc-001","zc-002","zc-007","zc-011");

    @Resource
    private OrderInfoMapper    orderInfoMapper;
    @Resource
    private SupplierApiClient  supplierApiClient;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;
    @Resource
    private RiskOrderRecordService riskOrderRecordService;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private SfcOrderAddressMapper sfcOrderAddressMapper;
    @Resource
    private SfcCouponRecordMapper sfcCouponRecordMapper;
    @Resource
    private SfcSupplierOrderService sfcSupplierOrderService;
    @Resource
    private OrderDriverMapper orderDriverMapper;
    @Resource
    private OrderWxCardMapper orderWxCardMapper;
    @Resource
    private OrderCouponMapper orderCouponMapper;
    @Resource
    private SupplierWorkOrderService supplierWorkOrderService;
    @Resource
    private VirtualPhoneRecordService virtualPhoneRecordService;
    @Resource
    private DriverCheatSyncOrderMapper driverCheatSyncOrderMapper;
    @Resource
    private CarOrderService carOrderService;


    public static void main(String[] args) {
        System.out.println(RiskTypeEnum.BRUSH_ORDER_EX.getCode());
    }

    public RiskOrderManage addRiskOrder(RiskOrderManage req) {
        log.info("[][][][]命中风险订单：{},规则编号{}", JsonUtils.json(req),req.getRuleNo());
        List<String> ruleList = new ArrayList<>();
        if(StringUtils.isNotBlank(req.getRuleNo())){
            List<String> reqRuleNoList = Arrays.asList(req.getRuleNo().split(","));
            //先看下当前命中规则的是否是
            boolean flag = false;
            for(String str : reqRuleNoList){
                if(riskOrderRuleList.contains(str) || StringUtils.lowerCase(str).startsWith(CommonConstants.OFFLINE_STRATEGY_PREFIX)){
                    flag = true;
                }
            }
            if(!flag){
                return null;
            }
            ruleList = reqRuleNoList;
        }
        try {
            RiskOrderManage riskOrderManage = riskOrderManageMapper.selectOne(
                    new QueryWrapper<RiskOrderManage>().eq("order_id",req.getOrderId())
            );
            //更新
            if(riskOrderManage != null){
                String ruleNo = riskOrderManage.getRuleNo();
                for(String str : ruleList){
                    if(StringUtils.isNotBlank(ruleNo) && !ruleNo.contains(str) ){
                        ruleNo = ruleNo + ","+str;
                    }
                    if(StringUtils.isBlank(ruleNo)){
                        ruleNo = str;
                    }
                }
                if ((riskOrderManage.getSurcharge() == null || riskOrderManage.getSurcharge().compareTo(BigDecimal.ZERO) == 0) && OrderUtils.isNewOrder(req.getOrderId())) {
                    CarOrderDetail orderDetail = carOrderService.queryOrderDetail(req.getOrderId());
                    if (null != orderDetail) {
                        riskOrderManage.setSurcharge(orderDetail.getBaseInfo().getSurcharge());
                    }
                }
                riskOrderManage.setRuleNo(ruleNo);
                riskOrderManageMapper.updateById(riskOrderManage);
                //插入操作记录
                riskOrderRecordService.insertRecord(req.getOrderId(), req.getRuleNo(), req.getIsRisk(), "系统");
                return null;
            }
            if(StringUtils.isNotBlank(req.getRuleNo())){
                RuleNoMapRiskTypeEnum descByRuleNo = RuleNoMapRiskTypeEnum.getDescByRuleNo(req.getRuleNo().split(",")[0]);
                if(descByRuleNo != null){
                    req.setRiskType(descByRuleNo.riskType);
                } else if(req.getRiskType() != null) {
                    req.setRiskType(req.getRiskType());
                }
            }

            // 如果是新订单流程
            if(OrderUtils.isNewOrder(req.getOrderId())){
                return dealNewOrderAddRiskManager(req);
            }

            //新增
            log.info("[][][][]命中风险订单-开始新增");
            RiskOrderManage orderManage = new RiskOrderManage();
            BeanUtils.copyProperties(req, orderManage);
            orderManage.setRiskTypeName(NewRiskOrderTypeEnum.getMsgByCode(req.getRiskType()));
            orderManage.setCreateTime(new Date());
            orderManage.setOperateTime(new Date());
            orderManage.setIsRisk(1);
            if(req.getRiskType() == null || req.getRiskType() == 0){
                orderManage.setRiskType(NewRiskOrderTypeEnum.BRUSH_ORDER_EX.getCode());
                orderManage.setRiskTypeName(NewRiskOrderTypeEnum.getMsgByCode(NewRiskOrderTypeEnum.BRUSH_ORDER_EX.getCode()));
            } else {

                orderManage.setRiskType(req.getRiskType());
                orderManage.setRiskTypeName(NewRiskOrderTypeEnum.getMsgByCode(req.getRiskType()));
            }

            orderManage.setRuleNo(req.getRuleNo());
            //通过订单查询所有信息
            if(req.getOrderId().startsWith("SFC")){
                orderManage.setProductLine("SFC");
                SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(req.getOrderId());
                List<SfcCouponRecord> sfcCouponRecord = sfcCouponRecordMapper.getListByOrderId(sfcOrder.getOrderId());
                if(sfcOrder != null){
                    if(sfcOrder.getSupplierCode().startsWith("MadaSaas")){
                        sfcOrder.setSupplierCode("MadaSaas");
                    }
                    orderManage.setMemberId(String.valueOf(sfcOrder.getMemberId()));
                    orderManage.setUnionId(sfcOrder.getUnionId());
                    orderManage.setSupplierCodeFull(sfcOrder.getSupplierCode());
//                orderManage.setDistributeOrderId(sfcOrder.get());
                    orderManage.setSupplierCode(sfcOrder.getSupplierCode());
                    orderManage.setChannelId(String.valueOf(sfcOrder.getRefId()));
                    orderManage.setChannelName(ChannelEnum.getMsgByCode(String.valueOf(sfcOrder.getRefId())));
                    orderManage.setSupplierName(supplierApiClient.getSupplierCodeMap().get(sfcOrder.getSupplierCode()));
                    orderManage.setSupplierOrderId(sfcOrder.getSupplierOrderId());
                    orderManage.setEnv(sfcOrder.getEnv().equals("PROD")?"PROD":"STAGE");
                    orderManage.setFinishTime(sfcOrder.getFinishTime());
                    orderManage.setTotalAmount(AmountUtil.changeF2Y(sfcOrder.getTotalAmount()));
                }
                OrderAddress orderAddress = orderAddressMapper.findByOrderId(req.getOrderId());
                if(orderAddress != null){
                    orderManage.setCityId(orderAddress.getStartCityId());
                    orderManage.setCityName(orderAddress.getStartCityName());
                    orderManage.setActualKilo(orderAddress.getEstimateKilo());
                    orderManage.setActualDuration(orderAddress.getActualMinute());
                }
                if(!CollectionUtils.isEmpty(sfcCouponRecord)){
                    List<String> couponList = sfcCouponRecord.stream().map(SfcCouponRecord::getCardId).collect(Collectors.toList());
                    List<String> couponNameList = sfcCouponRecord.stream().map(SfcCouponRecord::getTitle).collect(Collectors.toList());
                    orderManage.setCouponName(StringUtils.join(couponNameList,","));
                    orderManage.setCouponBatchNo(StringUtils.join(couponList,","));
                    orderManage.setCouponAmount(sfcCouponRecord.stream().map(SfcCouponRecord::getReduceCost).reduce(BigDecimal.ZERO,BigDecimal::add));
                }
                if(StringUtils.isBlank(sfcOrder.getSupplierOrderId())){
                    SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getByOrderId(sfcOrder.getOrderId(),sfcOrder.getSupplierOrderId());
                    if(sfcSupplierOrder != null){
                        orderManage.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
                    }
                }

            } else {
                orderManage.setProductLine("YNC");
                OrderInfo orderInfo = orderInfoMapper.findByOrderId(req.getOrderId());
                if(orderInfo == null){
                    log.info("[][][][]命中风险订单-查询订单信息异常");
                    return null;
                }
                OrderAddress orderAddress = orderAddressMapper.findByOrderId(req.getOrderId());
                if(orderAddress == null){
                    log.info("[][][][]命中风险订单-查询订单地址异常");
                    return null;
                }
                OrderDriver orderDriver = orderDriverMapper.findByOrderId(orderInfo.getOrderId());
                orderManage.setMemberId(orderInfo.getMemberId());
                orderManage.setUnionId(orderInfo.getUniId());
                orderManage.setEnv(orderInfo.getEnv());
                orderManage.setDistributeOrderId(orderInfo.getDistributorOrderId());
                orderManage.setChannelId(String.valueOf(orderInfo.getRefId()));
                orderManage.setChannelName(ChannelEnum.getMsgByCode(String.valueOf(orderInfo.getRefId())));
                orderManage.setSupplierCode(orderInfo.getSupplierCode());
                orderManage.setSupplierName(supplierApiClient.getSupplierCodeMap().get(orderInfo.getSupplierCode()));
                orderManage.setSupplierOrderId(orderInfo.getSupplierOrderId());
                orderManage.setFinishTime(orderInfo.getFinishTime());
                orderManage.setTotalAmount(orderInfo.getTotalAmount());
                orderManage.setSupplierCodeFull(orderInfo.getSupplierCodeFull());
                if(StringUtils.isNotBlank(orderInfo.getSupplierCodeFull())){
                    orderManage.setSupplierName(supplierApiClient.getSupplierCodeMap().get(orderInfo.getSupplierCodeFull()));
                }
                if(orderAddress != null){
                    orderManage.setCityId(orderAddress.getStartCityId());
                    orderManage.setCityName(orderAddress.getStartCityName());
                    orderManage.setActualKilo(orderAddress.getActualKilo());
                    orderManage.setActualDuration(orderAddress.getActualMinute());
                }
                if(orderDriver != null){
                    orderManage.setDriverCardNo(orderDriver.getPlateNumber());
                }
                //先查微信优惠券
                List<OrderWxCard> orderWxCards = orderWxCardMapper.selectList(
                        new QueryWrapper<OrderWxCard>().eq("order_id",req.getOrderId())
                );
                if(!CollectionUtils.isEmpty(orderWxCards)){
                    List<String> couponNameList = orderWxCards.stream().map(OrderWxCard::getTitle).collect(Collectors.toList());
                    List<String> couponList = orderWxCards.stream().map(OrderWxCard::getCardId).collect(Collectors.toList());
                    orderManage.setCouponName(StringUtils.join(couponNameList,","));
                    orderManage.setCouponBatchNo(StringUtils.join(couponList,","));
                    orderManage.setCouponAmount(orderWxCards.stream().map(OrderWxCard::getReduceCost).map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add));
                } else {
                    //查app优惠券
                    List<OrderCoupon> orderCoupons = orderCouponMapper.selectList(
                            new QueryWrapper<OrderCoupon>().eq("order_id",req.getOrderId())
                    );
                    List<String> couponNameList = orderCoupons.stream().map(OrderCoupon::getBatchName).collect(Collectors.toList());
                    List<String> couponList = orderCoupons.stream().map(OrderCoupon::getBatchNo).collect(Collectors.toList());
                    orderManage.setCouponName(StringUtils.join(couponNameList,","));
                    orderManage.setCouponBatchNo(StringUtils.join(couponList,","));
                    orderManage.setCouponAmount(orderCoupons.stream().map(OrderCoupon::getCouponAmount).map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add));
                }
                //同步工单
                supplierWorkOrderService.saveWorkOrder(orderInfo,orderDriver, req.getRuleNo(),null);
            }
            orderManage.setOperateTime(new Date());
            orderManage.setCreateTime(new Date());
            orderManage.setOperateUser("系统");
            orderManage.setJudgeTime(new Date());
            riskOrderManageMapper.insert(orderManage);
            //插入操作记录
            riskOrderRecordService.insertRecord(req.getOrderId(), req.getRuleNo(), req.getIsRisk(), "系统");
            //插入虚拟号记录
            virtualPhoneRecordService.savePhoneRecord(req.getOrderId());
            log.info("[][][][]命中风险订单-新增结束");
            return orderManage;
        } catch (Exception e) {
            log.error("[][][][]风险订单添加:",e);
        }
        return null;
    }

    private RiskOrderManage dealNewOrderAddRiskManager(RiskOrderManage req) {
        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(req.getOrderId());
        if(null == orderDetail){
            return null;
        }
        BaseOrderInfo baseInfo = orderDetail.getBaseInfo();
        OrderTripInfo orderTrip = orderDetail.getOrderTrip();
        CarInfo carInfo = orderDetail.getCarInfo();
        CarPassenger passengerInfo = orderDetail.getPassengerInfo();

        //新增
        log.info("[][][][]命中风险订单-开始新增");
        RiskOrderManage orderManage = new RiskOrderManage();
        BeanUtils.copyProperties(req, orderManage);
        orderManage.setRiskTypeName(NewRiskOrderTypeEnum.getMsgByCode(req.getRiskType()));
        orderManage.setCreateTime(new Date());
        orderManage.setOperateTime(new Date());
        orderManage.setIsRisk(1);
        if(req.getRiskType() == null || req.getRiskType() == 0){
            orderManage.setRiskType(NewRiskOrderTypeEnum.BRUSH_ORDER_EX.getCode());
            orderManage.setRiskTypeName(NewRiskOrderTypeEnum.getMsgByCode(NewRiskOrderTypeEnum.BRUSH_ORDER_EX.getCode()));
        } else {

            orderManage.setRiskType(req.getRiskType());
            orderManage.setRiskTypeName(NewRiskOrderTypeEnum.getMsgByCode(req.getRiskType()));
        }

        orderManage.setRuleNo(req.getRuleNo());
        orderManage.setProductLine(OrderUtils.getProductLineByOrderPrefix(req.getOrderId()));
        orderManage.setMemberId(orderDetail.getMemberId());
        orderManage.setUnionId(orderDetail.getUnionId());
        orderManage.setSupplierCodeFull(carInfo.getSupplierFullCode());
        orderManage.setSupplierCode(carInfo.getSupplierCode());
        orderManage.setChannelId(baseInfo.getRefId());
        orderManage.setChannelName(ChannelEnum.getMsgByCode(baseInfo.getRefId()));
        orderManage.setSupplierName(carInfo.getSupplierName());
        orderManage.setSupplierOrderId(orderDetail.getSupplierOrderId());
        orderManage.setEnv(Objects.equals("PROD", StringUtils.upperCase(orderDetail.getEnv()))?"PROD":"STAGE");
        orderManage.setFinishTime(baseInfo.getGmtTripFinished());
        orderManage.setTotalAmount(new BigDecimal(baseInfo.getAmount()));
        orderManage.setCityId(orderTrip.getDepartureCityCode());
        orderManage.setCityName(orderTrip.getDepartureCityName());
        orderManage.setActualKilo(orderTrip.getOldRealKilo());
        orderManage.setActualDuration(orderTrip.getOldRealMinute());
        String couponNameList = orderDetail.getUsedCoupons().stream().map(p -> p.getProductName()).collect(Collectors.joining(","));
        orderManage.setCouponName(couponNameList);
        orderManage.setCouponAmount(orderDetail.getUsedCoupons().stream().filter(p->StringUtils.isNotBlank(p.getRealPrice())).map(p->new BigDecimal(p.getRealPrice())).reduce(BigDecimal.ZERO,BigDecimal::add));
        orderManage.setDriverCardNo(carInfo.getCarNum());
        orderManage.setDistributeOrderId(StringUtils.EMPTY);
        orderManage.setSurcharge(baseInfo.getSurcharge());

        //通过订单查询所有信息
        if(!OrderUtils.isSFC(req.getOrderId())){

            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderId(orderDetail.getOrderId());
            orderInfo.setSupplierOrderId(orderDetail.getSupplierOrderId());
            orderInfo.setSupplierCode(carInfo.getSupplierCode());
            orderInfo.setFinishTime(baseInfo.getGmtTripFinished());
            OrderDriver orderDriver = new OrderDriver();
            orderDriver.setPlateNumber(carInfo.getCarNum());

            //同步工单
            supplierWorkOrderService.saveWorkOrder(orderInfo,orderDriver, req.getRuleNo(),baseInfo.getRealSupplierPrice());
        }
        orderManage.setOperateTime(new Date());
        orderManage.setCreateTime(new Date());
        orderManage.setOperateUser("系统");
        orderManage.setJudgeTime(new Date());
        riskOrderManageMapper.insert(orderManage);
        //插入操作记录
        riskOrderRecordService.insertRecord(req.getOrderId(), req.getRuleNo(), req.getIsRisk(), "系统");
        //插入虚拟号记录 新订单走原表查不到虚拟号
//        virtualPhoneRecordService.savePhoneRecord(req.getOrderId());
        log.info("[][][][]命中风险订单-新增结束");
        return orderManage;
    }

    public UiResult queryRiskOrder(String orderId, String productLine, List<String> orderIds){
        if(StringUtils.isBlank(productLine) && StringUtils.isNotBlank(orderId)){
            RiskOrderManage manage = this.riskOrderManageMapper.selectOne(new QueryWrapper<RiskOrderManage>()
                    .eq("order_id", orderId)
            );
            RiskOrderRsp rsp = new RiskOrderRsp();
            if(manage != null){
                rsp.setIsPass(false);
                rsp.setReason(manage.getRemark());
                return UiResult.ok(rsp);
            } else {
                rsp.setIsPass(true);
            }
            if(DateUtil.string2Date("2023-07-01 15:00:00").after(new Date())){
                rsp.setIsPass(true);
                rsp.setReason(manage.getRemark());
                return UiResult.ok(rsp);
            }
            return UiResult.ok(rsp);
        }

        if(CollectionUtils.isNotEmpty(orderIds)){
            List<String> riskOrderIds = new ArrayList<>();
            if(productLine.equals("SFC")){
                List<RiskOrderManage> manageList = this.riskOrderManageMapper.selectList(new QueryWrapper<RiskOrderManage>()
                        .in("order_id", orderIds)
                );
                riskOrderIds = manageList.stream().map(RiskOrderManage::getOrderId).collect(Collectors.toList());
            }
            if(productLine.equals("HC")){
                List<DriverCheatSyncOrder> syncOrderList = this.driverCheatSyncOrderMapper.selectList(new QueryWrapper<DriverCheatSyncOrder>()
                        .in("order_id", orderIds)
                        .in("appeal_status", 0, 1, 3)
                );
                riskOrderIds = syncOrderList.stream().map(DriverCheatSyncOrder::getOrderId).collect(Collectors.toList());
            }
            log.info("[][][][]风险单对接{}",JsonUtils.json(riskOrderIds));
            return UiResult.ok(riskOrderIds);
        }
        return UiResult.ok();
    }


}
