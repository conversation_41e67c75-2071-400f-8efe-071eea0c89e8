package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 当天用户完单小于2公里的订单大于2单且完单司机车牌一致
 * 且用户近3天完单司机一致的订单大于等于7单
 * 2-2
 * */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk023Service extends FilterSfcHandler{

    private static final String ruleNo = "023";
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        if(context.getRuleOnOff().get(ruleNo) == null || !context.getRuleOnOff().get(ruleNo)){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
            return;
        }
        log.info("[FilterRuleChain][SfcRuleRisk023Service][][]前置判断已通过，进入规则023判断");
        //获取用户当天完单
        List<OrderRiskContext> orderRiskContextList = context.getUserContextList().stream()
                .filter(data-> DateUtil.string2Date(data.getFinishTime()).after(TimeUtil.currentDay()))
                .filter(data-> new BigDecimal(CoordUtil.getDistance(data.getStartLng(),data.getStartLat(),data.getEndLng(),data.getEndLat()))
                        .compareTo(new BigDecimal(context.getSfcRiskRuleConfig().getDistance023())) < 0)
                .collect(Collectors.toList());
        //获取用户近3天完单
        List<OrderRiskContext> orderRiskContextListThreeDay = context.getUserContextList().stream()
                .filter(data-> DateUtil.string2Date(data.getFinishTime()).after(TimeUtil.threeDay()))
                .collect(Collectors.toList());

        //判断完单是否大于2两单，小于等于两单直接下个规则
        if(!CollectionUtils.isEmpty(orderRiskContextList) && orderRiskContextList.size() > context.getSfcRiskRuleConfig().getOrderNum023()){
            //判断这些订单的司机是否一致
            List<String> stringListBefore = orderRiskContextList.stream()
                    .map(OrderRiskContext::getDriverCardNo)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> stringListAfter = orderRiskContextListThreeDay.stream()
                    .map(OrderRiskContext::getDriverCardNo)
                    .distinct()
                    .collect(Collectors.toList());
            String orderIds = StringUtils.join(stringListAfter,",");

            if(stringListBefore.size() == 1 && stringListAfter.size() == 1
                    && orderRiskContextListThreeDay.size() >= context.getSfcRiskRuleConfig().getOrderNumByDriver023()){
                log.info("[SfcRuleRisk023Service][doHandler][][]命中023规则，关联用户为{},关联订单为{}",context.getMemberId(), JsonUtils.json(orderIds));
                distributionRiskManageService.addByRuleChain(orderRiskContextList,ruleNo, context.getMainScene(), context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过023",null,null);
                context.getUiResult().setData(dto);
                context.getUiResult().setMsg("风控不通过");

                if(StringUtils.isBlank(context.getRuleNo())){
                    context.setRuleNo(ruleNo);
                } else {
                    context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                }
                List<String> linkOrderIds = orderRiskContextList.stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList());
                riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,linkOrderIds));
            }
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
