package com.ly.car.risk.process.controller;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.params.DriverBlackParam;
import com.ly.car.risk.process.service.BlackDriverService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("blackDriver")
@RestController
public class BlackDriverShieldController {

    @Resource
    private BlackDriverService blackDriverService;

    @RequestMapping("userShield")
    public UiResult userShield(@RequestBody DriverBlackParam param){
        return blackDriverService.userShield(param);
    }

    @RequestMapping("userRemove")
    public UiResult userRemove(@RequestBody DriverBlackParam param){
        return blackDriverService.userRemove(param);
    }

    @RequestMapping("getList")
    public UiResult getList(@RequestBody DriverBlackParam param){
        return this.blackDriverService.getList(param);
    }
    
    @RequestMapping("orderBlack")
    public UiResult orderBlack(@RequestBody DriverBlackParam param){
        return this.blackDriverService.orderBlack(param);
    }
}
