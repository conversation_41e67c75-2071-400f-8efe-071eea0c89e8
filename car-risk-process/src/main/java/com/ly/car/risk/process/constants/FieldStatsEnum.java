package com.ly.car.risk.process.constants;

import java.util.ArrayList;

public enum FieldStatsEnum {

    MEMBER_ID(0,"memberId(兼容memberId的指标)"),
    DRIVER_AND_USER(3,"车牌号&乘客(memberId)"),
    USER_ID(9,"用户id(优先unionId)"),
    USER_AND_DRIVER(1,"用户id&车牌号(加密)"),
    ;

    private Integer code;
    private String msg;

    FieldStatsEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public static String queryMsg(Integer code){
        for (FieldStatsEnum enumItem : FieldStatsEnum.values()) {
            if(enumItem.code.equals(code)){
                return enumItem.msg;
            }
        }
        return null;
    }


}
