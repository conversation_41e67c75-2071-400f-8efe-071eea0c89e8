package com.ly.car.risk.process.strategy.model.detail;

import lombok.Data;

/**
 * Description of RiskFieldDetail
 *
 * <AUTHOR>
 * @date 2024/6/4
 * @desc 指标
 */
@Data
public class RiskFieldDetail {
    private String fieldId;
    // 指标编号
    private String fieldNo;
    // 运算符
    private String operator;
    // 0-常量 1-特征
    private int rightType;
    // 对比阈值
    private String rightValue;

    // 特征指标动态代码
    private String script;
    // 指标分类
    private int category;
    // 指标类型
    private int type;
    // 指标主体
    private int target;
    // 排序
    private int sort;
    // 自检指标 1-基于当前订单 0-基于历史订单
    private Integer basedCurrent;
}