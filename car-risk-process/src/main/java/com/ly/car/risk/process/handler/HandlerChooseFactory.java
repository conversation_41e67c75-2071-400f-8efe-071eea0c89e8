package com.ly.car.risk.process.handler;

import cn.hutool.core.lang.Assert;
import com.ly.car.risk.process.handler.orderstate.AbstractOrderStateHandler;
import com.ly.car.risk.process.handler.riskSecurity.AbstractRiskSecurityHandler;
import com.ly.car.risk.process.handler.selfbin.AbstractSelfBinHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Description of OrderStateChooseFactory
 *
 * <AUTHOR>
 * @date 2024/3/13
 * @desc
 */
@Service(value = "handlerChooseFactory")
@Slf4j
public class HandlerChooseFactory implements InitializingBean, ApplicationContextAware {

    private ApplicationContext ctx;

    private final Map<String, AbstractOrderStateHandler> orderStateHandlerMap = new HashMap<>();

    private final Map<String, AbstractRiskSecurityHandler> riskSecurityHandlerMap = new HashMap<>();

    private final Map<String, AbstractSelfBinHandler> selfBinHandlerMap = new HashMap<>();

    public AbstractSelfBinHandler chooseSelfBinHandler(String orderOldNewFlag){
        Assert.notBlank(orderOldNewFlag);
        if(orderOldNewFlag.equalsIgnoreCase("new")){
            return selfBinHandlerMap.get("new");
        }
        return selfBinHandlerMap.get("old");
    }

    public AbstractRiskSecurityHandler chooseRiskSecurityHandler(String orderOldNewFlag){
        Assert.notBlank(orderOldNewFlag);
        if(orderOldNewFlag.equalsIgnoreCase("new")){
            return riskSecurityHandlerMap.get("new");
        }
        return riskSecurityHandlerMap.get("old");
    }

    public AbstractOrderStateHandler chooseOrderStateHandler(String orderType){
        Assert.notBlank(orderType);
        if(orderType.equalsIgnoreCase("SFC")){
            return orderStateHandlerMap.get("SFC");
        }
        return orderStateHandlerMap.get("YNC");
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        collectOrderStateHandler();
        collectRiskSecurityHandler();
        collectSelfBinHandler();
    }

    private void collectSelfBinHandler() {
        Map<String, AbstractSelfBinHandler> handlers = ctx.getBeansOfType(AbstractSelfBinHandler.class, false, true);
        for (AbstractSelfBinHandler handler : handlers.values()) {
            String supportType = handler.supportOrderType();
            selfBinHandlerMap.put(supportType,handler);
        }
    }

    private void collectRiskSecurityHandler() {
        Map<String, AbstractRiskSecurityHandler> handlers = ctx.getBeansOfType(AbstractRiskSecurityHandler.class, false, true);
        for (AbstractRiskSecurityHandler handler : handlers.values()) {
            String supportType = handler.supportOrderType();
            riskSecurityHandlerMap.put(supportType,handler);
        }
    }

    private void collectOrderStateHandler() {
        Map<String, AbstractOrderStateHandler> handlers = ctx.getBeansOfType(AbstractOrderStateHandler.class, false, true);

        for(AbstractOrderStateHandler handler : handlers.values()){
            String supportType  = handler.supportType();
            if(orderStateHandlerMap.containsKey(supportType)){
                continue;
            }
            orderStateHandlerMap.put(handler.supportType(),handler);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.ctx = applicationContext;
    }
}