package com.ly.car.risk.process;

import com.ly.car.risk.process.bean.properties.KafKaProperties;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

import javax.annotation.Resource;
import java.util.Properties;

/**
 * 为什么不使用spring-kafka？
 * <p>
 * 公司kafka集群太多，除非项目始终使用同一个kafka集群，否则无法使用集成的spring-kafka
 */
@Configuration
@EnableConfigurationProperties({KafKaProperties.class})
public class KafkaConfiguration {
    @Resource
    private KafKaProperties kafKaProperties;

    /**
     * 每个集群配置一个KafkaProducer
     *
     * @return
     */
    @Bean
    public KafkaProducer<String, String> szKafkaProducer() {
        Properties pro = new Properties();
        pro.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafKaProperties.getSzServer());
        pro.put(ProducerConfig.ACKS_CONFIG, "1"); //1:确保leader收到消息；0:消息发到发送缓冲区即可；-1/all:确保leader收到消息并且同步给follower
        pro.put(ProducerConfig.RETRIES_CONFIG, "1");
        pro.put(ProducerConfig.LINGER_MS_CONFIG, 3); //提高生产效率，尤其是在同步生产时
        pro.put(ProducerConfig.BATCH_SIZE_CONFIG, 1024 * 64);
        pro.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 64 * 1024 * 1024);
        pro.put(ProducerConfig.RECEIVE_BUFFER_CONFIG, 64 * 1024 * 1024);
        pro.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "snappy");  //推荐使用snappy或lz4格式压缩， 大流量下网络带宽容易成为瓶颈，发送前压缩消息；不影响消费，消费者会自动解压
        pro.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        pro.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return new KafkaProducer<>(pro);
    }

    /**
     * 为什么Scope是prototype？
     * <p>
     * 为每个Consumer逻辑实例化一个KafkaConsumer
     *
     * @return
     */
    @Bean
    @Scope("prototype")
    public KafkaConsumer<String, String> szKafkaConsumer() {
        Properties prop = new Properties();

        prop.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafKaProperties.getSzServer());
        prop.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);
        prop.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 1000);

        prop.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 32);
        prop.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest"); //若没有指定offset,默认从最后的offset(latest)开始；earliest表示从最早的offset开始
        prop.put(ConsumerConfig.GROUP_ID_CONFIG, kafKaProperties.getGroupName());
        prop.put(ConsumerConfig.CONNECTIONS_MAX_IDLE_MS_CONFIG, 180 * 1000L);

        prop.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        prop.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        return new KafkaConsumer<>(prop);
    }

}
