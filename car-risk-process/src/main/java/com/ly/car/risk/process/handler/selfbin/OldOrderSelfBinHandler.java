package com.ly.car.risk.process.handler.selfbin;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.process.api.*;
import com.ly.car.risk.process.api.rsp.LabelRsp;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.AutoCallRecordService;
import com.ly.car.risk.process.service.MqSendService;
import com.ly.car.risk.process.service.VirtualPhoneRecordService;
import com.ly.car.risk.process.service.ability.AutoCallService;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.redis.OrderIdScoredSortedSetService;
import com.ly.car.risk.process.service.redis.OrderPassengerCellPhone;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.service.sfc.SfcSupplierOrderService;
import com.ly.car.risk.process.task.SfcFinishOrCancelData;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.DateUtilRisk;
import com.ly.car.risk.process.utils.PriceUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Description of NewOrerSelfBinHandler
 *
 * <AUTHOR>
 * @date 2024/3/14
 * @desc
 */
@Service
@Slf4j
public class OldOrderSelfBinHandler extends AbstractSelfBinHandler{


    private SfcSupplierOrderService sfcSupplierOrderService;
    private SfcOrderMapper sfcOrderMapper;
    private RedissonClient redissonClient;
    private SaveScoredSortedSetService saveScoredSortedSetService;
    @Resource(name = "mtRiskProducer")
    private MqRiskProducer mqRiskProducer;
    private OrderAddressMapper orderAddressMapper;
    private TrafficClient trafficClient;
    private KafkaProducer<String, String> szKafkaProducer;
    private EsQueryClient esQueryClient;
    private MqSendService mqSendService;
    private CarOrderApi carOrderApi;
    private LabelClient labelClient;
    private AutoCallService autoCallService;
    private AutoCallRecordService autoCallRecordService;
    private TencentCloudApiClient tencentCloudApiClient;
    private VirtualPhoneRecordService virtualPhoneRecordService;
    private OrderIdScoredSortedSetService orderIdScoredSortedSetService;

    @Override
    public String supportOrderType() {
        return "old";
    }

    @Override
    public void doHandler(String body, String tag) {

        if (tag.equals(MqTagEnum.car_risk_binlog_sfc_cancel.name())) {
            dealSfcCancelLog(body);
        } else if (tag.equals(MqTagEnum.car_risk_safe_warning.name())) {
            dealPlaceOrder(body);
        } else if (tag.equals(MqTagEnum.car_risk_place_order_phone.name())) {
            dealCreateOrder(body);
        } else if (tag.equals(MqTagEnum.car_risk_convert_sfc_data.name())) {
            dealFlinkData(body);
        } else if (tag.equals(MqTagEnum.car_risk_order_event_topic.name())) {
            dealFlinkDataByJson(JSONObject.parseObject(body));
        } else if (tag.equals(MqTagEnum.car_risk_driver_ex_notify.name())) {
            mqSendService = SpringContextUtil.getBean("mqSendService");
            mqSendService.driverWarnNotify(body, null, 0L);
        } else if (tag.equals(MqTagEnum.car_risk_driver_mini_warn_notify.name())) {
            //直接调用顺风车发送短信接口
            dealMiniSms(body);
        } else if (tag.equals(MqTagEnum.car_risk_sfc_user_onCar.name())) {
            dealUserPassengerOnCar(body);
        }
    }


    public void sendDriverVirtualCheck(String orderId) {
        mqRiskProducer = SpringContextUtil.getBean("");
    }

    /**
     * 用户确认上车事件
     */
    public void dealUserPassengerOnCar(String orderId) {
        sfcOrderMapper = SpringContextUtil.getBean("sfcOrderMapper");
        SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(orderId);
        if (sfcOrder.getStatus() > 200) {
            return;
        }
        //获取敏感时间
        String currentDay = DateUtil.date2String(new Date(), DateUtil.DATE_PATTERN_YYYY_MM_DD);
        if (new Date().after(DateUtil.string2Date(currentDay + " 05:00:00"))
                && new Date().before(DateUtil.string2Date(currentDay + " 20:00:00:00"))) {
            log.info("[][][][]当前订单不为敏感时间订单，不走监听{}", sfcOrder.getOrderId());
            return;
        }
        if (sfcOrder.getDistributorFlag() == 1) {
            log.info("[][][][]当前订单为分销订单，不走监听{}", sfcOrder.getOrderId());
            return;
        }

        labelClient = SpringContextUtil.getBean("labelClient");
        LabelRsp.DetailRsp detailRsp = labelClient.queryLabel(sfcOrder.getPlatId(),String.valueOf(sfcOrder.getMemberId()), sfcOrder.getUnionId());
        if (detailRsp != null && detailRsp.getGender() == 2) {
            //外呼
            autoCallService = SpringContextUtil.getBean("autoCallService");
            String callId = autoCallService.sendCall(orderId, "AO_117_1692337222554", sfcOrder.getPassengerCellphone(), null);
            //失败 发送短信
            mqRiskProducer = SpringContextUtil.getBean("riskSecurityProducer");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("callId", callId);
            jsonObject.put("orderId", orderId);
            jsonObject.put("mobile", sfcOrder.getPassengerCellphone());
            jsonObject.put("plateId", sfcOrder.getPlatId());
            mqRiskProducer.send(MqTagEnum.car_risk_Self_on_car_sms_send, JsonUtils.json(jsonObject), DateUtil.addMinute(new Date(), 15).getTime());
        }
    }

    /**
     * json数据
     */
    public void dealFlinkDataByJson(JSONObject jsonObject) {
        sfcOrderMapper = SpringContextUtil.getBean("sfcOrderMapper");
        String orderId = jsonObject.getString("orderId");
        SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(orderId);
        SfcFinishOrCancelData data = new SfcFinishOrCancelData();
        data.setOrderId(orderId);
        data.setMemberId(String.valueOf(sfcOrder.getMemberId()));
        if (data.getMemberId().equals("0")) {
            data.setMemberId(null);
        }
        data.setFinishTime(sfcOrder.getFinishTime());
        data.setCreateTime(sfcOrder.getCreated());
        data.setCancelTime(sfcOrder.getCancelTime());
        data.setStatus(sfcOrder.getStatus());
        data.setTotalAmount(PriceUtil.convertFenToYuan(sfcOrder.getTotalAmount()));
        data.setEventTime(jsonObject.getDate("eventTime"));
        data.setEventType(jsonObject.getInteger("eventType"));
        orderAddressMapper = SpringContextUtil.getBean("orderAddressMapper");
        OrderAddress orderAddress = orderAddressMapper.findByOrderId(orderId);
        if (orderAddress != null) {
            data.setEstimateKilo(orderAddress.getEstimateKilo());
            data.setCityId(orderAddress.getStartCityId());
            data.setStartLat(orderAddress.getStartLat());
            data.setStartLng(orderAddress.getStartLng());
            data.setEndLat(orderAddress.getEndLat());
            data.setEndLng(orderAddress.getEndLng());
        }
        sfcSupplierOrderService = SpringContextUtil.getBean("sfcSupplierOrderService");
        if (sfcOrder.getStatus() == 1000) {
            SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getListByOrderId(orderId).stream()
                    .filter(supplierOrder -> StringUtils.isNotBlank(supplierOrder.getPlateNumber())).findFirst().orElse(null);
            if (sfcSupplierOrder == null) {
                data.setCancelType(0);
            } else {
                data.setCancelType(1);
                data.setAcceptTime(sfcSupplierOrder.getAcceptTime());
                data.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
            }
        } else {
            SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getByOrderId(orderId, sfcOrder.getSupplierOrderId());
            if (sfcSupplierOrder != null && StringUtils.isNotBlank(sfcSupplierOrder.getPlateNumber())) {
                data.setAcceptTime(sfcSupplierOrder.getAcceptTime());
                data.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
                data.setIntervalTime((int) TimeUnit.MILLISECONDS.toMinutes(sfcOrder.getFinishTime().getTime() - sfcSupplierOrder.getAcceptTime().getTime()));
            }
        }
        data.setPhone7(sfcOrder.getPassengerCellphone().substring(0, 7));
        data.setPassengerCellphone(sfcOrder.getPassengerCellphone());
        szKafkaProducer = SpringContextUtil.getBean("szKafkaProducer");
        ProducerRecord<String, String> record = new ProducerRecord<>("car_flink_stream_order_create_topic_risk", JsonUtils.json(data));
        szKafkaProducer.send(record);
    }

    /**
     * 处理顺风车用户完单和取消的时候组装数据
     */
    public void dealFlinkData(String body) {
        SfcFinishOrCancelData data = convertFlinkData(body);
        if (data.getStatus() == 1000 && data.getDistributorFlag() == 1) {
            return;
        }
        szKafkaProducer = SpringContextUtil.getBean("szKafkaProducer");
        ProducerRecord<String, String> record = new ProducerRecord<>("car_flink_stream_order_finish_cancel_topic_risk", JsonUtils.json(data));
        szKafkaProducer.send(record);
    }

    /**
     * 处理用户全部下单
     */
    public void dealCreateOrder(String body) {
        String key = "ORDER_CREATE_" + body;
        redissonClient = SpringContextUtil.getBean("riskProcessRedisson");
        RBucket<Object> bucket = redissonClient.getBucket(key);
        if (bucket != null && bucket.get() != null) {
            return;
        } else {
            bucket.set(key, 10, TimeUnit.SECONDS);
        }
        sfcOrderMapper = SpringContextUtil.getBean("sfcOrderMapper");
        SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(body);
        if (sfcOrder.getMemberId() == null || sfcOrder.getMemberId() == 0) {
            return;
        }
        orderAddressMapper = SpringContextUtil.getBean("orderAddressMapper");
        OrderAddress orderAddress = orderAddressMapper.findByOrderId(body);
        OrderPassengerCellPhone orderPassengerCellPhone = new OrderPassengerCellPhone();
        orderPassengerCellPhone.setMemberId(String.valueOf(sfcOrder.getMemberId()));
        orderPassengerCellPhone.setOrderId(body);
        orderPassengerCellPhone.setCreateTime(sfcOrder.getCreated());
        orderPassengerCellPhone.setStartCityId(orderAddress.getStartCityId());
        saveScoredSortedSetService = SpringContextUtil.getBean("saveScoredSortedSetService");
        saveScoredSortedSetService.save(RedisKeyConstants.SFC_PHONE_ORDER_NUMBER + sfcOrder.getPassengerCellphone(),
                3 * 24 * 60 * 60L, orderPassengerCellPhone, sfcOrder.getCreated().getTime());

        //记录创单
        OrderRiskContext context = new OrderRiskContext();
        context.setOrderId(sfcOrder.getOrderId());
        context.setPassengerCellphone(sfcOrder.getPassengerCellphone());
        context.setMemberId(String.valueOf(sfcOrder.getMemberId()));
        context.setUnionId(sfcOrder.getUnionId());
        context.setCreateTime(DateUtil.date2String(sfcOrder.getCreated()));
        saveScoredSortedSetService.save("sfc_user_id_place_order_context_" + sfcOrder.getMemberId(),
                3 * 24 * 60 * 60L, context, sfcOrder.getCreated().getTime());


        String userId = StringUtils.isNotBlank(sfcOrder.getUnionId()) ? sfcOrder.getUnionId() : String.valueOf(sfcOrder.getMemberId());
        //下单存入
        saveScoredSortedSetService.save(RedisKeyConstants.SFC_USER_ID_PLACE_ORDER + userId,
                3 * 24 * 60 * 60L, sfcOrder.getOrderId(), sfcOrder.getCreated().getTime());

        orderIdScoredSortedSetService = SpringContextUtil.getBean("orderIdScoredSortedSetService");

        List<String> orderIds = orderIdScoredSortedSetService.get(RedisKeyConstants.SFC_USER_ID_PLACE_ORDER + userId);
        //用户下单指标
        RMap<String, Object> map = redissonClient.getMap("sfc_user_id_24h_order_num_" + userId);
        map.put("num", 1);
        map.put("orderNos", StringUtils.join(orderIds, ","));
        long endDate = DateUtil.addDay(new Date(), 1).getTime();
        redissonClient.getKeys().expireAt("sfc_user_id_24h_order_num_" + userId, endDate);

//        try {
//            SfcFinishOrCancelData data = convertFlinkData(body);
//            szKafkaProducer = SpringContextUtil.getBean("szKafkaProducer");
//            ProducerRecord<String, String> record = new ProducerRecord<>("car_flink_stream_order_create_topic_risk", JsonUtils.json(data));
//            szKafkaProducer.send(record);
//        }catch (Exception e){
//            log.error("发送创单kafka消息错误====",e);
//        }

    }

    //统一组装flinkData
    public SfcFinishOrCancelData convertFlinkData(String orderId) {
        sfcOrderMapper = SpringContextUtil.getBean("sfcOrderMapper");
        SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(orderId);
        SfcFinishOrCancelData data = new SfcFinishOrCancelData();
        data.setOrderId(orderId);
        data.setMemberId(String.valueOf(sfcOrder.getMemberId()));
        data.setUnionId(sfcOrder.getUnionId());
        data.setFinishTime(sfcOrder.getFinishTime());
        data.setCreateTime(sfcOrder.getCreated());
        data.setCancelTime(sfcOrder.getCancelTime());
        data.setStatus(sfcOrder.getStatus());
        data.setDistributorFlag(sfcOrder.getDistributorFlag());
        orderAddressMapper = SpringContextUtil.getBean("orderAddressMapper");
        OrderAddress orderAddress = orderAddressMapper.findByOrderId(orderId);
        if (orderAddress != null) {
            data.setEstimateKilo(orderAddress.getEstimateKilo());
            data.setCityId(orderAddress.getStartCityId());
            data.setStartLat(orderAddress.getStartLat());
            data.setStartLng(orderAddress.getStartLng());
            data.setEndLat(orderAddress.getEndLat());
            data.setEndLng(orderAddress.getEndLng());
        }
        sfcSupplierOrderService = SpringContextUtil.getBean("sfcSupplierOrderService");
        if (sfcOrder.getStatus() == 1000) {
            SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getListByOrderId(orderId).stream()
                    .filter(supplierOrder -> StringUtils.isNotBlank(supplierOrder.getPlateNumber())).findFirst().orElse(null);
            if (sfcSupplierOrder == null) {
                data.setCancelType(0);
            } else {
                data.setCancelType(1);
                data.setAcceptTime(sfcSupplierOrder.getAcceptTime());
                data.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
            }
        } else {
            SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getByOrderId(orderId, sfcOrder.getSupplierOrderId());
            if (sfcSupplierOrder != null) {
                data.setAcceptTime(sfcSupplierOrder.getAcceptTime());
                data.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
                data.setIntervalTime((int) TimeUnit.MILLISECONDS.toMinutes(sfcOrder.getFinishTime().getTime() - sfcSupplierOrder.getAcceptTime().getTime()));
            }
        }
        data.setPhone7(sfcOrder.getPassengerCellphone().substring(0, 7));
        data.setPassengerCellphone(sfcOrder.getPassengerCellphone());
        return data;
    }

    /**
     * 这边处理恶意下单的
     */
    public void dealPlaceOrder(String body) {
        String key = "ORDER_STARTING_" + body;
        redissonClient = SpringContextUtil.getBean("riskProcessRedisson");
        RBucket<Object> bucket = redissonClient.getBucket(key);
        if (bucket != null && bucket.get() != null) {
            return;
        } else {
            bucket.set(key, 10, TimeUnit.SECONDS);
        }
        log.info("[][][][]开始处理相关消息:{}", body);
        //行程中的订单处理
        saveScoredSortedSetService = SpringContextUtil.getBean("saveScoredSortedSetService");
        orderAddressMapper = SpringContextUtil.getBean("orderAddressMapper");
        esQueryClient = SpringContextUtil.getBean("esQueryClient");
        //存储预估路径
        OrderAddress orderAddress = orderAddressMapper.findByOrderId(body);
        // 备注：老LBS 2024年10月14下线，不再支持
//        trafficClient = SpringContextUtil.getBean("trafficClient");
//        String estimatePath = trafficClient.queryPoint(orderAddress.getStartLng() + "," + orderAddress.getStartLat()
//                , orderAddress.getEndLng() + "," + orderAddress.getEndLat());
        //先缓存起来，给个1天的过期时间,这个时间每次会重置，权重给个5分钟开始计算
        if (body.startsWith("SFC")) {
            //这边放入行程中队列
            saveScoredSortedSetService.save(RedisKeyConstants.SFC_MOVING_ORDER,
                    3 * 24 * 60 * 60L, body, DateUtil.addMinute(new Date(), 20).getTime());
//            redissonClient.getBucket(RedisKeyConstants.SFC_ESTIMATE_PATH + body).set(estimatePath, 1, TimeUnit.DAYS);
        } else {
            saveScoredSortedSetService.save(RedisKeyConstants.YNC_MOVING_ORDER,
                    3 * 24 * 60 * 60L, body, DateUtil.addMinute(new Date(), 5).getTime());
//            redissonClient.getBucket(RedisKeyConstants.YNC_ESTIMATE_PATH + body).set(estimatePath, 1, TimeUnit.DAYS);
        }
    }

    /**
     * 处理顺丰车取消的
     */
    public void dealSfcCancelLog(String orderId) {
        saveScoredSortedSetService = SpringContextUtil.getBean("saveScoredSortedSetService");
        String key = "sfc_cancel_order_" + orderId;
        redissonClient = SpringContextUtil.getBean("riskProcessRedisson");
        RBucket<Object> bucket = redissonClient.getBucket(key);
        if (bucket != null && bucket.get() != null) {
            return;
        } else {
            bucket.set(key, 10, TimeUnit.SECONDS);
        }
        //看下当前订单是哪种取消，接单后取消给type为1,不是接单后取消 给0
        sfcSupplierOrderService = SpringContextUtil.getBean("sfcSupplierOrderService");
        List<SfcSupplierOrder> listByOrderId = sfcSupplierOrderService.getListByOrderId(orderId);
        sfcOrderMapper = SpringContextUtil.getBean("sfcOrderMapper");
        SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(orderId);
        orderAddressMapper = SpringContextUtil.getBean("orderAddressMapper");
        OrderAddress orderAddress = orderAddressMapper.findByOrderId(orderId);
        SfcSupplierOrder sfcSupplierOrder = listByOrderId.stream().filter(data -> StringUtils.isNotBlank(data.getPlateNumber())).findFirst().orElse(null);
        if (sfcOrder.getMemberId() == 0) {
            //分销的单子不做
            return;
        }
        //说明是接单后取消的
        OrderStatusCancelDTO dto = new OrderStatusCancelDTO();
        dto.setOrderId(orderId);
        dto.setMemberId(String.valueOf(sfcOrder.getMemberId()));
        dto.setPassengerCellphone(sfcOrder.getPassengerCellphone());
        dto.setCancelTime(DateUtilRisk.addSeconds(new Date(), -5));
        dto.setStartCityId(orderAddress.getStartCityId());
        if (sfcSupplierOrder != null) {
            dto.setType(1);
        } else {
            dto.setType(0);
        }
        String cancelKey = StringUtils.isNotBlank(sfcOrder.getUnionId()) ? sfcOrder.getUnionId() : String.valueOf(sfcOrder.getMemberId());
        saveScoredSortedSetService.save(RedisKeyConstants.SFC_CANCEL_ORDER_MEMBER + dto.getMemberId(),
                3 * 24 * 60 * 60L, dto, new Date().getTime());

//        saveScoredSortedSetService.save(RedisKeyConstants.SFC_USER_ID_CANCEL_ORDER+cancelKey,
//                3 * 24 * 60 * 60L,
//                sfcOrder.getOrderId(),
//                sfcOrder.getCreated().getTime());
        log.info("[][][][]顺风车取消添加redis成功:{}", orderId);

        //计算取消率
//        orderIdScoredSortedSetService = SpringContextUtil.getBean("orderIdScoredSortedSetService");
//        List<String> cancelOrderList = orderIdScoredSortedSetService.get(RedisKeyConstants.SFC_USER_ID_CANCEL_ORDER + cancelKey);
//        List<String> placeOrderList = orderIdScoredSortedSetService.get(RedisKeyConstants.SFC_USER_ID_PLACE_ORDER + cancelKey);
//        if(CollectionUtils.isNotEmpty(placeOrderList) && CollectionUtils.isNotEmpty(cancelOrderList)){
//            if(new BigDecimal(cancelOrderList.size())
//                    .divide(new BigDecimal(placeOrderList.size()),2,BigDecimal.ROUND_CEILING).compareTo(new BigDecimal(100)) == 0){
//                RMap<String, Object> map = redissonClient.getMap(RedisKeyConstants.SFC_USER_ID_CANCEL_24h_RATE + key);
//                map.put("num",1);
//                map.put("orderNos",StringUtils.join(cancelOrderList,","));
//                long endDate = DateUtil.addDay(new Date(),1).getTime();
//                redissonClient.getKeys().expireAt(RedisKeyConstants.SFC_USER_ID_CANCEL_24h_RATE + key, endDate);
//            }
//        }
    }

    public void dealMiniSms(String body) {
        redissonClient = SpringContextUtil.getBean("riskProcessRedisson");
        RBucket<String> bucket = redissonClient.getBucket("program:order:warn:sms:" + body);
        if (bucket.isExists() || StringUtils.isNotBlank(bucket.get())) {
            return;
        } else {
            bucket.set("1", 10, TimeUnit.MINUTES);
        }
        //直接请求顺风车接口，发送短信
        carOrderApi = SpringContextUtil.getBean("carOrderApi");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId", body);
        carOrderApi.sendSms(jsonObject);
    }
}