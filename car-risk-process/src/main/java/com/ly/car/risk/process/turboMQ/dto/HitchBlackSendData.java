package com.ly.car.risk.process.turboMQ.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class HitchBlackSendData {

    private String driverId;//给业务方的司机标识
    /**
     * blackCustomer - 禁止认证名单
     * idCard - 身份验证
     * bankCard - 银行卡验证
     * mobileInfo - 运营商验证
     * scoreInfo - 犯罪记录验证
     * licenseInfo - 驾驶证验证
     * drivingPermit - 行驶证
     * */
    private Map<String, Integer> resultMap = new HashMap<>();
}
