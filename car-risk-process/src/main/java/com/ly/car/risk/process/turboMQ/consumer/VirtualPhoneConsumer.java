package com.ly.car.risk.process.turboMQ.consumer;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.car.risk.process.api.TencentCloudApiClient;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.dto.VirtualPhoneData;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class VirtualPhoneConsumer implements MessageListenerConcurrently {

    private TencentCloudApiClient tencentCloudApiClient;
    private MqRiskProducer mqRiskProducer;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for(MessageExt messageExt : list){
            String body = null;
            try {
                body = new String(messageExt.getBody(),"utf-8");
                log.info("[binlog][SfcStatusChangeConsumer][][][]收到turbo消息,消息id={}, body={}", messageExt.getMsgId(), body);
                VirtualPhoneData data = JsonUtils.json(body,VirtualPhoneData.class);
                if(!data.getSourceTag().equals("MadaSaas") && !data.getSourceTag().equals("SFC")){
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                //todo 中台需要添加录音id
                tencentCloudApiClient = SpringContextUtil.getBean("tencentCloudApiClient");
                Long recTaskId = tencentCloudApiClient.createRecTask(0, data.getSoundUrl());
                JSONObject taskJsonObject = new JSONObject();
                taskJsonObject.put("taskId",recTaskId);
                taskJsonObject.put("times",1);
                taskJsonObject.put("orderId",data.getOrderId());
                taskJsonObject.put("driverCardNo",null);
                taskJsonObject.put("source",2);
                taskJsonObject.put("callId",data.getCallId());
                taskJsonObject.put("reportId",data.getReportId());
                if(recTaskId != null){
                    //发送获取结果mq
                    mqRiskProducer = SpringContextUtil.getBean("riskSecurityProducer");
                    mqRiskProducer.send(MqTagEnum.car_self_tencent_get_describe_task, JsonUtils.json(taskJsonObject), DateUtil.addMinute(new Date(),1).getTime());
                }
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
