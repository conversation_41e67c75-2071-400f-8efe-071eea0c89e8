package com.ly.car.risk.process.service.dto.order;

import lombok.Data;

/**
 * Description of UsedDiscountInfo
 *
 * <AUTHOR>
 * @date 2024/3/11
 * @desc
 */
@Data
public class UsedDiscountInfo {

    /** 0:未知 1:车票 2:统一辅营 3:红包/抵用券 4:里程 5:活动营销 6:抵扣商品 非必填 */
    private Integer businessType;
    /** 销售价 */
    private String salePrice;
    /** 实际价 */
    private String realPrice;
    /** 产品名称 */
    private String productName;

}