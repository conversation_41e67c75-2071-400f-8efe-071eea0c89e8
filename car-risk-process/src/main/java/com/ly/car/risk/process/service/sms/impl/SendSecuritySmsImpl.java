package com.ly.car.risk.process.service.sms.impl;

import com.alibaba.fastjson.JSON;
import com.ly.car.risk.process.client.PushClient;
import com.ly.car.risk.process.client.ShortUrlClient;
import com.ly.car.risk.process.controller.request.safecenter.SafetySmsRequest;
import com.ly.car.risk.process.repo.risk.mapper.entity.SafeCenterShare;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.safecenter.SafeCenterService;
import com.ly.car.risk.process.service.sms.SendSecuritySms;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.travel.pushcore.facade.response.PushResponse;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/14 11:18
 **/
@Service
@Slf4j
public class SendSecuritySmsImpl implements SendSecuritySms {
    
    @Resource
    private PushClient pushClient;
    
    @Resource
    private ShortUrlClient shortUrlClient;
    
    @Resource
    private SafeCenterService safeCenterService;
    
    @Override
    public PushResponse sendSecuritySms(SafetySmsRequest request, CarOrderDetail orderDetail) {
        try {
            switch (request.getSmsTypeEnum()) {
                case YC_SAFETY_NOTICE:
                    return pushYcSafetyNoticeSms(request.getSmsParams(), orderDetail);
                default:
                    break;
            }
        } catch (Exception e) {
            LoggerUtils.warn(log, "短信推送失败,入参{}", e, JSON.toJSON(request));
        }
        PushResponse pushResponse = new PushResponse();
        pushResponse.setSuccess(true);
        return pushResponse;
    }
    
    private PushResponse pushYcSafetyNoticeSms(Map<String, String> params, CarOrderDetail orderDetail) {
        LoggerUtils.info(log, "开始进行行程分享");
        String orderId = params.get("orderId");
        String memberId = params.get("memberId");
        String userPhone = params.get("userPhone");
        String carNumber = params.get("carNumber");
        
        if (StringUtils.isBlank(orderId) ||
                StringUtils.isBlank(carNumber)) {
            LoggerUtils.warn(log, "行程分享失败,参数缺失{}", JSON.toJSONString(params));
            PushResponse pushResponse = new PushResponse();
            pushResponse.setSuccess(false);
            return pushResponse;
        }
        //安全分享开关是否打开，时间是否符合
        SafeCenterShare shareUserInfo = safeCenterService.getRecentUpdateShareInfo(memberId, null);
        
        LoggerUtils.info(log, "行程分享配置:{}", JSON.toJSONString(shareUserInfo));
        
        if (!checkShareInfo(shareUserInfo)) {
            PushResponse pushResponse = new PushResponse();
            pushResponse.setSuccess(false);
            pushResponse.setErrorCode("502");
            pushResponse.setErrorMessage("分享或联系人配置为空");
            return pushResponse;
        }
        //推送短信模板信息
        String shortUrls = shortUrlClient.getShortUrl(orderDetail);
        Map<String, String> smsParams = new HashMap<>(4);
        smsParams.put("@url", shortUrls);
        smsParams.put("@phonenumber", userPhone);
        smsParams.put("@carnumber", carNumber);
        return pushClient.pushSms(orderDetail.getOrderChannel(), shareUserInfo.getEmergencyPhone(), "", smsParams);
    }
    
    @Nullable
    private static Boolean checkShareInfo(SafeCenterShare shareUserInfo) {
        if (shareUserInfo == null || shareUserInfo.getJourneyShareSwitch() == 0) {
            LoggerUtils.info(log, "用户未开启自动行程分享");
            return false;
        }
        if (StringUtils.isBlank(shareUserInfo.getShareBeginTime()) || StringUtils.isBlank(shareUserInfo.getShareEndTime())) {
            LoggerUtils.info(log, "用户未配置自动行程分享时间");
            return false;
        }
        boolean res = TimeUtil.checkTime(shareUserInfo.getShareBeginTime(), shareUserInfo.getShareEndTime());
        if (!res) {
            LoggerUtils.info(log, "当前时间不处于用户开启自动行程分享时间区间");
            return false;
        }
        String emergencyPhone = shareUserInfo.getEmergencyPhone();
        if (StringUtils.isBlank(emergencyPhone)) {
            LoggerUtils.info(log, "用户未配置紧急联系人");
            return false;
        }
        return true;
    }
    
}
