package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.ly.car.order.entity.DistributionInfo;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.repo.hitchorder.entity.PassengerOrderAddress;
import com.ly.car.risk.process.repo.hitchorder.entity.PassengerOrderInfo;
import com.ly.car.risk.process.repo.hitchorder.mapper.PassengerOrderAddressMapper;
import com.ly.car.risk.process.repo.hitchorder.mapper.PassengerOrderInfoMapper;
import com.ly.car.risk.process.repo.order.mapper.DistributionInfoMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.dto.HcSceneConfig;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HcOffLineRiskService {

    @Resource
    private PassengerOrderInfoMapper passengerOrderInfoMapper;
    @Resource
    private PassengerOrderAddressMapper passengerOrderAddressMapper;
    @Resource
    private DistributionInfoMapper distributionInfoMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private HcRiskOrderService hcRiskOrderService;

    public void queryHcRiskOrder(){
        //获取前一个小时到小时数
        String beforeHour = DateUtil.date2String(DateUtil.addHour(new Date(),-2)).split(":")[0];
        //查询当前时间前一小时的完单
        //查询当前时间前一小时的完单
        String startDate = beforeHour + ":00:00";
        String endDate = beforeHour + ":59:59";
        //查询汇川库的司机完单
        List<PassengerOrderInfo> sfcOrderList = passengerOrderInfoMapper.selectList(
                new QueryWrapper<PassengerOrderInfo>().ge("arrival_time", startDate).le("arrival_time", endDate)
                .in("order_status",300,400)
        );
        if(CollectionUtils.isEmpty(sfcOrderList)){
            log.info("[][][][]未查出完单信息:{}",startDate);
            return;
        }
        //500订单去查一次
        List<List<PassengerOrderInfo>> splitList = Lists.partition(sfcOrderList,500);
        for(List<PassengerOrderInfo> orderList : splitList){
            List<String> orderIds = orderList.stream().map(PassengerOrderInfo::getPassengerOrderId).collect(Collectors.toList());
            List<String> outOrderIds = orderList.stream().map(PassengerOrderInfo::getDistributionOrderId).collect(Collectors.toList());

            //查询所有ref_id
            List<SfcOrder> tcSfcOrderList = sfcOrderMapper.selectList(new QueryWrapper<SfcOrder>().in("order_id",outOrderIds));
            Map<String,SfcOrder> sfcOrderMap = tcSfcOrderList.stream().collect(Collectors.toMap(SfcOrder::getOrderId,v->v,(old,cur)->old));
            List<Long> refIds = tcSfcOrderList.stream().map(SfcOrder::getRefId).collect(Collectors.toList());
            List<DistributionInfo> distributionInfos = distributionInfoMapper.selectList(new QueryWrapper<DistributionInfo>().in("refid",refIds));
            Map<Long,DistributionInfo> distributionInfoMap = distributionInfos.stream().collect(Collectors.toMap(DistributionInfo::getRefid,v->v,(old,cur)->old));
            //对该批次订单进行详情查询
            List<PassengerOrderAddress> orderAddressList = passengerOrderAddressMapper.selectList(
                    new QueryWrapper<PassengerOrderAddress>().in("passenger_order_id",orderIds)
            );
            //对该详情进行map结构
            Map<String,PassengerOrderAddress> orderAddressMap = orderAddressList.stream().collect(
                    Collectors.toMap(PassengerOrderAddress::getPassengerOrderId,v->v,(old,cur)->old)
            );
            //获取下汇川的配置
            HcSceneConfig config = queryConfig();
            List<Long> driverMemberIds = orderList.stream().map(PassengerOrderInfo::getDriverMemberId).collect(Collectors.toList());
            //查下司机纬度的所有乘车人订单
            List<PassengerOrderInfo> driverOrderList = passengerOrderInfoMapper.selectList(
                    new QueryWrapper<PassengerOrderInfo>().between("arrival_time",DateUtil.addHour(new Date(),-config.getScene7_6HistoryTime()),new Date())
                            .in("driver_member_id",driverMemberIds)
                        .in("order_status",300,400)
            );
            log.info("[][][][]查询司机出完单信息:{}",driverOrderList.size());
            //对24小时司机进行分组
            Map<Long,List<PassengerOrderInfo>> driverMap = driverOrderList.stream().collect(Collectors.groupingBy(PassengerOrderInfo::getDriverMemberId));
            for(PassengerOrderInfo orderInfo : orderList){
                PassengerOrderAddress orderAddress = orderAddressMap.get(orderInfo.getPassengerOrderId());
                if(orderAddress.getActualMinute() < config.getScene7_6Time() || orderAddress.getActualKilo().compareTo(config.getScene7_6Kilo()) < 0){
                    //推送人审队列,这边和下面是一样的，主要是推送的标识不一样
                    //获取分销商订单
                    SfcOrder sfcOrder = sfcOrderMap.get(orderInfo.getDistributionOrderId());
                    hcRiskOrderService.syncDataBase(sfcOrder,orderInfo,distributionInfoMap.get(sfcOrder.getRefId()),orderAddress);
                }
                log.info("[][][][]当前汇川订单{}",JsonUtils.json(orderInfo));
                //本来是一次性判断，为了减少查询压力，这边判断多用几层
                //获取该司机近24h的完单
                List<PassengerOrderInfo> driverByList = driverMap.get(orderInfo.getDriverMemberId());
                if(orderAddress.getActualKilo().compareTo(config.getScene7_6Kilo()) < 1){
                    //司机24h完单的订单号
                    List<String> driverByOrderIds = driverByList.stream().map(PassengerOrderInfo::getPassengerOrderId).collect(Collectors.toList());
                    //查询这些订单里程等信息、
                    List<PassengerOrderAddress> orderAddressByDriver = passengerOrderAddressMapper.selectList(
                            new QueryWrapper<PassengerOrderAddress>().in("passenger_order_id",driverByOrderIds)
                    );
                    //获取里程小于等于100米的订单
                    List<PassengerOrderAddress> filterDistanceList = orderAddressByDriver.stream()
                            .filter(data -> data.getActualKilo().compareTo(config.getScene7_6Kilo()) < 1)
                            .collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(filterDistanceList) && filterDistanceList.size() >= config.getScene7_6Num()){
                        //推送风险单
                        SfcOrder sfcOrder = sfcOrderMap.get(orderInfo.getDistributionOrderId());
                        hcRiskOrderService.syncDataBase(sfcOrder,orderInfo,distributionInfoMap.get(sfcOrder.getRefId()),orderAddress);
                    }
                }
                if(orderAddress.getActualMinute() <= config.getScene7_6Time()){
                    //司机24h完单的订单号
                    List<String> driverByOrderIds = driverByList.stream().map(PassengerOrderInfo::getPassengerOrderId).collect(Collectors.toList());
                    //查询这些订单里程等信息、
                    List<PassengerOrderAddress> orderAddressByDriver = passengerOrderAddressMapper.selectList(
                            new QueryWrapper<PassengerOrderAddress>().in("passenger_order_id",driverByOrderIds)
                    );
                    //获取里程小于等于100米的订单
                    List<PassengerOrderAddress> filterTimeList = orderAddressByDriver.stream()
                            .filter(data -> data.getActualMinute() <= config.getScene7_6Time())
                            .collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(filterTimeList) && filterTimeList.size() >= config.getScene7_6Num()){
                        //推送风险单
                        SfcOrder sfcOrder = sfcOrderMap.get(orderInfo.getDistributionOrderId());
                        hcRiskOrderService.syncDataBase(sfcOrder,orderInfo,distributionInfoMap.get(sfcOrder.getRefId()),orderAddress);
                    }
                }
            }
        }
    }

    /**
     * 获取配置信息
     * */
    private HcSceneConfig queryConfig(){
        try {
            String configJson = ConfigCenterClient.get("hc_driver_risk");
            HcSceneConfig config = JSONObject.parseObject(configJson,HcSceneConfig.class);
            log.info("[][][][]获取汇川规则配置:{}",configJson);
            log.info("[][][][]获取汇川规则配置转换:{}", JsonUtils.json(config));
            return config;
        } catch (Exception e) {
            log.error("获取汇川规则配置错误:",e);
        }
        return null;
    }
}
