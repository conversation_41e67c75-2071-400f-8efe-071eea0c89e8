package com.ly.car.risk.process.service.rule.hcGroup;

import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.constants.RiskResultEnum;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.dto.HitchBlackSendData;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class HcDriverCarService extends FilterHcAroundHandler{

    private final String THIRD_TYPE = "drivingPermit";

    @Resource
    private TianChuangRiskClient tianChuangRiskClient;
    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource(name = "hitchRiskProducer")
    private MqRiskProducer mqRiskProducer;

    @Override
    public void doHandler(FilterHcContext context) {
        RiskResultNewDTO resultDTO = (RiskResultNewDTO) context.getUiResult().getData();
        HitchBlackSendData data = new HitchBlackSendData();
        data.setDriverId(context.getDriverId());
        resultDTO.setObj(data);
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setPlate(context.getPlate());
        param.setPlateType(context.getPlateType());
        String carName = context.getCarName();
        carName = carName.replace("(","（");
        carName = carName.replace(")","）");
        param.setName(carName);
        Integer result = tianChuangRiskClient.verifyCarOwner(param,null);
        if (result != 0) {
            //发送mq
            resultDTO.setCode(1);
            resultDTO.setMessage("风控不通过");
            data.setDriverId(context.getDriverId());
            data.getResultMap().put(THIRD_TYPE,1);
            resultDTO.setObj(data);
            context.getUiResult().setData(resultDTO);
            mqRiskProducer.send(MqTagEnum.car_risk_hitch_notify, JsonUtils.json(resultDTO),0L);
            return;
        }
        if (this.nextHandler != null) {
            this.nextHandler.doHandler(context);
        } else {
            doAfter(resultDTO);
        }
    }

    @Override
    public void doAfter(RiskResultNewDTO resultDTO) {
        if(this.nextHandler == null && resultDTO.getCode() == 0){
            mqRiskProducer.send(MqTagEnum.car_risk_hitch_notify, JsonUtils.json(resultDTO),0L);
        }
    }
}
