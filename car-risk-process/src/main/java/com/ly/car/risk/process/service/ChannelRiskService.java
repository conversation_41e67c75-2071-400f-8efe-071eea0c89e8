package com.ly.car.risk.process.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.ChannelBadDebtsRisk;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.process.client.SupplierApiClient;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.api.MemberApi;
import com.ly.car.risk.process.api.MemberApiClient;
import com.ly.car.risk.process.api.TrafficClient;
import com.ly.car.risk.process.api.param.DriverBlack;
import com.ly.car.risk.process.api.param.DriverBlackNumReq;
import com.ly.car.risk.process.api.param.DriverBlackReq;
import com.ly.car.risk.process.api.rsp.MemberQueryResponse;
import com.ly.car.risk.process.bean.BillDateConverter;
import com.ly.car.risk.process.repo.risk.mapper.ChannelBadDebtsRiskMapper;
import com.ly.car.risk.process.repo.risk.mapper.DistributionRiskManageMapper;
import com.ly.car.risk.process.service.dto.ChannelComputeResult;
import com.ly.car.risk.process.service.dto.ChannelRiskBadDebtsDTO;
import com.ly.car.risk.process.service.dto.ChannelWarnConfig;
import com.ly.car.risk.process.service.dto.MailDTO;
import com.ly.car.risk.process.utils.SendMailUtils;
import com.ly.car.risk.process.utils.WorkWeiXinRobotUtils;
import com.ly.car.sharding.order.entity.DcdbOrderRiskDto;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.sharding.order.mappers.dcdb.DcdbOrderDataMapper;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChannelRiskService {

    @Resource
    private DcdbOrderDataMapper dcdbOrderDataMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderAddressMapper           orderAddressMapper;
    @Resource
    private SupplierApiClient            supplierApiClient;
    @Resource
    private DistributionRiskManageMapper distributionRiskManageMapper;
    @Resource
    private TrafficClient trafficClient;
    @Resource
    private ChannelBadDebtsRiskMapper channelBadDebtsRiskMapper;
    @Resource
    private SendMailUtils sendMailUtils;
    @Resource
    private MemberApi memberApi;
    @Resource
    private MemberApiClient memberApiClient;
    @Resource
    private RiskOrderManageService riskOrderManageService;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;


    public String findBadDebts(String codeStr){
        List<String> orderIdList = null;
        if(StringUtils.isNotBlank(codeStr)){
            orderIdList = Arrays.asList(codeStr.split(","));
        }

        int rule7days = 3;
        int rule7orderNum = 3;
        int rule8days = 3;
        int rule8orderNum = 3;
        int rule8blackNum = 3;
        BigDecimal rule8linkAmount = new BigDecimal("500");
        try {
            String channelRiskConfig= ConfigCenterClient.get("channel_driver_risk_config");
            if(StringUtils.isNotBlank(channelRiskConfig)){
                JSONObject jsonObject = JSON.parseObject(channelRiskConfig);
                rule7days = jsonObject.getInteger("rule7days");
                rule7orderNum = jsonObject.getInteger("rule7orderNum");
                rule8days = jsonObject.getInteger("rule8days");
                rule8orderNum = jsonObject.getInteger("rule8orderNum");
                rule8blackNum = jsonObject.getInteger("rule8blackNum");
                rule8linkAmount = jsonObject.getBigDecimal("rule8linkAmount");
            }
        } catch (Exception e) {
            log.error("[userRisk] [error] [{}] [{}] 获取渠道风控信息异常：{}",  e);
        }

        String startDate = DateUtil.date2String(DateUtil.addDay(new Date(),-rule7days),DateUtil.DATE_PATTERN_YYYY_MM_DD) + " 00:00:00";
        String endDate = DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD) + " 23:59:59";
        log.info("渠道风控任务范围开始时间:"+startDate);
        log.info("渠道风控任务范围结束时间:"+endDate);
        //查询近三天司机关联用户未支付订单
        log.info("渠道风控任务查询订单开始时间:"+System.currentTimeMillis());
        List<DcdbOrderRiskDto> orderRiskDtos = dcdbOrderDataMapper.queryChannelOrder(startDate,endDate,orderIdList);
        log.info("渠道风控任务查询订单结束时间:"+System.currentTimeMillis());
        if(orderRiskDtos == null || orderRiskDtos.size() == 0){
            log.info("渠道风控任务查询订单为空:"+System.currentTimeMillis());
            return "success";
        }
        //全部订单
        List<String> allOrderIds = orderRiskDtos.stream().map(DcdbOrderRiskDto::getOrderId).collect(Collectors.toList());
        //对司机进行分组
        Map<String,List<DcdbOrderRiskDto>> riskMap = orderRiskDtos.stream()
                .filter(e->StringUtils.isNotBlank(e.getPlateNumber()))
                .collect(Collectors.groupingBy(order-> order.getPlateNumber()));
        //整合司机，查看司机黑名单次数
        List<String> driverNos = new ArrayList<>(riskMap.keySet());
        DriverBlackNumReq driverBlackNumReq = new DriverBlackNumReq();
        driverBlackNumReq.setDriverList(driverNos);
        String resultData = trafficClient.getBlackDriverNum(driverBlackNumReq);
        //查询订单
        List<OrderInfo> orderInfoList = orderInfoMapper.findByOrderIds(allOrderIds);
        Map<String,OrderInfo> orderMap = orderInfoList.stream().collect(Collectors.toMap(OrderInfo::getOrderId, v->v,(old, cur)->old));
        //查询地址信息
        List<OrderAddress> orderAddressList = orderAddressMapper.findByOrderIds(allOrderIds);
        Map<String,OrderAddress> addressMap = orderAddressList.stream().collect(Collectors.toMap(OrderAddress::getOrderId,v->v,(old, cur)->old));
        //需要插入的风险订单
        List<DistributionRiskManage> manageInsertList = new ArrayList<>();
        //统计黑名单司机拉黑次数
        Map<String,Integer> driverCountMap = JSONObject.parseObject(resultData,Map.class);
        log.info("渠道风控任务黑名单司机为:"+driverCountMap == null?"":JSONObject.toJSONString(driverCountMap));
        //开始统计司机维度未支付的
        for(Map.Entry<String, List<DcdbOrderRiskDto>> orderRisk : riskMap.entrySet()){
            BigDecimal noPayAmount = orderRisk.getValue().stream().map(DcdbOrderRiskDto::getTotalAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
            log.info("[][][][]渠道风控任务未支付金额:"+noPayAmount+",司机为:"+orderRisk.getKey());
            if(orderRisk.getValue().size() > rule7orderNum-1){
                int finalRule8blackNum = rule8blackNum;
                BigDecimal finalRule8linkAmount = rule8linkAmount;
                orderRisk.getValue().forEach(risk->{
                    List<String> orderIds = orderRisk.getValue().stream().map(DcdbOrderRiskDto::getOrderId).collect(Collectors.toList());
                    orderIds.remove(risk.getOrderId());
                    DistributionRiskManage manage = new DistributionRiskManage();
                    manage.setOrderId(risk.getOrderId());
                    int blackNum = driverCountMap.get(orderRisk.getKey());
                    if(noPayAmount.compareTo(finalRule8linkAmount) > 0 ||  blackNum > finalRule8blackNum-1){
                        manage.setRuleNoList("007,008");
                    } else {
                        manage.setRuleNoList("007");
                    }
                    manage.setRiskMainScenario("离线场景");
                    manage.setRiskChildScenario("渠道刷单");
                    manage.setRiskLevel(5);
                    manage.setCityId(addressMap.get(risk.getOrderId()).getEndCityId());
                    manage.setCityName(addressMap.get(risk.getOrderId()).getEndCityName());
                    String supplierCodeFull = orderMap.get(risk.getOrderId()).getSupplierCodeFull();
                    if(StringUtils.isNotBlank(supplierCodeFull)){
                        
                        manage.setSupplierCode(supplierCodeFull);
                        manage.setSupplierName(supplierApiClient.getSupplierCodeMap().get(supplierCodeFull));
                        manage.setPayAccount("");//分销的没有支付账号
                    }
                    manage.setLinkOrder(StringUtils.join(orderIds,","));
                    manage.setCreateTime(new Date());
                    manage.setUpdateTime(new Date());
                    manage.setHitTime(new Date());
                    manage.setMainScene(3);
                    manage.setMemberId(orderMap.get(risk.getOrderId()).getMemberId());
                    manage.setChildScene(3);//渠道刷单
                    manage.setDriverCardNo(risk.getPlateNumber());
                    manage.setUnionId(risk.getUnionId());
                    manage.setMemberId(risk.getMemberId());
                    manage.setPhone(risk.getPassengerCellphone());
                    manageInsertList.add(manage);
                });
            }
        }


        //每天跑前三天可能会有重复的命中订单，所以要把即将插入的订单先查下库，再决定是否是要插入
        Map<String,DistributionRiskManage> alreadyManageMap = new HashMap<>();
        if(manageInsertList.size() > 0){
            List<String> manageOrderIds = manageInsertList.stream().map(DistributionRiskManage::getOrderId).collect(Collectors.toList());
            List<DistributionRiskManage> alreadyManage = distributionRiskManageMapper.selectList(
                    new QueryWrapper<DistributionRiskManage>()
                            .in("order_id",manageOrderIds)
                            .eq("main_scene",3)
                            .eq("child_scene",3)
            );
            if(alreadyManage != null && alreadyManage.size() > 0){
                alreadyManageMap = alreadyManage.stream().collect(Collectors.toMap(DistributionRiskManage::getOrderId,v->v,(old,cur)->old));
            }
        }

        List<DistributionRiskManage> shieldingList = new ArrayList<>();
        Map<String,List<String>> driverLinkOrder = new HashMap<>();
        for(DistributionRiskManage manage : manageInsertList){
            shieldingList.add(manage);
            if(alreadyManageMap.get(manage.getOrderId()) != null){
                continue;
            }
            if(driverLinkOrder.get(manage.getDriverCardNo()) == null || driverLinkOrder.get(manage.getDriverCardNo()).size() == 0){
                List<String> strList = new ArrayList<>();
                strList.add(manage.getOrderId());
                driverLinkOrder.put(manage.getDriverCardNo(),strList);
            } else {
                driverLinkOrder.get(manage.getDriverCardNo()).add(manage.getOrderId());
            }
            MemberQueryResponse infoByMemberId = memberApiClient.getInfoByMemberId(manage.getMemberId());
            if(infoByMemberId != null && infoByMemberId.getData() != null){
                manage.setUserPhone(infoByMemberId.getData().getMobile());
            }
            //通过memberId获取手机号等信息
//            distributionRiskManageMapper.insert(manage);
            distributionRiskManageService.addByRule(manage.getOrderId(),manage.getRuleNoList(),manage.getMainScene(),
                    manage.getChildScene(),manage.getLinkOrder(),null,manage.getRiskLevel());

            RiskOrderManage riskOrderManage = new RiskOrderManage();
            riskOrderManage.setIsRisk(1);
            riskOrderManage.setRuleNo(manage.getRuleNoList());
            riskOrderManage.setOrderId(manage.getOrderId());
            riskOrderManageService.addRiskOrder(riskOrderManage);
        }
        //执行拉黑司机动作
        List<DriverBlack> driverBlackReqs = new ArrayList<>();
        List<String> haveList = new ArrayList<>();
        int driverCount = 0;
        int currentCount = 0;
        for(DistributionRiskManage manage : shieldingList){
            if(haveList.contains(manage.getDriverCardNo())){
                continue;
            } else {
                currentCount = currentCount + 1;
                //查询下当前司机是不是已经黑名单，已经是的就不需要拉黑
                Integer result = trafficClient.isBlackDriver(manage.getDriverCardNo());
                if(result != null && result == 1){
                    continue;
                }
                haveList.add(manage.getDriverCardNo());
            }
            DriverBlack req = new DriverBlack();
            req.setDriver_plate_number(manage.getDriverCardNo());
            req.setSupplier(manage.getSupplierCode());
            req.setRemake("渠道风控");
            req.setReason(StringUtils.join(driverLinkOrder.get(manage.getDriverCardNo()),","));
            if(manage.getRuleNoList().contains("008")){
                req.setFreed_time("");
            } else {
                req.setFreed_time("7");
            }
            driverBlackReqs.add(req);
            driverCount = driverCount + 1;
        }
        log.info("当前批次司机的个数为:"+currentCount);
        log.info("需要拉黑司机的个数为:"+driverCount);
        DriverBlackReq driverBlackReq = new DriverBlackReq();
        driverBlackReq.setDirverBlacklist(driverBlackReqs);
        trafficClient.driverShielding(driverBlackReq);
        return "success";
    }


    public String badDebtsNotify(){
        String nowDate = DateUtil.date2String(new Date(),"yyyyMMdd");
        log.info("当天日期:"+nowDate);
        String sevenDate = DateUtil.date2String(DateUtil.addDay(new Date(),-6),"yyyyMMdd");
        log.info("周日期:"+sevenDate);
        String fifteenDate = DateUtil.date2String(DateUtil.addDay(new Date(),-14),"yyyyMMdd");
        log.info("十五天日期:"+fifteenDate);
        String thirtyDate = DateUtil.date2String(DateUtil.addDay(new Date(),-29),"yyyyMMdd");
        log.info("月日期:"+thirtyDate);

        //直接查询30天的
        List<ChannelBadDebtsRisk> thirtyRiskList = channelBadDebtsRiskMapper.selectList(
                new QueryWrapper<ChannelBadDebtsRisk>()
                        .ge("order_created",Integer.valueOf(thirtyDate))
                        .le("order_created",Integer.valueOf(nowDate))
        );
        if(thirtyRiskList == null || thirtyRiskList.size() == 0){
            //为空不执行
            log.info("近30天无数据");
            return "success";
        }
        log.info("近30天数据有:"+thirtyRiskList.size()+"条");

        //获取阈值信息
        ChannelWarnConfig config = WarnConfig();

        //汇总发送日，周，月维度预警
        List<ChannelBadDebtsRisk> dayWarnList = thirtyRiskList.stream().filter(e->e.getOrderCreated().equals(Integer.valueOf(nowDate))).collect(Collectors.toList());
        log.info("当天数量:" + dayWarnList.size());
        BigDecimal currentDayAmount = dayWarnList.stream().map(ChannelBadDebtsRisk::getBadOrderAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal currentDayAllAmount = dayWarnList.stream().map(ChannelBadDebtsRisk::getAllOrderAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal currentDayRate = currentDayAmount.compareTo(BigDecimal.ZERO)==0?BigDecimal.ZERO:currentDayAmount.divide(currentDayAllAmount,4,BigDecimal.ROUND_DOWN);
        log.info("当天坏账金额:" + currentDayAmount);
        log.info("当天坏账订单占比:" + currentDayRate);
        ChannelComputeResult dayWarnResult = convertCompute(dayWarnList);
        dayWarnResult.setAllBadAmount(currentDayAmount);
        dayWarnResult.setBadAmountRateSum(String.valueOf(currentDayRate));

        List<ChannelBadDebtsRisk> weekWarnList = thirtyRiskList.stream().filter(e->e.getOrderCreated() >= Integer.valueOf(sevenDate)).collect(Collectors.toList());
        ChannelComputeResult weekWarnResult = convertCompute(weekWarnList);
        List<ChannelBadDebtsRisk> monthWarnList = thirtyRiskList.stream().filter(e->e.getOrderCreated() >= Integer.valueOf(thirtyDate)).collect(Collectors.toList());
        ChannelComputeResult monthWarnResult = convertCompute(monthWarnList);
        sendWorkWxWarn(dayWarnResult,weekWarnResult,monthWarnResult,config);

        if(new Date().getHours() != 9){
            return "success";
        }

        //先按照城市分组
        Map<String,List<ChannelBadDebtsRisk>> cityGroup = thirtyRiskList.stream().collect(Collectors.groupingBy(risk->risk.getCityName()));
        //按照结果再进行供应商分组
        List<ChannelRiskBadDebtsDTO> exportList = new ArrayList<>();
        for(Map.Entry<String,List<ChannelBadDebtsRisk>> entry : cityGroup.entrySet()){
            if(entry.getValue() == null || entry.getValue().size() == 0){
                log.info("当前城市无数据:"+entry.getKey());
                continue;
            }
            Map<String,List<ChannelBadDebtsRisk>> supplierGroup
                    = entry.getValue().stream().collect(Collectors.groupingBy(risk->risk.getSupplierCode()));
            for(Map.Entry<String,List<ChannelBadDebtsRisk>> supplierMap : supplierGroup.entrySet()){
                log.info("当前城市->供应商数据:"+supplierMap.getKey()+supplierMap.getValue());
                if(supplierMap.getValue() == null || supplierMap.getValue().size() == 0){
                    log.info("当前城市->供应商无数据:"+supplierMap.getKey());
                    continue;
                }
                //已经到城市->供应商->当天维度，可以直接计算了
                //step-1 拿出当天的数据坏账订单数，坏账金额，当前坏账订单占比，当前坏账金额占比

                List<ChannelBadDebtsRisk> sortRisks = supplierMap.getValue().stream().
                        sorted(Comparator.comparing(ChannelBadDebtsRisk::getOrderCreated).reversed()).collect(Collectors.toList());
                List<ChannelBadDebtsRisk> risks = sortRisks.stream().filter(e->e.getOrderCreated().equals(Integer.valueOf(nowDate))).collect(Collectors.toList());
                int currentBadOrderNum = risks.stream().map(ChannelBadDebtsRisk::getBadOrderNum).mapToInt(s->s).sum();
                int currentAllBadOrderNum = risks.stream().map(ChannelBadDebtsRisk::getAllOrderNum).mapToInt(s->s).sum();
                BigDecimal currentBadAmount = risks.stream().map(ChannelBadDebtsRisk::getBadOrderAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                BigDecimal currentAllBadAmount = risks.stream().map(ChannelBadDebtsRisk::getAllOrderAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                BigDecimal currentBadOrderRate = currentBadOrderNum==0?BigDecimal.ZERO:new BigDecimal(currentBadOrderNum).divide(new BigDecimal(currentAllBadOrderNum),4,BigDecimal.ROUND_DOWN);
                BigDecimal currentBadAmountRate = currentBadAmount.compareTo(BigDecimal.ZERO)==0?BigDecimal.ZERO:currentBadAmount.divide(currentAllBadAmount,4,BigDecimal.ROUND_DOWN);
                //step-2 以当天为基准往前6天,先获取集合，再计算 近7天坏账订单占比、近7天坏账金额、近7天坏账金额占比
                List<ChannelBadDebtsRisk> sevenRisks = sortRisks.stream().filter(e->e.getOrderCreated() >= Integer.valueOf(sevenDate)).collect(Collectors.toList());
                log.info("当前近7天数据:"+(sevenRisks.isEmpty()?0:sevenRisks.size()));
                ChannelComputeResult sevenResult = convertCompute(sevenRisks);
                //step-3 近15天坏账订单占比、近15天坏账金额、近15天坏账金额占比
                List<ChannelBadDebtsRisk> fifteenRisks = sortRisks.stream().filter(e->e.getOrderCreated() >= Integer.valueOf(fifteenDate)).collect(Collectors.toList());
                log.info("当前近15天数据:"+(fifteenRisks.isEmpty()?0:fifteenRisks.size()));
                ChannelComputeResult fifteenResult = convertCompute(fifteenRisks);
                //step-4 近30天坏账订单占比、近30天坏账金额、近30天坏账金额占比
                List<ChannelBadDebtsRisk> thirtyRisks = sortRisks.stream().filter(e->e.getOrderCreated() >= Integer.valueOf(thirtyDate)).collect(Collectors.toList());
                log.info("当前近30天数据:"+(thirtyRisks.isEmpty()?0:thirtyRisks.size()));
                ChannelComputeResult thirtyResult = convertCompute(thirtyRisks);

                //整合导出数据
                ChannelRiskBadDebtsDTO debtsDTO = new ChannelRiskBadDebtsDTO();
                debtsDTO.setCityName(entry.getKey());
                debtsDTO.setSupplierName(supplierMap.getKey());
                debtsDTO.setPlatform("");
                debtsDTO.setTime(nowDate);
                debtsDTO.setBadNum(String.valueOf(currentBadOrderNum));
                debtsDTO.setBadAmount(String.valueOf(currentBadAmount));
                debtsDTO.setCurrBadOrderRate(decimalConvertString(currentBadOrderRate));
                debtsDTO.setCurrBadAmountRate(decimalConvertString(currentBadAmountRate));
                debtsDTO.setSevenBadOrderRate(decimalConvertString(new BigDecimal(sevenResult.getBadOrderRate())));
                debtsDTO.setSevenBadAmount(String.valueOf(sevenResult.getAllBadAmount()));
                debtsDTO.setSevenBadAmountRate(decimalConvertString(new BigDecimal(sevenResult.getBadAmountRateSum())));
                debtsDTO.setFifteenBadOrderRate(decimalConvertString(new BigDecimal(fifteenResult.getBadOrderRate())));
                debtsDTO.setFifteenBadAmount(String.valueOf(fifteenResult.getAllBadAmount()));
                debtsDTO.setFifteenBadAmountRate(decimalConvertString(new BigDecimal(fifteenResult.getBadAmountRateSum())));
                debtsDTO.setThirtyBadOrderRate(decimalConvertString(new BigDecimal(thirtyResult.getBadOrderRate())));
                debtsDTO.setThirtyBadAmount(String.valueOf(thirtyResult.getAllBadAmount()));
                debtsDTO.setThirtyBadAmountRate(decimalConvertString(new BigDecimal(thirtyResult.getBadAmountRateSum())));
                exportList.add(debtsDTO);
            }
        }

        ByteArrayOutputStream byteArray = new ByteArrayOutputStream();
        EasyExcel.write(byteArray, ChannelRiskBadDebtsDTO.class).registerConverter(new BillDateConverter()).sheet("sheet1")
                .doWrite(exportList);
        //发送邮件
        MailDTO mailDTO = new MailDTO();
        mailDTO.setWorkId(config.getWorkId());
        List<MailDTO.AttachmentDTO> attachments = new ArrayList<>();
        MailDTO.AttachmentDTO attachmentDTO = new MailDTO.AttachmentDTO();
        attachmentDTO.setFileName("坏账风控预警报表"+DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD));
        attachmentDTO.setFileSuffix("xlsx");
        attachmentDTO.setFileContent(SendMailUtils.getBase64Str(byteArray));
        attachments.add(attachmentDTO);
        mailDTO.setAttachments(attachments);
        MailDTO.MailBody body = new MailDTO.MailBody();
        body.setEmailSubject("分销坏账预警");
        body.setEmailBody("注意查收当前预警");
        mailDTO.setParameter(body);
        sendMailUtils.sendMail(mailDTO);
        return "success";
    }

    public ChannelComputeResult convertCompute(List<ChannelBadDebtsRisk> risks){
        ChannelComputeResult channelComputeResult = new ChannelComputeResult();
        if(risks == null || risks.size() == 0){
            channelComputeResult.setBadAmountRateSum("0");
            channelComputeResult.setAllBadAmount(new BigDecimal("0"));
            channelComputeResult.setBadOrderRate("0");
            return channelComputeResult;
        }
        int allOrderNum = risks.stream().map(ChannelBadDebtsRisk::getAllOrderNum).mapToInt(s->s).sum();
        int allBadOrderNum = risks.stream().map(ChannelBadDebtsRisk::getBadOrderNum).mapToInt(s->s).sum();
        BigDecimal badAmountSum = risks.stream().map(ChannelBadDebtsRisk::getBadOrderAmount).reduce(BigDecimal.ZERO,BigDecimal::add);

        BigDecimal allOrderAmount = risks.stream().map(ChannelBadDebtsRisk::getAllOrderAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal badOrderRate = new BigDecimal(allBadOrderNum).divide(new BigDecimal(allOrderNum),4,BigDecimal.ROUND_DOWN);
        BigDecimal badAmountRateSum = badAmountSum.divide(allOrderAmount,4,BigDecimal.ROUND_DOWN);
        BigDecimal allRevenueAmount = risks.stream().map(ChannelBadDebtsRisk::getRevenueAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        channelComputeResult.setBadAmountRateSum(String.valueOf(badAmountRateSum));
        channelComputeResult.setAllBadAmount(badAmountSum);
        channelComputeResult.setBadOrderRate(String.valueOf(badOrderRate));
        channelComputeResult.setRevenueAmount(allRevenueAmount);
        channelComputeResult.setAllOrderAmount(allOrderAmount);
        return channelComputeResult;
    }

    public void sendWorkWxWarn(ChannelComputeResult dayResult,ChannelComputeResult weekResult,ChannelComputeResult monthResult,ChannelWarnConfig config){
        log.info("[][][][][]日统计："+JSONObject.toJSONString(dayResult));
        log.info("[][][][][]周统计："+JSONObject.toJSONString(weekResult));
        log.info("[][][][][]月统计："+JSONObject.toJSONString(monthResult));

        //处理日是否发送
        String message = "";
        if(new BigDecimal(dayResult.getBadAmountRateSum()).compareTo(config.getDayBadAmountRate()) > 0
            && dayResult.getAllBadAmount().compareTo(new BigDecimal(config.getDayBadAmount())) > 0){
            message = message +"【日】坏账率占比超出预警值"+decimalConvertString(config.getDayBadAmountRate())+
                    ",且坏账金额大于"+config.getDayBadAmount()+
                    ",当前值为"+decimalConvertString(new BigDecimal(dayResult.getBadAmountRateSum()))+
                    ",坏账金额为"+dayResult.getAllBadAmount()+
                    "\n";
        }

        if(new BigDecimal(weekResult.getBadAmountRateSum()).compareTo(config.getSevenBadAmountRate()) > 0
            && weekResult.getAllBadAmount().compareTo(new BigDecimal(config.getSevenBadAmount()))>0){
            message = message +"【周】坏账率占比超出预警值"+decimalConvertString(config.getSevenBadAmountRate())+
                    ",且坏账金额大于"+config.getSevenBadAmount()+
                    ",当前值为"+decimalConvertString(new BigDecimal(weekResult.getBadAmountRateSum()))+
                    ",坏账金额为"+weekResult.getAllBadAmount()+
                    "\n";
        }

        BigDecimal shouqiAmount = monthResult.getAllOrderAmount().multiply(new BigDecimal("0.01"));
        BigDecimal warnAmountRate = monthResult.getAllBadAmount().subtract(shouqiAmount).divide(monthResult.getRevenueAmount(), 2, BigDecimal.ROUND_CEILING);
        if(warnAmountRate.compareTo(config.getRevenueRate()) > 0){
            BigDecimal limitAmount = monthResult.getRevenueAmount().multiply(config.getRevenueRate());

            BigDecimal lossAmount = monthResult.getAllBadAmount().subtract(shouqiAmount).subtract(monthResult.getRevenueAmount());
            String isShowText = "";
            if(lossAmount.compareTo(BigDecimal.ZERO) > 0){
                isShowText = ",实际亏损"+lossAmount+"元";
            }
            message = message + "【月】坏账金额为"+monthResult.getAllBadAmount()+"元,上游首汽承担金额"+
                    shouqiAmount+"元,净营收金额为"+
                    monthResult.getRevenueAmount()+"元,当前坏账金额已大于净营收的"+warnAmountRate.multiply(new BigDecimal("100"))+"%"+isShowText+"\n";
        }
        if(new BigDecimal(monthResult.getBadAmountRateSum()).compareTo(config.getThirtyBadAmountRate()) > 0){
            message = message +"【月】坏账率占比超出预警值"+decimalConvertString(config.getThirtyBadAmountRate())+",当前值为"+decimalConvertString(new BigDecimal(monthResult.getBadAmountRateSum()))+"\n";
        }
        WorkWeiXinRobotUtils.sendTextMessage(message,true,null,config.getWorkId());
    }

    public String decimalConvertString(BigDecimal decimal){
        BigDecimal hundred = new BigDecimal("100");
        return decimal.multiply(hundred)+"%";
    }

    public ChannelWarnConfig WarnConfig(){
        int thirtyBadAmount = 2000;
        BigDecimal thirtyBadAmountRate = new BigDecimal("0.02");
        int sevenBadAmount = 3000;
        BigDecimal sevenBadAmountRate = new BigDecimal("0.03");
        int dayBadAmount = 8000;
        BigDecimal dayBadAmountRate = new BigDecimal("0.12");
        BigDecimal revenueRate = new BigDecimal("0.8");
        String workId = "1207024";
        try {
            String channelRiskConfig = ConfigCenterClient.get("channel_driver_risk_config");
            if (StringUtils.isNotBlank(channelRiskConfig)) {
                JSONObject jsonObject = JSON.parseObject(channelRiskConfig);
                thirtyBadAmount = jsonObject.getInteger("thirtyBadAmount");
                thirtyBadAmountRate = jsonObject.getBigDecimal("thirtyBadAmountRate");
                sevenBadAmount = jsonObject.getInteger("sevenBadAmount");
                sevenBadAmountRate = jsonObject.getBigDecimal("sevenBadAmountRate");
                dayBadAmount = jsonObject.getInteger("dayBadAmount");
                dayBadAmountRate = jsonObject.getBigDecimal("dayBadAmountRate");
                revenueRate = jsonObject.getBigDecimal("revenueRate");
                workId = jsonObject.getString("workId");
            }
        } catch (Exception e) {
            log.error("获取预警策略错误",e);
        }
        ChannelWarnConfig config = new ChannelWarnConfig();
        config.setThirtyBadAmount(thirtyBadAmount);
        config.setThirtyBadAmountRate(thirtyBadAmountRate);
        config.setSevenBadAmount(sevenBadAmount);
        config.setSevenBadAmountRate(sevenBadAmountRate);
        config.setDayBadAmount(dayBadAmount);
        config.setDayBadAmountRate(dayBadAmountRate);
        config.setWorkId(workId);
        config.setRevenueRate(revenueRate);
        return config;
    }

    public static void main(String[] args) {
        System.out.println("当前小时:"+new Date().getHours());
    }


}
