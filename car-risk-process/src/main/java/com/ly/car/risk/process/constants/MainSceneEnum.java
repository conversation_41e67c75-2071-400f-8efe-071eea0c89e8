package com.ly.car.risk.process.constants;

import org.apache.commons.lang3.StringUtils;


public enum MainSceneEnum {
    /**
     * 风险主场景枚举 1-营收场景 2-下单场景 3-离线场景 4-其他
     */
    REVENUE(1, "营销场景"),
    PLACE_ORDER(2, "下单场景"),
    OFF_LINE(3, "离线场景"),
    OTHER(4, "其他"),
    SEARCH_PRICE(5,"询价场景"),
    CREATE_ORDER(6,"创单场景"),
    SELF_HITCH(7,"汇川场景"),
    SENSITIVE_INFO(8,"涉敏场景"),
    SAFE_WARNING(9,"安全场景"),
    YANG_ZHAO(10,"扬招场景"),
    MT_SCENE(11,"萌艇场景"),

    COMMON_SCENE(12,"公共场景")
    ;
    private Integer code;
    private String msg;

    MainSceneEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (MainSceneEnum enumItem : MainSceneEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
}
