package com.ly.car.risk.process.service.rule;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.client.AssistServiceClient;
import com.ly.car.risk.process.component.OrderStatusSlidingWindowCounter;
import com.ly.car.risk.process.component.RuleServiceContextUtil;
import com.ly.car.risk.process.component.SendOrderSlidingWindowCounter;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.*;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.controller.request.CashRateQueryRequest;
import com.ly.car.risk.process.controller.request.IsRiskUserRequest;
import com.ly.car.risk.process.controller.request.OrderAcceptCheckRequest;
import com.ly.car.risk.process.controller.request.RiskLevelQueryRequest;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSceneContext;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.*;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.rule.priceGroup.FilterCheckPriceHandler;
import com.ly.car.risk.process.service.rule.sendGroup.FilterSendOrderHandler;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class YncConvertHandlerService {

    @Resource
    private SendOrderSlidingWindowCounter   sendOrderSlidingWindowCounter;
    @Resource
    private OrderStatusSlidingWindowCounter orderStatusSlidingWindowCounter;
    @Resource
    private RiskCustomerService             riskCustomerService;
    @Resource
    private RiskHitService                  riskHitService;
    @Resource
    private AssistServiceClient assistServiceClient;
    @Resource
    private CarOrderService   carOrderService;
    @Resource
    private OrderAddressMapper orderAddressMapper;

    /**
     * 询价场景handler
     * */
    public List<FilterCheckPriceHandler> getCheckPriceHandler(Integer mainScene,Integer childScene){
        List<FilterCheckPriceHandler> serviceNameList = new ArrayList<>();
        List<String> childSceneStr = new ArrayList<>();
        if(childScene == null){
            //先取出主场景下所有子场景
            childSceneStr.addAll(ChildSceneEnum.getAllChildStr(mainScene));
        } else {
            childSceneStr.add(mainScene+"-"+childScene);
        }
        //名单类肯定放第一个
        for(String str : childSceneStr){
            List<String> mapService = RuleServiceContextUtil.serviceNameMap.get(str);
            for(String serviceStr : mapService){
                if(CustomerConstants.customerMap.get(serviceStr) != null){
                    serviceNameList.add(0,SpringContextUtil.getBean(serviceStr));
                } else {
                    serviceNameList.add(SpringContextUtil.getBean(serviceStr));
                }
            }
        }
        //串联链路
        if(!CollectionUtils.isEmpty(serviceNameList)){
            for(int i = 1;i< serviceNameList.size();i++){
                serviceNameList.get(i-1).next(serviceNameList.get(i));
            }
        }
        return serviceNameList;
    }

    /**
     * 场景handler
     * */
    public List<FilterCheckPriceHandler> getHandlers(List<String> scenes) {
        List<FilterCheckPriceHandler> serviceNameList = new ArrayList<>();
        //名单类肯定放第一个
        for (String str : scenes) {
            List<String> mapService = RuleServiceContextUtil.serviceNameMap.get(str);
            for (String serviceStr : mapService) {
                if (CustomerConstants.customerMap.get(serviceStr) != null) {
                    serviceNameList.add(0, SpringContextUtil.getBean(serviceStr));
                } else {
                    serviceNameList.add(SpringContextUtil.getBean(serviceStr));
                }
            }
        }
        //串联链路
        if (!CollectionUtils.isEmpty(serviceNameList) && serviceNameList.size() > 1) {
            for (int i = 1; i < serviceNameList.size(); i++) {
                serviceNameList.get(i - 1).next(serviceNameList.get(i));
            }
        }
        return serviceNameList;
    }

    /**
     * 场景handler
     * */
    public FilterCheckPriceHandler getHandler(List<String> scenes) {
        List<FilterCheckPriceHandler> handlers = getHandlers(scenes);
        Assert.isTrue(CollectionUtils.isNotEmpty(handlers), () -> new RuntimeException("执行引擎不存在"));
        return handlers.get(0);
    }

    /**
     * 下单场景handler
     * */
    public FilterSendOrderHandler getSendOrderHandler(List<String> scenes){
        List<FilterSendOrderHandler> serviceNameList = new ArrayList<>();

        for (String str : scenes) {
            List<String> mapService = RuleServiceContextUtil.serviceNameMap.get(str);
            for (String serviceStr : mapService) {
                if (CustomerConstants.customerMap.get(serviceStr) != null) {
                    serviceNameList.add(0, SpringContextUtil.getBean(serviceStr));
                } else {
                    serviceNameList.add(SpringContextUtil.getBean(serviceStr));
                }
            }
        }
        //串联链路
        if (!CollectionUtils.isEmpty(serviceNameList) && serviceNameList.size()>1) {
            for (int i = 1; i < serviceNameList.size(); i++) {
                serviceNameList.get(i - 1).next(serviceNameList.get(i));
            }
        }

        Assert.isTrue(CollectionUtils.isNotEmpty(serviceNameList), () -> new RuntimeException("执行引擎不存在"));
        return serviceNameList.get(0);
    }

    /**
     * 获取排第一位的名单类
     **/
    public FilterCheckPriceHandler getCheckRiskLevelHandler() {
       return SpringContextUtil.getBean("ruleRisk1001Service");
    }

    /**
     * 下单场景handler
     * */
    public List<FilterSendOrderHandler> getSendOrderHandler(Integer mainScene,Integer childScene){
        List<FilterSendOrderHandler> serviceNameList = new ArrayList<>();
        List<String> childSceneStr = new ArrayList<>();
        if(childScene == null){
            //先取出主场景下所有子场景
            childSceneStr.addAll(ChildSceneEnum.getAllChildStr(mainScene));
        } else {
            childSceneStr.add(mainScene+"-"+childScene);
        }
        for(String str : childSceneStr){
            List<String> mapService = RuleServiceContextUtil.serviceNameMap.get(str);
            for(String serviceStr : mapService){
                if(CustomerConstants.customerMap.get(serviceStr) != null){
                    serviceNameList.add(0,SpringContextUtil.getBean(serviceStr));
                } else {
                    serviceNameList.add(SpringContextUtil.getBean(serviceStr));
                }
            }
        }
        //串联链路
        if(!CollectionUtils.isEmpty(serviceNameList)){
            for(int i = 1;i< serviceNameList.size();i++){
                serviceNameList.get(i-1).next(serviceNameList.get(i));
            }
        }
        return serviceNameList;
    }

    /**去重*/
   public List<SendOrderContext> noRepeat(List<SendOrderContext> contexts){
        if(contexts == null || contexts.size() == 0){
            return new ArrayList<>();
        }
        List<SendOrderContext> newContexts = new ArrayList<>();
        List<String> orderIds = new ArrayList<>();
        //先看有没有重复的，有重复的在重复的里面选
        Map<String,List<SendOrderContext>> map = contexts.stream().collect(Collectors.groupingBy(SendOrderContext::getOrderId));
        for(Map.Entry<String,List<SendOrderContext>> entry : map.entrySet()){
            if(entry.getValue().size() == 1){
                continue;
            }
            for(SendOrderContext context : entry.getValue()){
                if(context.getActualKilo() == null){
                    continue;
                }
                if(context.getActualDuration() == null){
                    continue;
                }
                if(orderIds.contains(context.getOrderId())){
                    continue;
                } else {
                    orderIds.add(context.getOrderId());
                    newContexts.add(context);
                }
            }
        }
        return newContexts;
   }


    /**
     * 询价参数
     * */
    public FilterSceneContext convertCheckRiskLevelCtx(RiskLevelQueryRequest request) throws Exception {
        FilterSceneContext context = new FilterSceneContext();
        CommonCustomerParam cusParams = new CommonCustomerParam();
        cusParams.setDeviceId(request.getDeviceId());
        cusParams.setMemberId(request.getMemberId());
        cusParams.setUnionId(request.getUnionId());
        cusParams.setUserPhone(request.getUserPhone());
        cusParams.setPassengerCellphone(request.getPassengerCellphone());
        Date date = new Date();
        List<RiskCustomerManage> listByValueByGroup = riskCustomerService.getListByValueByGroup(cusParams, date);
        context.setRiskCustomerManageList(listByValueByGroup);
        context.setMemberId(request.getMemberId());
        context.setUnionId(request.getUnionId());
        context.setRateMap(getRateConfig());//获取配置
        return context;
    }

    /**
     * 下单场景参数
     * */
    public FilterSendOrderContext convertSendOrder(IsRiskUserRequest request){
        long startMs = TimeUtil.threeDayMs();
        FilterSendOrderContext context = new FilterSendOrderContext();
        context.setPayAccountList(new ArrayList<>());
        context.setDeviceList(new ArrayList<>());


        if (StringUtils.isNotBlank(request.getUnionId())) {
            List<SendOrderContext> list = sendOrderSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_UNION_ID_WINDOW + request.getUnionId(), startMs);
            context.setMemberList(noRepeat(list));
            context.setOrderStatusDTOList(orderStatusSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_ORDER_NUMBER_UNION + request.getUnionId(), startMs));
        } else if (StringUtils.isNotBlank(request.getMemberId())) {
            List<SendOrderContext> list = sendOrderSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_MEMBER_WINDOW + request.getMemberId(), startMs);
            context.setMemberList(noRepeat(list));
            context.setOrderStatusDTOList(orderStatusSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_ORDER_NUMBER_MEMBER + request.getMemberId(), startMs));
        } else {
            context.setMemberList(new ArrayList<>());
        }

        context.setDriverList(new ArrayList<>());

        if (StringUtils.isNotBlank(request.getOrderId())) {
            // 新订单
            if (request.getOrderId().startsWith("YC")) {
                CarOrderDetail order = carOrderService.queryOrderDetail(request.getOrderId());
                if (null != order && null != order.getOrderTrip()) {
                    request.setStartLng(null == order.getOrderTrip().getDepartureLng() ? "" : order.getOrderTrip().getDepartureLng().toString());
                    request.setStartLat(null == order.getOrderTrip().getDepartureLat() ? "" : order.getOrderTrip().getDepartureLat().toString());
                    request.setEndLng(null == order.getOrderTrip().getArrivalLng() ? "" : order.getOrderTrip().getArrivalLng().toString());
                    request.setEndLat(null == order.getOrderTrip().getArrivalLat() ? "" : order.getOrderTrip().getArrivalLat().toString());
                }
            } else {
                // 旧订单
                if (request.getOrderId().startsWith("YNC")) {
                    OrderAddress orderAddress = orderAddressMapper.findByOrderId(request.getOrderId());
                    if (orderAddress != null) {
                        request.setStartLng(null == orderAddress.getStartLng() ? "" : orderAddress.getStartLng().toString());
                        request.setStartLat(null == orderAddress.getStartLat() ? "" : orderAddress.getStartLat().toString());
                        request.setEndLng(null == orderAddress.getEndLng() ? "" : orderAddress.getEndLng().toString());
                        request.setEndLat(null == orderAddress.getEndLat() ? "" : orderAddress.getEndLat().toString());
                    }
                }
            }
        }

        //组装规则配置
        SpecialCarRuleConfig specialConfig = getSpecialConfig();
        context.setSpecialCarRuleConfig(specialConfig);
        context.setRuleList(new ArrayList<>());
        context.setOrderId(request.getOrderId());
        context.setUserPhone(request.getUserPhone());
        context.setPassengerCellphone(request.getPassengerCellphone());
        context.setDeviceId("");
        context.setMemberId(request.getMemberId());
        context.setDriverCardNo("");
        context.setUnionId(request.getUnionId());
        context.setMainScene(request.getMainScene());
        context.setChildScene(request.getChildScene());
        context.setStartLat(request.getStartLat());
        context.setStartLng(request.getStartLng());
        context.setEndLat(request.getEndLat());
        context.setEndLng(request.getEndLng());

        FilterParams params = new FilterParams();
        params.setOrderId(request.getOrderId());
        params.setMainScene(request.getMainScene());
        params.setChildScene(request.getChildScene());
        params.setMemberId(request.getMemberId());
        params.setDriverCardNo("");
        params.setProductLine(request.getProductLine());
        params.setRequestId(request.getRequestId());
        params.setUnionId(request.getUnionId());
        params.setStartLat(request.getStartLat());
        params.setStartLng(request.getStartLng());
        params.setEndLat(request.getEndLat());
        params.setEndLng(request.getEndLng());
        params.setUserPhone(request.getUserPhone());
        params.setPassengerCellphone(request.getPassengerCellphone());
        String carNewFlag = assistServiceClient.unifyNewJudge(request.getMemberId(), request.getUnionId(), request.getChannel());
        params.setIsNewUser(Objects.equals(carNewFlag, "1") ? 1 : 0);
        context.setParams(params);
        return context;
    }

    /**
     * 下单场景参数
     * */
    public FilterSendOrderContext convertSendOrder(OrderAcceptCheckRequest request,boolean isDistributionOrder){
        long startMs = TimeUtil.threeDayMs();
        FilterSendOrderContext context = new FilterSendOrderContext();
        context.setPayAccountList(new ArrayList<>());
        context.setDeviceList(new ArrayList<>());

        // 如果不是分销单，
        if(!isDistributionOrder){
            if (StringUtils.isNotBlank(request.getUnionId())) {
                List<SendOrderContext> list = sendOrderSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_UNION_ID_WINDOW + request.getUnionId(), startMs);
                context.setMemberList(noRepeat(list));
                context.setOrderStatusDTOList(orderStatusSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_ORDER_NUMBER_UNION + request.getUnionId(), startMs));
            } else if (StringUtils.isNotBlank(request.getMemberId())) {
                List<SendOrderContext> list = sendOrderSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_MEMBER_WINDOW + request.getMemberId(), startMs);
                context.setMemberList(noRepeat(list));
                context.setOrderStatusDTOList(orderStatusSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_ORDER_NUMBER_MEMBER + request.getMemberId(), startMs));
            } else {
                context.setMemberList(new ArrayList<>());
            }
        }

        if(StringUtils.isNotBlank(request.getDriverCardNo())){
            List<SendOrderContext> list = sendOrderSlidingWindowCounter.getCommonWindow(RedisKeyConstants.DRIVER_CARD_WINDOW+request.getDriverCardNo(),startMs);
            context.setDriverList(list);
        } else {
            context.setDriverList(new ArrayList<>());
        }

        if (StringUtils.isNotBlank(request.getOrderId())) {
            // 新订单
            if (request.getOrderId().startsWith("YC")) {
                CarOrderDetail order = carOrderService.queryOrderDetail(request.getOrderId());
                if (null != order && null != order.getOrderTrip()
                        && Objects.equals(order.getProductLine(), ProductLineEnum.YNC.getCode())) {
                    request.setStartLng(null == order.getOrderTrip().getDepartureLng() ? "" : order.getOrderTrip().getDepartureLng().toString());
                    request.setStartLat(null == order.getOrderTrip().getDepartureLat() ? "" : order.getOrderTrip().getDepartureLat().toString());
                    request.setEndLng(null == order.getOrderTrip().getArrivalLng() ? "" : order.getOrderTrip().getArrivalLng().toString());
                    request.setEndLat(null == order.getOrderTrip().getArrivalLat() ? "" : order.getOrderTrip().getArrivalLat().toString());
                }
            } else {
                // 旧订单
                if (request.getOrderId().startsWith("YNC")) {
                    OrderAddress orderAddress = orderAddressMapper.findByOrderId(request.getOrderId());
                    if (orderAddress != null) {
                        request.setStartLng(null == orderAddress.getStartLng() ? "" : orderAddress.getStartLng().toString());
                        request.setStartLat(null == orderAddress.getStartLat() ? "" : orderAddress.getStartLat().toString());
                        request.setEndLng(null == orderAddress.getEndLng() ? "" : orderAddress.getEndLng().toString());
                        request.setEndLat(null == orderAddress.getEndLat() ? "" : orderAddress.getEndLat().toString());
                    }
                }
            }
        }

        //组装规则配置
        SpecialCarRuleConfig specialConfig = getSpecialConfig();
        context.setSpecialCarRuleConfig(specialConfig);
        context.setRuleList(new ArrayList<>());
        context.setOrderId(request.getOrderId());
        context.setUserPhone(request.getUserPhone());
        context.setPassengerCellphone(request.getPassengerCellphone());
        context.setDeviceId("");
        context.setMemberId(request.getMemberId());
        context.setDriverCardNo(request.getCarNum());
        context.setUnionId(request.getUnionId());
        context.setMainScene(request.getMainScene());
        context.setChildScene(request.getChildScene());
        context.setStartLat(request.getStartLat());
        context.setStartLng(request.getStartLng());
        context.setEndLat(request.getEndLat());
        context.setEndLng(request.getEndLng());

        FilterParams params = new FilterParams();
        params.setOrderId(request.getOrderId());
        params.setMainScene(request.getMainScene());
        params.setChildScene(request.getChildScene());
        params.setMemberId(request.getMemberId());
        params.setDriverCardNo(request.getCarNum());
        params.setProductLine(request.getProductLine());
        params.setRequestId(request.getRequestId());
        params.setUnionId(request.getUnionId());
        params.setStartLat(request.getStartLat());
        params.setStartLng(request.getStartLng());
        params.setEndLat(request.getEndLat());
        params.setEndLng(request.getEndLng());
        params.setUserPhone(request.getUserPhone());
        params.setPassengerCellphone(request.getPassengerCellphone());
        params.setDistributionOrder(isDistributionOrder);
        context.setParams(params);
        return context;
    }

    /**
     * 下单场景参数
     * */
    public FilterSendOrderContext convertSendOrder(FilterParams params){
        long startMs = TimeUtil.threeDayMs();
        FilterSendOrderContext context = new FilterSendOrderContext();
        if(StringUtils.isNotBlank(params.getPayAccount())){
            List<SendOrderContext> list = sendOrderSlidingWindowCounter.getCommonWindow(RedisKeyConstants.PAY_ACCOUNT_WINDOW+params.getPayAccount(),startMs);
            context.setPayAccountList(noRepeat(list));
        } else {
            context.setPayAccountList(new ArrayList<>());
        }
        if(StringUtils.isNotBlank(params.getDeviceId())){
            List<SendOrderContext> list = sendOrderSlidingWindowCounter.getCommonWindow(RedisKeyConstants.DEVICE_ID_WINDOW+params.getDeviceId(),startMs);
            context.setDeviceList(noRepeat(list));
        } else {
            context.setDeviceList(new ArrayList<>());
        }

        if(StringUtils.isNotBlank(params.getUnionId())){
            List<SendOrderContext> list = sendOrderSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_UNION_ID_WINDOW+params.getUnionId(),startMs);
            context.setMemberList(noRepeat(list));
            context.setOrderStatusDTOList(orderStatusSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_ORDER_NUMBER_UNION+params.getUnionId(),startMs));
        } else if(StringUtils.isNotBlank(params.getMemberId())){
            List<SendOrderContext> list = sendOrderSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_MEMBER_WINDOW+params.getMemberId(),startMs);
            context.setMemberList(noRepeat(list));
            context.setOrderStatusDTOList(orderStatusSlidingWindowCounter.getCommonWindow(RedisKeyConstants.USER_ORDER_NUMBER_MEMBER+params.getMemberId(),startMs));
        } else {
            context.setMemberList(new ArrayList<>());
        }

        if(StringUtils.isNotBlank(params.getDriverCardNo())){
            List<SendOrderContext> list = sendOrderSlidingWindowCounter.getCommonWindow(RedisKeyConstants.DRIVER_CARD_WINDOW+params.getDriverCardNo(),startMs);
            context.setDriverList(list);
        } else {
            context.setDriverList(new ArrayList<>());
        }
        //组装规则配置
        SpecialCarRuleConfig specialConfig = getSpecialConfig();
        context.setSpecialCarRuleConfig(specialConfig);
        context.setRuleList(new ArrayList<>());
        context.setOrderId(params.getOrderId());
        context.setUserPhone(params.getUserPhone());
        context.setPassengerCellphone(params.getPassengerCellphone());
        context.setDeviceId(params.getDeviceId());
        context.setMemberId(params.getMemberId());
        context.setDriverCardNo(params.getDriverCardNo());
        context.setUnionId(params.getUnionId());
        context.setMainScene(params.getMainScene());
        context.setChildScene(params.getChildScene());
        context.setStartLat(params.getStartLat());
        context.setStartLng(params.getStartLng());
        context.setEndLat(params.getEndLat());
        context.setEndLng(params.getEndLng());
        context.setParams(params);
        return context;
    }

    /**
     * 询价参数
     * */
    public FilterSceneContext convertCheckPrice(FilterParams params) throws Exception {
        FilterSceneContext context = new FilterSceneContext();
        CommonCustomerParam cusParams = new CommonCustomerParam();
        cusParams.setDeviceId(params.getDeviceId());
        cusParams.setMemberId(params.getMemberId());
        cusParams.setUnionId(params.getUnionId());
        cusParams.setUserPhone(params.getUserPhone());
        cusParams.setPassengerCellphone(params.getPassengerCellphone());
        Date date = new Date();
        List<RiskCustomerManage> listByValueByGroup = riskCustomerService.getListByValueByGroup(cusParams, date);
        context.setRiskCustomerManageList(listByValueByGroup);
        context.setOrderId(params.getOrderId());
        context.setMainScene(params.getMainScene());
        context.setChildScene(params.getChildScene());
        context.setPayAccount(params.getPayAccount());
        context.setMemberId(params.getMemberId());
        context.setUnionId(params.getUnionId());
        context.setIsNewUser(params.getIsNewUser());
        context.setEsAmount(params.getEsAmount());
        context.setRateMap(getRateConfig());//获取配置
        context.setSpecialCarRuleConfig(getSpecialConfig());
        return context;
    }

    /**
     * 询价参数
     * */
    public FilterSceneContext convertCheckPrice(CashRateQueryRequest request) throws Exception {
        FilterSceneContext context = new FilterSceneContext();
        CommonCustomerParam cusParams = new CommonCustomerParam();
        cusParams.setDeviceId(request.getDeviceId());
        cusParams.setMemberId(request.getMemberId());
        cusParams.setUnionId(request.getUnionId());
        cusParams.setUserPhone(request.getUserPhone());
        cusParams.setPassengerCellphone(request.getPassengerCellphone());
        Date date = new Date();
        List<RiskCustomerManage> listByValueByGroup = riskCustomerService.getListByValueByGroup(cusParams, date);
        context.setRiskCustomerManageList(listByValueByGroup);
        context.setMemberId(request.getMemberId());
        context.setUnionId(request.getUnionId());
        context.setEsAmount(request.getEsAmount());
        context.setRateMap(getRateConfig());//获取配置
        context.setSpecialCarRuleConfig(getSpecialConfig());
        String carNewFlag = assistServiceClient.unifyNewJudge(request.getMemberId(), request.getUnionId(), request.getChannel());
        context.setIsNewUser(Objects.equals(carNewFlag, "1") ? 1 : 0);
        return context;
    }

    /**
     * 专车接入风控
     * */
    public SpecialCarRuleConfig getSpecialConfig(){
        try {
            String configJson = ConfigCenterClient.get("special_car_rule_config");
            SpecialCarRuleConfig config = JSONObject.parseObject(configJson,SpecialCarRuleConfig.class);
            log.info("[][][][]获取专车接入规则配置:{}",configJson);
            log.info("[][][][]获取专车接入规则配置转换:{}",JsonUtils.json(config));
            return config;
        } catch (Exception e) {
            log.error("获取专车接入规则配置错误:",e);
        }
        return null;
    }

    /**
     * 询价场景配置
     * */
    public Map<String, String> getRateConfig(){
        try {
            String configJson = ConfigCenterClient.get("price_rule_rate");
            log.info("获取询价价格系数:"+configJson);
            Map<String,String> map = JSONObject.parseObject(configJson,Map.class);
            return map;
        } catch (Exception e) {
            log.error("获取价格系数错误:",e);
        }
        return new HashMap<>();
    }

    /**
     * 专车场景返回值封装
     * */
    public UiResult convertResult(FilterSendOrderContext context){
        UiResult result = UiResult.ok();
        RiskResultDTO dto = new RiskResultDTO();
        dto.setLevel(0);
        if (context.getRuleList().size() == 0) {
            result.setData(dto);
        } else if (context.getRuleList().size() == 1) {
            if (context.getRuleList().get(0).getLevel() == 5) {
                dto = new RiskResultDTO(405, "", context.getRuleList().get(0).getRuleNo(), "", 2);
                result.setData(dto);
            } else {
                dto = new RiskResultDTO(405, "", context.getRuleList().get(0).getRuleNo(), "", 1);
                result.setData(dto);
            }
            if (context.getRuleList().get(0).getRuleNo().equals("1003") || context.getRuleList().get(0).getRuleNo().equals("2001")) {
                // 1003是用户下单判断黑名单的，  2001是司机接单时在黑名单的。 也就是当不在黑名单时，走initHitRisk去加入黑名单
            } else {
                riskHitService.initHitRisk(context.getParams(), new HitInfoDTO(context.getRuleList().get(0).getRuleNo(), RiskLevelEnum.HIGH.getCode(), 0, null, result));
            }
        } else {
            String ruleNoList = StringUtils.join(context.getRuleList().stream().map(RuleChain::getRuleNo).distinct().collect(Collectors.toList()), ",");
            dto = new RiskResultDTO(405, "", ruleNoList, "", 2);
            result.setData(dto);
            riskHitService.initHitRisk(context.getParams(), new HitInfoDTO(ruleNoList, RiskLevelEnum.HIGH.getCode(), 0, null, result));
        }
        return result;
    }

    public UiResult convertRiskFlag(UiResult<RiskResultDTO> result) {
        RiskResultDTO dto = result.getData();
        dto.setRiskFlag(dto.getCode() != 0 ? 1 : 0);
        result.setData(dto);
        return result;
    }

    public static void main(String[] args) {
        List<String> strList = new ArrayList<>();
        strList.add("111");
        strList.add("222");
        strList.add(0,"000");
        System.out.println(JsonUtils.json(strList));
    }
}
