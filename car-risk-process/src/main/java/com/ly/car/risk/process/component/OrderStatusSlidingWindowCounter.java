package com.ly.car.risk.process.component;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.service.redis.OrderPassengerCellPhone;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import com.ly.car.risk.process.service.redis.OrderStatusDTO;
import com.ly.car.risk.process.utils.TimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Component
public class OrderStatusSlidingWindowCounter {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    public List<OrderStatusDTO> getCommonWindow(String key, long startMs){
        //当前时间
        long currentTime = System.currentTimeMillis();
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        //优化下节省内存
        long initMs = TimeUtil.initMs();
        scoredSortedSet.removeRangeByScore(initMs,true,startMs,false);
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, currentTime, true);
        List<OrderStatusDTO> objList = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            OrderStatusDTO context = JSONObject.parseObject(obj.getValue().toString(),OrderStatusDTO.class);
            objList.add(context);
        }
        return objList;
    }

    public List<OrderStatusCancelDTO> getCancelList(String key,long startMs){
        long currentTime = System.currentTimeMillis();
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        long initMs = TimeUtil.initMs();
        scoredSortedSet.removeRangeByScore(initMs,true,startMs,false);
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, currentTime, true);
        List<OrderStatusCancelDTO> objList = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            OrderStatusCancelDTO context = JSONObject.parseObject(obj.getValue().toString(),OrderStatusCancelDTO.class);
            objList.add(context);
        }
        return objList;
    }

    public List<OrderPassengerCellPhone> getPhoneOrderList(String key,long startMs){
        long currentTime = System.currentTimeMillis();
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        long initMs = TimeUtil.initMs();
        scoredSortedSet.removeRangeByScore(initMs,true,startMs,false);
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, currentTime, true);
        List<OrderPassengerCellPhone> objList = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            OrderPassengerCellPhone context = JSONObject.parseObject(obj.getValue().toString(),OrderPassengerCellPhone.class);
            objList.add(context);
        }
        return objList;
    }
}
