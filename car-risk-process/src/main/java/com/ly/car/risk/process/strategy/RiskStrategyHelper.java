package com.ly.car.risk.process.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.MetricFieldMapper;
import com.ly.car.risk.process.repo.risk.mapper.MetricSceneStrategyRelationMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.MetricField;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskRuleFieldDTO;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskSceneStrategyDTO;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskStrategyRuleDTO;
import com.ly.car.risk.process.strategy.model.RiskStrategyDetail;
import com.ly.car.risk.process.strategy.model.detail.RiskFieldDetail;
import com.ly.car.risk.process.strategy.model.detail.RiskRuleDetail;
import com.ly.car.risk.process.utils.LoggerUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

/**
 * Description of RiskStrategryHelper
 *
 * <AUTHOR>
 * @date 2024/5/31
 * @desc
 */
@Service
@Slf4j
public class RiskStrategyHelper implements InitializingBean {

    @Resource
    private MetricSceneStrategyRelationMapper sceneStrategyRelationMapper;
    @Resource
    private MetricFieldMapper fieldMapper;

    private CopyOnWriteArrayList<RiskStrategyDetail> riskStrategy = new CopyOnWriteArrayList<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(1);
        executorService.scheduleWithFixedDelay(() -> initRiskStrategy(), 0, 1, TimeUnit.MINUTES);
    }

    private void initRiskStrategy() {
        LoggerUtils.initLogMap("STRATEGY_INIT", "", "", "");
        List<RiskStrategyDetail> riskStrategyList = new ArrayList<>();
        try {
            //
            List<RiskSceneStrategyDTO> sceneStrategyList = sceneStrategyRelationMapper.findSceneStrategy();
            // 将策略和规则的关系查出来
            List<RiskStrategyRuleDTO> strategyRuleList = sceneStrategyRelationMapper.findStrategyRule();
            Map<Long, List<RiskStrategyRuleDTO>> strategyRuleMap = strategyRuleList.stream().collect(Collectors.groupingBy(RiskStrategyRuleDTO::getStrategyId));
            // 规则和指标的关系查出来
            Map<Long, List<RiskRuleFieldDTO>> ruleFieldMap = sceneStrategyRelationMapper.findRuleField().stream().collect(Collectors.groupingBy(RiskRuleFieldDTO::getRuleId));
            //
            Map<Long, MetricField> fieldMap = fieldMapper.selectList(new QueryWrapper<>()).stream().collect(Collectors.toMap(p -> p.getId(), v -> v, (k1, k2) -> k1));

            for (RiskSceneStrategyDTO sceneStrategy : sceneStrategyList) {
                RiskStrategyDetail riskStrategy = new RiskStrategyDetail();
                riskStrategyList.add(riskStrategy);
                // 1.策略基础内容
                BeanUtils.copyProperties(sceneStrategy, riskStrategy);
                riskStrategy.setProductLine(StrUtil.split(sceneStrategy.getProductLines(), ",", true, true));
                riskStrategy.setChannels(StrUtil.split(sceneStrategy.getChannels(), ",", true, true));
                riskStrategy.setSupplierCodes(StrUtil.split(sceneStrategy.getSupplierCodes(), ",", true, true));
                List<RiskRuleDetail> ruleDetails = new ArrayList<>();
                riskStrategy.setRules(ruleDetails);
                // 2.查出规则
                List<RiskStrategyRuleDTO> ruleList = strategyRuleMap.get(sceneStrategy.getStrategyId());
                if(CollUtil.isEmpty(ruleList)){
                    continue;
                }
                for (RiskStrategyRuleDTO strategyRule : ruleList) {
                    RiskRuleDetail riskRuleDetail = new RiskRuleDetail();
                    ruleDetails.add(riskRuleDetail);
                    BeanUtils.copyProperties(strategyRule, riskRuleDetail);
                    // 3.规则查指标
                    List<RiskRuleFieldDTO> ruleFieldList = ruleFieldMap.get(strategyRule.getRuleId());
                    if (CollUtil.isEmpty(ruleFieldList)) {
                        continue;
                    }
                    List<RiskFieldDetail> riskFields = ruleFieldList.stream().map(ruleField -> {
                        RiskFieldDetail fieldDetail = new RiskFieldDetail();
                        fieldDetail.setFieldId(String.valueOf(ruleField.getField()));
                        fieldDetail.setOperator(ruleField.getOperator());
                        fieldDetail.setRightType(ruleField.getRightType());
                        fieldDetail.setRightValue(ruleField.getRightValue());
                        fieldDetail.setSort(ruleField.getSort());
                        MetricField metricField = fieldMap.get(ruleField.getField());
                        if (null != metricField) {
                            fieldDetail.setFieldNo(metricField.getFieldNo());
                            fieldDetail.setScript(metricField.getScript());
                            fieldDetail.setCategory(metricField.getCategory());
                            fieldDetail.setType(metricField.getType());
                            fieldDetail.setTarget(metricField.getTarget());
                            fieldDetail.setBasedCurrent(metricField.getBasedCurrent());
                        }
                        return fieldDetail;
                    }).collect(Collectors.toList());
                    riskRuleDetail.setRiskFields(riskFields);
                }
            }

            riskStrategy = new CopyOnWriteArrayList<>(riskStrategyList);

        } catch (Exception ex) {
            LoggerUtils.error(log, "策略初始化 异常", ex);
        } finally {
            LoggerUtils.info(log,"策略初始化结束，初始化策略条数：{}",riskStrategy.size());
            LoggerUtils.removeAll();
        }

    }
    
    public List<RiskStrategyDetail> findStrategy(String productLine, String channel, String scene, String supplierCode) {
        List<RiskStrategyDetail> strategyList = riskStrategy.stream().filter(p -> p.getProductLine().contains(productLine)
                        && (StringUtils.isBlank(channel) || p.getChannels().contains(channel) || p.getChannels().contains("all"))
                        && Objects.equals(p.getScene(), scene))
                .collect(Collectors.toList());
        
        // 供应商为空不过滤
        if (StringUtils.isBlank(supplierCode)) {
            return strategyList;
        }
        
        // 精准过滤出策略才执行
        List<RiskStrategyDetail> supplierStrategylist = strategyList.stream().filter(s -> s.getSupplierCodes().contains(supplierCode)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(supplierStrategylist)) {
            return supplierStrategylist;
        }
        
        return strategyList;
    }

}