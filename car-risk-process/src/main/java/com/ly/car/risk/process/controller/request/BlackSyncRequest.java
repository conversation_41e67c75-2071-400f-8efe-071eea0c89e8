package com.ly.car.risk.process.controller.request;

import java.util.List;
import lombok.Data;

/**
 * Description of BatchCarBlackInfoQueryRequest
 *
 * <AUTHOR>
 * @date 2024/4/16
 * @desc
 */
@Data
public class BlackSyncRequest extends BaseRequest {
    
    /**
     * 1:腾讯出行黑名单同步
     */
    private Integer fromType;
    /**
     * 操作类型
     * 1: 全量同步（没有）
     * 2: 新增(更新)
     * 3: 删除
     */
    private Integer operationType;
    
    private List<DriverBlackVO> driverBlacklist;
    
    @Data
    public static class DriverBlackVO {
        
        /**
         * 车牌
         */
        private String plateNo;
        /**
         * 乘客手机号
         */
        private String passengerPhone;
        /**
         * 拉黑原因
         */
        private String controlReason;
        /**
         * 拉黑时间 毫秒 -1 表示永久
         */
        private Long   expireTime;
        
    }
}