package com.ly.car.risk.process.service.rule.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CommonHandlerMap {

    public static Map<String, List<String>> serviceNameMap;

    static {
        Map<String,List<String>> map = new HashMap<>();

        List<String> COMMON_CERTIFICATION = new ArrayList<>();
        COMMON_CERTIFICATION.add("driverIdCardService");
        COMMON_CERTIFICATION.add("driverScoreService");
//        COMMON_CERTIFICATION.add("driverLicenseInfoService");
        COMMON_CERTIFICATION.add("driverIdCardService");
        map.put("12-1",COMMON_CERTIFICATION);

        List<String> COMMON_BLACK_CARD = new ArrayList<>();
        COMMON_BLACK_CARD.add("driverBankCardService");
        map.put("12-2",COMMON_BLACK_CARD);
        serviceNameMap = map;
    }
}
