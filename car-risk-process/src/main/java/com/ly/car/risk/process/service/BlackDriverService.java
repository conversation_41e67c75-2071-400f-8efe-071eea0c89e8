package com.ly.car.risk.process.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerCustomerTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerOptionTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerStatusEnum;
import com.ly.car.risk.process.constants.RiskCustomerTtlEnum;
import com.ly.car.risk.process.controller.params.DriverBlackParam;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcSupplierOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskCustomerRecord;
import com.ly.car.risk.process.service.dto.RiskCustomerManageListDTO;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.dal.util.DateUtil;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BlackDriverService {

    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private SfcSupplierOrderMapper sfcSupplierOrderMapper;
    @Resource
    private RiskCustomerRecordMapper riskCustomerRecordMapper;
    @Resource
    private CarOrderService          carOrderService;

    /**
     * 用户拉黑司机
     * */
    public UiResult userShield(DriverBlackParam param){
        UiResult uiResult = UiResult.ok();
        List<RiskCustomerManage> manages = this.riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>()
                        .eq("bind_user",param.getPassengerCellphone())
                        .eq("customer_value",param.getDriverCardNo())
                        .gt("invalid_time",new Date())
        );
        manages = manages.stream().sorted(Comparator.comparing(RiskCustomerManage::getInvalidTime).reversed()).collect(Collectors.toList());
        RiskCustomerManage manage = CollectionUtils.isEmpty(manages) ? null : manages.get(0);
        
        //有效期内存在，则更新过期时间
        if(manage != null){
            manage.setInvalidTime(DateUtil.addMonth(new Date(),12));
            manage.setUpdateTime(new Date());
            manage.setBindOrder(param.getOrderId());
            this.riskCustomerManageMapper.updateById(manage);
            RiskCustomerRecord record = new RiskCustomerRecord();
            record.setCustomerId(manage.getId());
            record.setOperateType(1);//修改
            record.setCreateUser(manage.getCreateUser());
            record.setOperateUser("用户");
            record.setCustomerType(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode());
            record.setRemark(param.getRemark());
            record.setCreateTime(new Date());
            record.setCustomerValue(manage.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
            return uiResult;
        }
        //先看下当前有没有拉黑的
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        riskCustomerManage.setRiskType(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode());
        riskCustomerManage.setCustomerType(RiskCustomerCustomerTypeEnum.car_number.getCode());
        riskCustomerManage.setCustomerValue(param.getDriverCardNo());
        riskCustomerManage.setStatus(RiskCustomerStatusEnum.valid.getCode());
        riskCustomerManage.setTtl(RiskCustomerTtlEnum.one_year.getCode());
        riskCustomerManage.setOptionType(RiskCustomerOptionTypeEnum.user_client.getCode());
        riskCustomerManage.setOptionName("用户");
        riskCustomerManage.setCreateUser("用户");
        riskCustomerManage.setRiskRemark(param.getRemark());
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        riskCustomerManage.setInvalidTime(DateUtil.addMonth(new Date(),12));
        riskCustomerManage.setBindUser(param.getPassengerCellphone());//手机号
        riskCustomerManage.setBindOrder(param.getOrderId());
        riskCustomerManage.setDriverName(param.getDriverName());
        riskCustomerManage.setStartAddress(param.getStartAddress());
        riskCustomerManage.setEndAddress(param.getEndAddress());
        riskCustomerManage.setUseTime(param.getUseTime());
        //顺风车的去看下有没有driverId
        if(StringUtils.isNotBlank(param.getOrderId())){
            // 新订单
            if (OrderUtils.isNewOrder(param.getOrderId())) {

                CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(param.getOrderId());

                if (null != carOrderDetail && null != carOrderDetail.getCarInfo()) {
                    riskCustomerManage.setDriverId(carOrderDetail.getCarInfo().getDriverCode());
                }

            } else if(param.getOrderId().startsWith("SFC")){
                // 旧订单
                SfcOrder sfcOrder = this.sfcOrderMapper.queryByOrderId(param.getOrderId());

                if(sfcOrder != null && StringUtils.isNotBlank(param.getOrderId())){
                    SfcSupplierOrder sfcSupplierOrder = this.sfcSupplierOrderMapper.selectOne(new QueryWrapper<SfcSupplierOrder>()
                            .eq("order_id",sfcOrder.getOrderId()).eq("supplier_order_id",sfcOrder.getSupplierOrderId())
                    );
                    riskCustomerManage.setDriverId(sfcSupplierOrder.getDriverId());
                }
            }
        }
        this.riskCustomerManageMapper.insert(riskCustomerManage);

        RiskCustomerRecord record = new RiskCustomerRecord();
        record.setCustomerId(riskCustomerManage.getId());
        record.setOperateType(1);//修改
        record.setCreateUser("用户");
        record.setOperateUser("用户");
        record.setCustomerType(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode());
        record.setRemark(param.getRemark());
        record.setCreateTime(new Date());
        record.setCustomerValue(riskCustomerManage.getCustomerValue());
        riskCustomerRecordMapper.insert(record);
        return uiResult;
    }

    /**
     * 用户移除1v1黑名单司机
     * */
    public UiResult userRemove(DriverBlackParam param){
        UiResult result = UiResult.ok();
        RiskCustomerManage riskCustomerManage = this.riskCustomerManageMapper.selectOne(
                new QueryWrapper<RiskCustomerManage>()
                        .eq("bind_user",param.getPassengerCellphone())
                        .eq("customer_value",param.getDriverCardNo())
                        .gt("invalid_time",new Date())
        );
        if (riskCustomerManage != null) {
            riskCustomerManage.setInvalidTime(DateUtil.addMinute(new Date(),-1));
            riskCustomerManage.setUpdateTime(new Date());
            this.riskCustomerManageMapper.updateById(riskCustomerManage);

            RiskCustomerRecord record = new RiskCustomerRecord();
            record.setCustomerId(riskCustomerManage.getId());
            record.setOperateType(2);//修改
            record.setCreateUser(riskCustomerManage.getCreateUser());
            record.setOperateUser("用户");
            record.setCustomerType(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode());
            record.setRemark(param.getRemark());
            record.setCreateTime(new Date());
            record.setCustomerValue(riskCustomerManage.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
        }
        return result;

    }

    /**
     * 用户司机黑名单列表
     * */
    public UiResult getList(DriverBlackParam param){
        if(StringUtils.isBlank(param.getPassengerCellphone())){
            return UiResult.ok();
        }
        UiResult result = UiResult.ok();
        List<RiskCustomerManage> manageList = this.riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>()
                        .eq("bind_user",param.getPassengerCellphone())
                        .gt("invalid_time",new Date())
        );
        List<RiskCustomerManageListDTO> dtoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(manageList)){
            for(RiskCustomerManage manage : manageList){
                if(StringUtils.isBlank(manage.getBindOrder()) ||StringUtils.isBlank(manage.getStartAddress())){
                    continue;
                }
                RiskCustomerManageListDTO dto = new RiskCustomerManageListDTO();
                dto.setInvalidTime(DateUtil.date2String(manage.getInvalidTime()));
                dto.setDriverName(manage.getDriverName());
                dto.setCustomerValue(manage.getCustomerValue());
                dto.setOrderId(manage.getBindOrder());
                dto.setShieldTime(DateUtil.date2String(manage.getUpdateTime()));
                dto.setStartAddress(manage.getStartAddress());
                dto.setEndAddress(manage.getEndAddress());
                dto.setUseTime(manage.getUseTime());
                dto.setDriverCardNo(manage.getCustomerValue());
                dtoList.add(dto);
            }
        }
        result.setData(dtoList);
        return result;
    }

    public UiResult orderBlack(DriverBlackParam param){
        UiResult result = UiResult.ok();
        if(StringUtils.isBlank(param.getOrderId())){
            result.setData(false);
            return result;
        }
        List<RiskCustomerManage> manageList = this.riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>()
                        .eq("bind_order",param.getOrderId())
                        .eq("customer_value",param.getDriverCardNo())
                        .gt("invalid_time",new Date())
        );
        if(manageList != null && manageList.size() > 0){
            result.setData(true);
            return result;
        }
        result.setData(false);
        return result;
    }

}
