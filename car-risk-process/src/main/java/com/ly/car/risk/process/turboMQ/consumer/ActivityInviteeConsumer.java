package com.ly.car.risk.process.turboMQ.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.repo.risk.mapper.ActivityRelationMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.ActivityRelation;
import com.ly.car.risk.process.turboMQ.dto.ActivityInvitee;
import com.ly.car.risk.process.turboMQ.dto.BingLogData;
import com.ly.car.risk.process.utils.BingLogUtil;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class ActivityInviteeConsumer implements MessageListenerConcurrently {

    private ActivityRelationMapper activityRelationMapper;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for(MessageExt messageExt : list){
            try {
                String body = new String(messageExt.getBody(),"utf-8");
                log.info("[binlog][ActivityInviteeConsumer][][][]收到turbo消息,消息id={}, body={}", messageExt.getMsgId(), body);
                BingLogData bingLogData = JSON.parseObject(body, BingLogData.class);
                String eventType = bingLogData.getEventType();
                ActivityInvitee beforeActivity = BingLogUtil.buildBefore(bingLogData,ActivityInvitee.class);
                ActivityInvitee afterActivity = BingLogUtil.buildSource(bingLogData,ActivityInvitee.class);
                if(eventType.equals("INSERT")){
                    activityRelationMapper = SpringContextUtil.getBean("activityRelationMapper");
                    ActivityRelation relation = new ActivityRelation();
                    relation.setUserId(afterActivity.getUserId());
                    relation.setInviterUserId(afterActivity.getInviterUserId());
                    relation.setIsDelete(afterActivity.getIsDelete());
                    relation.setActivityId(afterActivity.getActivityId());
                    relation.setCreateTime(new Date());
                    relation.setUpdateTime(new Date());
                    relation.setInvalidTime(DateUtil.addDay(new Date(), 1));
                    activityRelationMapper.insert(relation);
                } else {
                    if(beforeActivity.getIsDelete() == 0 && afterActivity.getIsDelete() == 1){
                        activityRelationMapper = SpringContextUtil.getBean("activityRelationMapper");
                        ActivityRelation relation = activityRelationMapper.selectOne(new QueryWrapper<ActivityRelation>()
                            .eq("user_id",afterActivity.getUserId())
                            .eq("inviter_user_id",afterActivity.getInviterUserId())
                            .eq("activity_id",afterActivity.getActivityId())
                            .gt("invalid_time",new Date())
                            .eq("is_delete",0)
                        );
                        if(relation != null){
                            relation.setIsDelete(1);
                            activityRelationMapper.updateById(relation);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("[][][][]同步活动关系错误",e);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
