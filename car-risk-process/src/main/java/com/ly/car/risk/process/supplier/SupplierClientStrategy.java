package com.ly.car.risk.process.supplier;

import com.ly.car.risk.process.supplier.Tsan.SupplierT3Client;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class SupplierClientStrategy {
    
    @Resource
    SupplierT3Client             supplierT3Client;
    @Resource
    SupplierYueYueClientAbstract supplierYueYueClientAbstract;
    
    public SupplierClient get(String supplierCode) {
        if ("T3".equals(supplierCode) || "T3-preferential".equals(supplierCode)) {
            return supplierT3Client;
        } else if ("YueYue".equals(supplierCode) || "YueYueTHFixedPrice".equals(supplierCode)) {
            return supplierYueYueClientAbstract;
        }
        return null;
    }
}
