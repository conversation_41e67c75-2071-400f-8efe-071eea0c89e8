package com.ly.car.risk.process.turboMQ.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.car.order.entity.SfcCouponRecord;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcOrderExt;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.entity.OrderComplete;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.BingLogEventType;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.constants.SfcOrderStatus;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.repo.order.mapper.SfcCouponRecordMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderExtMapper;
import com.ly.car.risk.process.repo.risk.mapper.OrderCompleteMapper;
import com.ly.car.risk.process.service.chain.RuleRiskHandler;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.SfcOrderNumDTO;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.service.rule.sfcGroup.RuleRisk018Service;
import com.ly.car.risk.process.service.sfc.SfcSupplierOrderService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.dto.BingLogData;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.AmountUtil;
import com.ly.car.risk.process.utils.BingLogUtil;
import com.ly.car.risk.process.utils.DateUtilRisk;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BingLogSfcOrderInfoConsumer implements MessageListenerConcurrently {

    private SfcSupplierOrderService sfcSupplierOrderService;
    private SaveScoredSortedSetService saveScoredSortedSetService;
    private OrderAddressMapper orderAddressMapper;
    private OrderCompleteMapper orderCompleteMapper;
    private SfcCouponRecordMapper sfcCouponRecordMapper;
    private ExecutorService executorService;
    private RuleRiskHandler ruleRiskHandler;
    private RuleRisk018Service ruleRisk018Service;
    private SfcOrderExtMapper sfcOrderExtMapper;
    private MqRiskProducer mqRiskProducer;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for(MessageExt messageExt : list){
            try {
                String body = new String(messageExt.getBody(), "utf-8");
                log.info("[binlog][BingLogSfcOrderInfoConsumer][][][]收到turbo消息,消息id={}, body={}", messageExt.getMsgId(), body);
                BingLogData bingLogData = JSON.parseObject(body, BingLogData.class);
                //创单
                String eventType = bingLogData.getEventType();
                SfcOrder afterSfcOrder = BingLogUtil.buildSource(bingLogData, SfcOrder.class);
                SfcOrder beforeSfcOrderInfo = BingLogUtil.buildBefore(bingLogData, SfcOrder.class);
                dealEvent(eventType,beforeSfcOrderInfo,afterSfcOrder);
                if(BingLogEventType.UPDATE.getType().equals(eventType)){
                    //发送个mq,进行异步处理缓存预估路径
                    mqRiskProducer = SpringContextUtil.getBean("binlogProducer");
                    if(beforeSfcOrderInfo.getPassengerOnCar() != null && afterSfcOrder.getPassengerOnCar() == 1 && beforeSfcOrderInfo.getPassengerOnCar() == 0){
                        mqRiskProducer.send(MqTagEnum.car_risk_sfc_user_onCar,afterSfcOrder.getOrderId(), DateUtilRisk.addSeconds(new Date(),10).getTime());
                    }

                    if(beforeSfcOrderInfo.getStatus() != null &&
                            beforeSfcOrderInfo.getStatus() != 200 && afterSfcOrder.getStatus() == 200){
                        mqRiskProducer.send(MqTagEnum.car_risk_safe_warning,afterSfcOrder.getOrderId(), 0L);
                    }

                    //实时入库，当前同步是完单的
                    if(beforeSfcOrderInfo.getStatus() != null
                            && SfcOrderStatus.FINISH.code != beforeSfcOrderInfo.getStatus()
                            && SfcOrderStatus.FINISH.code == afterSfcOrder.getStatus()){
                        saveScoredSortedSetService = SpringContextUtil.getBean("saveScoredSortedSetService");
                        //这个时候洗到数据库,并且放到缓存
                        OrderRiskContext orderRiskContext = setOrderContext(afterSfcOrder);
                        if(orderRiskContext.getMemberId().equals("0")){
                            log.info("[binlog][BingLogSfcOrderInfoConsumer][][][]分销订单存入司机维度缓存{}", JsonUtils.json(orderRiskContext));
                            saveScoredSortedSetService.save(RedisKeyConstants.DRIVER_SLIDING_WINDOW+orderRiskContext.getDriverCardNo(),
                                    3 * 24 * 60 * 60L,
                                    orderRiskContext,
                                    DateUtil.string2Date(orderRiskContext.getFinishTime()).getTime());
                            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                        }
                        saveScoredSortedSetService.save(RedisKeyConstants.USER_SLIDING_WINDOW+orderRiskContext.getMemberId(),
                                3 * 24 * 60 * 60L,
                                orderRiskContext,
                                DateUtil.string2Date(orderRiskContext.getFinishTime()).getTime());
                        saveScoredSortedSetService.save(RedisKeyConstants.DRIVER_SLIDING_WINDOW+orderRiskContext.getDriverCardNo(),
                                3 * 24 * 60 * 60L,
                                orderRiskContext,
                                DateUtil.string2Date(orderRiskContext.getFinishTime()).getTime());

                        //完单的发送个mq监听
                        mqRiskProducer = SpringContextUtil.getBean("binlogProducer");
                        mqRiskProducer.send(MqTagEnum.car_risk_convert_sfc_data,afterSfcOrder.getOrderId(), DateUtilRisk.addSeconds(new Date(),10).getTime());

                    }
                    //取消的
                    try {
                        if(beforeSfcOrderInfo.getStatus() != null
                                && SfcOrderStatus.CANCEL.code != beforeSfcOrderInfo.getStatus()
                                && SfcOrderStatus.CANCEL.code == afterSfcOrder.getStatus()){

                            //当前有两个维度，一个是memberId,一个是乘车人手机号
                            mqRiskProducer = SpringContextUtil.getBean("binlogProducer");
                            //发一个5s的延迟mq
                            mqRiskProducer.send(MqTagEnum.car_risk_binlog_sfc_cancel,afterSfcOrder.getOrderId(), DateUtilRisk.addSeconds(new Date(),5).getTime());
                            //存入三天所下的订单 ，订单号，取消时间
                            saveScoredSortedSetService = SpringContextUtil.getBean("saveScoredSortedSetService");
                            //这边给当前时间是怕取消时间没有更新
                            SfcOrderNumDTO sfcOrderNumDTO = new SfcOrderNumDTO(afterSfcOrder.getOrderId(), new Date(), afterSfcOrder.getPassengerCellphone());
                            String key = "";
                            String userId = "";
                            if(StringUtils.isNotBlank(afterSfcOrder.getUnionId())){
                                key = RedisKeyConstants.SFC_USER_ORDER_NUMBER+afterSfcOrder.getUnionId();
                                userId = afterSfcOrder.getUnionId();
                            } else if(afterSfcOrder.getMemberId() != null && afterSfcOrder.getMemberId() != 0){
                                key = RedisKeyConstants.SFC_USER_ORDER_NUMBER+afterSfcOrder.getMemberId();
                                userId = String.valueOf(afterSfcOrder.getMemberId());
                            } else {
                                //说明是分销的单子，不需要看
                                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                            }
                            if(StringUtils.isNotBlank(key)){
                                saveScoredSortedSetService.save(key,
                                        3 * 24 * 60 * 60L,
                                        sfcOrderNumDTO,
                                        sfcOrderNumDTO.getCancelTime().getTime());
                            }

                            //完单的发送个mq监听
                            mqRiskProducer = SpringContextUtil.getBean("binlogProducer");
                            mqRiskProducer.send(MqTagEnum.car_risk_convert_sfc_data,afterSfcOrder.getOrderId(), DateUtilRisk.addSeconds(new Date(),10).getTime());
                        }
                    } catch (Exception e) {
                        log.error("[binlog][BingLogSfcOrderInfoConsumer][][][]存储取消信息异常,消息id={}", messageExt.getMsgId(), e);
                    }
                } else {
                    mqRiskProducer = SpringContextUtil.getBean("binlogProducer");
                    mqRiskProducer.send(MqTagEnum.car_risk_place_order_phone,afterSfcOrder.getOrderId(), DateUtilRisk.addSeconds(new Date(),5).getTime());
                }
            }catch (Exception e){
                log.error("[binlog][BingLogSfcOrderInfoConsumer][][][]收到turbo消息,处理异常,消息id={}", messageExt.getMsgId(), e);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    public OrderRiskContext setOrderContext(SfcOrder sfcOrder){
        log.info("[][][][]组装顺风车参数:{}", JsonUtils.json(sfcOrder));

        orderAddressMapper = SpringContextUtil.getBean("orderAddressMapper");
        OrderAddress orderAddress = orderAddressMapper.findByOrderId(sfcOrder.getOrderId());
        //查询顺风车优惠券信息
        sfcCouponRecordMapper = SpringContextUtil.getBean("sfcCouponRecordMapper");
        List<SfcCouponRecord> sfcCouponRecord = sfcCouponRecordMapper.getListByOrderId(sfcOrder.getOrderId());
        BigDecimal amount = BigDecimal.ZERO;
        try {
            amount = sfcCouponRecord.stream()
                    .map(SfcCouponRecord::getReduceCost).reduce(BigDecimal.ZERO,BigDecimal::add);
        }catch (Exception e) {
            log.error("计算优惠券金额错误");
        }
        sfcSupplierOrderService = SpringContextUtil.getBean("sfcSupplierOrderService");
        SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getByOrderId(sfcOrder.getOrderId(),sfcOrder.getSupplierOrderId());
        log.info("[][][][]查询顺风车供应商单据:{}", JsonUtils.json(sfcSupplierOrder));

        OrderRiskContext orderRiskContext = new OrderRiskContext();
        orderRiskContext.setOrderId(sfcOrder.getOrderId());
        orderRiskContext.setFinishTime(DateUtil.date2String(sfcOrder.getFinishTime()));
        orderRiskContext.setMemberId(String.valueOf(sfcOrder.getMemberId()));
        orderRiskContext.setUnionId(sfcOrder.getUnionId());
        orderRiskContext.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
        orderRiskContext.setStartLat(orderAddress.getStartLat());
        orderRiskContext.setStartLng(orderAddress.getStartLng());
        orderRiskContext.setEndLat(orderAddress.getEndLat());
        orderRiskContext.setEndLng(orderAddress.getEndLng());
        orderRiskContext.setTotalAmount(AmountUtil.changeF2Y(sfcOrder.getTotalAmount()));
        orderRiskContext.setEstimateKilo(orderAddress.getEstimateKilo());
        orderRiskContext.setActualKilo(orderAddress.getActualKilo());
        orderRiskContext.setEstimateDuration(orderAddress.getEstimateMinute());
        orderRiskContext.setActualDuration(orderAddress.getActualMinute());
        orderRiskContext.setIntervalTime((int)TimeUnit.MILLISECONDS.toMinutes(sfcOrder.getFinishTime().getTime()-sfcSupplierOrder.getAcceptTime().getTime()));
        orderRiskContext.setPassengerCellphone(sfcOrder.getPassengerCellphone());
        orderRiskContext.setCouponAmount(amount);
        //存入orderComplete
        OrderComplete complete = new OrderComplete();
        complete.setOrderId(sfcOrder.getOrderId());
        complete.setSupplierOrderId(sfcSupplierOrder.getSupplierOrderId());
        complete.setStartCityId(orderAddress.getStartCityId());
        complete.setStartCityName(orderAddress.getStartCityName());
        complete.setCreateOrderTime(sfcOrder.getCreated());
        complete.setPayTime(sfcOrder.getPayTime());
        complete.setFinishTime(sfcOrder.getFinishTime());
        complete.setMemberId(String.valueOf(sfcOrder.getMemberId()));
        complete.setUnionId(sfcOrder.getUnionId());
        complete.setTotalAmount(AmountUtil.changeF2Y(sfcOrder.getTotalAmount()));
        complete.setPhone(sfcOrder.getPassengerCellphone());
        complete.setStartAddress(orderAddress.getStartAddress());
        complete.setEndAddress(orderAddress.getEndAddress());
        List<String> couponList = sfcCouponRecord.stream().map(SfcCouponRecord::getCardId).collect(Collectors.toList());
        complete.setCouponBatchNo(StringUtils.join(couponList,","));
        complete.setCouponAmount(sfcCouponRecord.stream().map(SfcCouponRecord::getReduceCost).reduce(BigDecimal.ZERO,BigDecimal::add).toString());
        complete.setCreateTime(new Date());
        complete.setUpdateTime(new Date());
        complete.setEstimateKilo(orderAddress.getEstimateKilo());
        complete.setEstimateTime(orderAddress.getEstimateMinute());
        complete.setActualKilo(orderAddress.getActualKilo());
        complete.setActualTime(orderAddress.getActualMinute());
        complete.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
        complete.setSupplierCode(sfcOrder.getSupplierCode());
        complete.setStartLat(orderAddress.getStartLat());
        complete.setStartLng(orderAddress.getStartLng());
        complete.setEndLat(orderAddress.getEndLat());
        complete.setEndLng(orderAddress.getEndLng());
        orderCompleteMapper = SpringContextUtil.getBean("orderCompleteMapper");
//        orderCompleteMapper.insert(complete);
        return orderRiskContext;
    }

    public static void main(String[] args) {
        String startDate = "2022-11-23 12:00:00";
        String endDate = "2022-11-23 12:59:59";
        Date ss1 = DateUtil.string2Date(startDate);
        Date ss2 = DateUtil.string2Date(endDate);
        System.out.println((int)TimeUnit.MILLISECONDS.toMinutes(ss2.getTime()-ss1.getTime()));
    }


    //下单、接单、取消、完单 事件整理
    //    CREATE_SUCCESS(0, "已创建", "待支付", "订单支付时间剩余",1,"待支付"),
    //    @Deprecated
    //    PAY_SUCCESS(5, "已支付", "派车中", "",21,"派车中"),
    //    DISPATCH_SUCCESS(10, "已派单", "等待车主接单", "正在派车，系统将于30分钟内返回结果",21,"派车中"),
    //    REDISPATCH_SUCCESS(11, "已改派单", "等待车主接单", "正在派车，系统将于30分钟内返回结果",21,"派车中"),
    //    DECISION_SUCCESS(20, "司机已接单", "待出行", "司机已接单，您可联系司机商量行程问题",23,"待接驾"),
    //    SETOUT(50, "司机已出发", "待接驾", "司机已出发，请根据司机位置提前到达路边等候",23,"待接驾"),
    //    ARRIVED(100, "司机已到达", "待上车", "司机已到达指定地点，请尽快上车",24,"待上车"),
    //    SERVICE(200, "服务开始", "行程中", "行程已开始，请系好安全带",22,"行程中"),
    //    DSEST(250, "司机已经到达目的地", "已完成", "本次行程已结束，期待您的再次使用",5,"已完成"),
    //    FINISH(300, "服务结束", "已完成", "本次行程已结束，期待您的再次使用",5,"已完成"),
    //    CANCEL(1000, "取消", "已取消", "订单已取消，期待您的再次使用",2,"已取消");
    public void dealEvent(String dataType,SfcOrder beforeOrder,SfcOrder afterOrder){
        try {
            mqRiskProducer = SpringContextUtil.getBean("binlogProducer");
            JSONObject jsonObject = new JSONObject();
            if(BingLogEventType.UPDATE.getType().equals(dataType)){
                if(beforeOrder.getStatus() != null &&
                        beforeOrder.getStatus() != 20 && afterOrder.getStatus() == 20){
                    jsonObject.put("orderId",afterOrder.getOrderId());
                    jsonObject.put("eventTime",new Date());
                    jsonObject.put("eventType","2");
                    mqRiskProducer.send(MqTagEnum.car_risk_order_event_topic,
                            JsonUtils.json(jsonObject),
                            DateUtilRisk.addSeconds(new Date(),5).getTime());
                } else if(beforeOrder.getStatus() != null &&
                        beforeOrder.getStatus() != 1000 && afterOrder.getStatus() == 1000){
                    jsonObject.put("orderId",afterOrder.getOrderId());
                    jsonObject.put("eventTime",afterOrder.getCancelTime());
                    jsonObject.put("eventType","3");
                    mqRiskProducer.send(MqTagEnum.car_risk_order_event_topic,
                            JsonUtils.json(jsonObject),
                            DateUtilRisk.addSeconds(new Date(),5).getTime());
                } else if(beforeOrder.getStatus() != null &&
                        beforeOrder.getStatus() != 300 && afterOrder.getStatus() == 300){
                    jsonObject.put("orderId",afterOrder.getOrderId());
                    jsonObject.put("eventTime",afterOrder.getFinishTime());
                    jsonObject.put("eventType","4");
                    mqRiskProducer.send(MqTagEnum.car_risk_order_event_topic,
                            JsonUtils.json(jsonObject),
                            DateUtilRisk.addSeconds(new Date(),5).getTime());
                } else {
                    //以后再看有没有其他事件
                }
            } else {
                jsonObject.put("orderId",afterOrder.getOrderId());
                jsonObject.put("eventTime",afterOrder.getCreated());
                jsonObject.put("eventType","1");
                mqRiskProducer.send(MqTagEnum.car_risk_order_event_topic,
                        JsonUtils.json(jsonObject),
                        DateUtilRisk.addSeconds(new Date(),5).getTime());
            }
        } catch (Exception e){
            log.error("[][][][]当前顺风车事件整合报错",e);
        }


    }

}
