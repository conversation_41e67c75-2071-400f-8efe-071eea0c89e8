package com.ly.car.risk.process.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.*;
import com.ly.car.risk.process.api.rsp.MemberQueryResponse;
import com.ly.car.risk.process.repo.order.mapper.*;
import com.ly.car.risk.process.repo.risk.mapper.RiskHitLinkMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskHitLink;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.order.*;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderDriver;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskHitLinkService {

    @Resource
    private RiskHitLinkMapper riskHitLinkMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private DistributionInfoMapper distributionInfoMapper;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private OrderWxCardMapper orderWxCardMapper;
    @Resource
    private OrderDriverMapper orderDriverMapper;
    @Resource
    private OrderCouponMapper orderCouponMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private SfcSupplierOrderMapper sfcSupplierOrderMapper;
    @Resource
    private SfcCouponRecordMapper sfcCouponRecordMapper;
    @Resource(name = "executorService")
    private ExecutorService executorService;
    @Resource
    private MemberService memberService;
    @Resource
    private CarOrderService carOrderService;

    public void saveHitLink(RiskHitLinkDTO dto){
        executorService.execute(()->{
            if(dto == null || CollectionUtils.isEmpty(dto.getLinkOrderIds())){
                return;
            }
            for(String orderId : dto.getLinkOrderIds()){
                if(OrderUtils.isNewOrder(orderId)){
                    dealNewOrderRiskHit(dto,orderId);
                    continue;
                }
                RiskHitLink riskHitLink = new RiskHitLink();
                riskHitLink.setParentOrderId(dto.getOrderId());
                riskHitLink.setOrderId(orderId);
                riskHitLink.setRuleNo(dto.getRuleNo());
                riskHitLink.setIsCheating(0);
                riskHitLink.setUpdateTime(new Date());
                riskHitLink.setRequestId(dto.getRequestId());
                riskHitLink.setHitTime(new Date());
                if(orderId.startsWith("YNC") || orderId.startsWith("GNC")){
                    OrderInfo orderInfo = orderInfoMapper.findByOrderId(orderId);
                    riskHitLink.setSupplierCode(orderInfo.getSupplierCode());
                    riskHitLink.setRefId(String.valueOf(orderInfo.getRefId()));
                    DistributionInfo distributionInfo = distributionInfoMapper.selectOne(
                            new QueryWrapper<DistributionInfo>().eq("status",1).eq("refid",orderInfo.getRefId())
                    );
                    if(distributionInfo != null){
                        riskHitLink.setRefName(distributionInfo.getDistributionName());
                    }
                    riskHitLink.setMemberId(orderInfo.getMemberId());
                    riskHitLink.setPassengerCellphone(orderInfo.getPassengerCellphone());
                    riskHitLink.setCreateTime(orderInfo.getCreateTime());
                    riskHitLink.setAcceptTime(orderInfo.getDecisionTime());
                    riskHitLink.setFinishTime(orderInfo.getFinishTime());
                    riskHitLink.setTotalAmount(orderInfo.getTotalAmount());
                    OrderAddress orderAddress = orderAddressMapper.findByOrderId(orderId);
                    if(orderAddress != null){
                        riskHitLink.setStartAddress(orderAddress.getStartAddress());
                        riskHitLink.setEndAddress(orderAddress.getEndAddress());
                        riskHitLink.setActualDistance(orderAddress.getActualKilo());
                        riskHitLink.setActualMinute(orderAddress.getActualMinute());
                        riskHitLink.setCityId(orderAddress.getStartCityId());
                        riskHitLink.setCityName(orderAddress.getStartCityName());
                    }
                    OrderDriver orderDriver = orderDriverMapper.findByOrderId(orderId);
                    if(orderDriver != null){
                        riskHitLink.setDriverCardNo(orderDriver.getPlateNumber());
                    }
                    //先查微信优惠券
                    List<OrderWxCard> orderWxCards = orderWxCardMapper.selectList(
                            new QueryWrapper<OrderWxCard>().eq("order_id",dto.getOrderId())
                    );
                    if(!CollectionUtils.isEmpty(orderWxCards)){
                        List<String> couponNameList = orderWxCards.stream().map(OrderWxCard::getTitle).collect(Collectors.toList());
                        riskHitLink.setCouponNo(StringUtils.join(couponNameList,","));
                        riskHitLink.setCouponAmount(orderWxCards.stream().map(OrderWxCard::getReduceCost).map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add));
                    } else {
                        //查app优惠券
                        List<OrderCoupon> orderCoupons = orderCouponMapper.selectList(
                                new QueryWrapper<OrderCoupon>().eq("order_id",dto.getOrderId())
                        );
                        List<String> couponNameList = orderCoupons.stream().map(OrderCoupon::getBatchName).collect(Collectors.toList());
                        riskHitLink.setCouponNo(StringUtils.join(couponNameList,","));
                        riskHitLink.setCouponAmount(orderCoupons.stream().map(OrderCoupon::getCouponAmount).map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add));
                    }
                } else {
                    SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(orderId);
                    if(sfcOrder != null){
                        riskHitLink.setSupplierCode(sfcOrder.getSupplierCode());
                        riskHitLink.setRefId(String.valueOf(sfcOrder.getRefId()));
                        riskHitLink.setFinishTime(sfcOrder.getFinishTime());
                        riskHitLink.setTotalAmount(new BigDecimal(sfcOrder.getTotalAmount()/100));
                        riskHitLink.setMemberId(String.valueOf(sfcOrder.getMemberId()));
                    }
                    DistributionInfo distributionInfo = distributionInfoMapper.selectOne(
                            new QueryWrapper<DistributionInfo>().eq("status",1).eq("refid",sfcOrder.getRefId())
                    );
                    if(distributionInfo != null){
                        riskHitLink.setRefName(distributionInfo.getDistributionName());
                    }
                    riskHitLink.setPassengerCellphone(sfcOrder.getPassengerCellphone());
                    riskHitLink.setCreateTime(sfcOrder.getCreated());
                    SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderMapper.selectOne(new QueryWrapper<SfcSupplierOrder>()
                            .eq("order_id",sfcOrder.getOrderId())
                            .eq(sfcOrder.getSupplierOrderId()!=null,"supplier_order_id",sfcOrder.getSupplierOrderId())
                            .last("limit 1")
                    );
                    if(sfcSupplierOrder != null){
                        riskHitLink.setAcceptTime(sfcSupplierOrder.getAcceptTime());
                        riskHitLink.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
                    }
                    OrderAddress orderAddress = orderAddressMapper.findByOrderId(orderId);
                    if(orderAddress != null){
                        riskHitLink.setStartAddress(orderAddress.getStartAddress());
                        riskHitLink.setEndAddress(orderAddress.getEndAddress());
                        riskHitLink.setActualDistance(orderAddress.getActualKilo());
                        riskHitLink.setActualMinute(orderAddress.getActualMinute());
                        riskHitLink.setCityId(orderAddress.getStartCityId());
                        riskHitLink.setCityName(orderAddress.getStartCityName());
                    }
                    List<SfcCouponRecord> sfcCouponRecord = sfcCouponRecordMapper.getListByOrderId(sfcOrder.getOrderId());
                    if(!CollectionUtils.isEmpty(sfcCouponRecord)){
                        List<String> couponNameList = sfcCouponRecord.stream().map(SfcCouponRecord::getTitle).collect(Collectors.toList());
                        riskHitLink.setCouponNo(StringUtils.join(couponNameList,","));
                        riskHitLink.setCouponAmount(sfcCouponRecord.stream().map(SfcCouponRecord::getReduceCost).reduce(BigDecimal.ZERO,BigDecimal::add));
                    }
                }
                if(StringUtils.isNotBlank(riskHitLink.getMemberId())  && !riskHitLink.getMemberId().equals("0")){
                    MemberQueryResponse infoByMemberId = memberService.getInfoByMemberId(String.valueOf(riskHitLink.getMemberId()));
                    if(infoByMemberId != null && infoByMemberId.getData() != null){
                        riskHitLink.setUserPhone(infoByMemberId.getData().getMobile());
                    }
                }
                riskHitLinkMapper.insert(riskHitLink);
            }
        });
    }

    private void dealNewOrderRiskHit(RiskHitLinkDTO dto, String orderId) {

        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
        if(null == orderDetail){
            return;
        }
        CarInfo carInfo = orderDetail.getCarInfo();
        BaseOrderInfo baseInfo = orderDetail.getBaseInfo();
        OrderTripInfo orderTrip = orderDetail.getOrderTrip();
        CarPassenger passengerInfo = orderDetail.getPassengerInfo();

        RiskHitLink riskHitLink = new RiskHitLink();
        riskHitLink.setParentOrderId(dto.getOrderId());
        riskHitLink.setOrderId(orderId);
        riskHitLink.setRuleNo(dto.getRuleNo());
        riskHitLink.setIsCheating(0);
        riskHitLink.setUpdateTime(new Date());
        riskHitLink.setRequestId(dto.getRequestId());
        riskHitLink.setHitTime(new Date());
        riskHitLink.setSupplierCode(carInfo.getSupplierCode());
        riskHitLink.setRefId(baseInfo.getRefId());
        riskHitLink.setMemberId(orderDetail.getMemberId());
        riskHitLink.setPassengerCellphone(passengerInfo.getPassengerCellPhone());
        riskHitLink.setCreateTime(baseInfo.getGmtCreate());
        riskHitLink.setAcceptTime(baseInfo.getGmtAccept());
        riskHitLink.setFinishTime(baseInfo.getGmtTripFinished());
        riskHitLink.setTotalAmount(new BigDecimal(baseInfo.getAmount()));
        riskHitLink.setStartAddress(orderTrip.getDepartureAddress());
        riskHitLink.setEndAddress(orderTrip.getArrivalAddress());
        riskHitLink.setActualDistance(orderTrip.getOldRealKilo());
        riskHitLink.setActualMinute(orderTrip.getOldRealMinute());
        riskHitLink.setCityId(orderTrip.getDepartureCityCode());
        riskHitLink.setCityName(orderTrip.getDepartureCityName());
        riskHitLink.setDriverCardNo(carInfo.getCarNum());
        String couponNameList = orderDetail.getUsedCoupons().stream().map(p -> p.getProductName()).collect(Collectors.joining(","));
        riskHitLink.setCouponNo(couponNameList);
        riskHitLink.setCouponAmount(orderDetail.getUsedCoupons().stream().filter(p->StringUtils.isNotBlank(p.getRealPrice()))
                .map(p->new BigDecimal(p.getRealPrice())).reduce(BigDecimal.ZERO,BigDecimal::add));
        riskHitLink.setUserPhone(baseInfo.getContactPhone());

        riskHitLinkMapper.insert(riskHitLink);
    }
}
