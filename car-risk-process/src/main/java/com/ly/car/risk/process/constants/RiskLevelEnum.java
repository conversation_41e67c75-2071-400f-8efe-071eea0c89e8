package com.ly.car.risk.process.constants;

import org.apache.commons.lang3.StringUtils;


public enum RiskLevelEnum {
    /**
     * 0-无风险 1-低 2-中低 3-中 4-中高 5-高
     */
    NO(0, "无风险"),
    <PERSON><PERSON>(1, "低"),
    MEDIUM_LOW(2, "中低"),
    MEDIUM(3, "中"),
    MEDIUM_HIGH(4, "中高"),
    HIGH(5, "高");
    private Integer code;
    private String msg;

    RiskLevelEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (RiskLevelEnum enumItem : RiskLevelEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
}
