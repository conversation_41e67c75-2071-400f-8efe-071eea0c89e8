package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.*;
import com.ly.car.risk.process.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 当前订单关联设备ID（unionid）近1小时内完单数量大于等于4单，且接单司机一致
 * */
@Component
@Slf4j
@Scope("prototype")
public class RuleRisk013Service extends FilterSendOrderHandler{

    private static final String ruleNo = "013";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        if(!context.getSpecialCarRuleConfig().getRule013onOff()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                return;
            }
        } else {

            //获取当前memberId关联订单
            List<SendOrderContext> orderContextListByMember = context.getMemberList().stream()
                    .filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.fortyHour())).collect(Collectors.toList());

            //当前设备id
            List<SendOrderContext> orderContextListByDevice = context.getDeviceList().stream()
                    .filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.fortyHour())).collect(Collectors.toList());

            List<String> linkOrderIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(orderContextListByMember)) {
                List<String> driverCardNos = orderContextListByMember.stream().map(SendOrderContext::getDriverCardNo).distinct().collect(Collectors.toList());
                if (orderContextListByMember.size() >= context.getSpecialCarRuleConfig().getRule013OrderNum() && driverCardNos.size() == 1) {
                    context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.MEDIUM.getCode()));
                    List<String> orderIds = orderContextListByMember.stream().map(SendOrderContext::getOrderId).distinct().collect(Collectors.toList());
                    distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene(), context.getChildScene(),
                            0, null, RiskLevelEnum.HIGH.getCode());

                    if(StringUtils.isBlank(context.getRuleNo())){
                        context.setRuleNo(ruleNo);
                    } else {
                        context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                    }
                    riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                }
            }

            if (this.nextHandler != null) {
                this.nextHandler.doHandler(context);
            }
        }
    }
}
