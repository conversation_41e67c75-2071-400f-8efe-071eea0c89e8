package com.ly.car.risk.process.controller.request;

import lombok.Data;

@Data
public class CarMtBlackInfo {
    
    private String  carNum;
    //拉黑时间
    private String  shieldTime;
    //失效时间
    private String  invalidTime;
    //拉黑时长
    private Integer ttl;
    // 拉黑原因
    private String  riskRemark;
    // 风险类型
    private Integer riskType;
    // 操作类型
    private Integer optionType;
    // 状态 1有效 2无效
    private Integer status;
    // 名单类型 1风控名单 2萌艇司机名单
    private Integer source;
}