package com.ly.car.risk.process.constants;

import org.apache.commons.lang3.StringUtils;

public enum DriverBadReportEnum {

    BAD_PLAY_PHONE(1,"频繁操作手机"),
    BAD_TIRED(2,"疲劳驾驶"),
    BAD_SPEED_QUICK(3,"车速过快"),
    BAD_SPEED_CHANGE(4,"变速过急"),
    BAD_LINE_CHANGE(5,"频繁变道"),
    BAD_CAR_CLOSE(6,"车距过近"),

    ;

    private Integer code;
    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    DriverBadReportEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (DriverBadReportEnum enumItem : DriverBadReportEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
}
