package com.ly.car.risk.process.service.rule.hcGroup;

import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.SceneToCustomerType;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 这个是需要http返回的，同一种模式的就统一返回，需要异步的则不同的数据开发
 * 所有名单类的都是分为白名单和其他
 * */
@Service
@Slf4j
public class HcDriverCommonBlackService extends FilterHcAroundHandler{

    private final String childSceneNo = "7-4";

    @Override
    public void doHandler(FilterHcContext context) {
        //先看下当前场景下判断几种名单类型
        List<Integer> customerTypeList = SceneToCustomerType.hcCustomerTypeMap.get(context.getMainScene()+"-"+context.getChildScene());

        RiskCustomerManage manage = context.getCustomerManageList().stream()
                .filter(data->data.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode())).findFirst().orElse(null);
        if(manage != null){
            return;
        }
        List<RiskCustomerManage> manageList = new ArrayList<>();
        if(customerTypeList == null || customerTypeList.size() == 0){
            manageList = context.getCustomerManageList().stream()
                    .filter(data->!data.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode())).collect(Collectors.toList());
        } else {
            manageList = context.getCustomerManageList().stream()
                    .filter(data->customerTypeList.contains(data.getRiskType()))
                    .filter(data->!data.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode())).collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(manageList)){
            RiskResultNewDTO dto = new RiskResultNewDTO(1,"命中黑名单","");
            if(CollectionUtils.isNotEmpty(context.getOrderIds())){
                dto.setObj(context.getOrderIds());
            }
            context.getUiResult().setData(dto);
            return;
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }
    }

    @Override
    public void doAfter(RiskResultNewDTO resultDTO) {

    }
}
