package com.ly.car.risk.process.service.rule.mtGroup;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.mtticket.entity.RideOrderInfo;
import com.ly.car.risk.process.repo.mtticket.mapper.RideOrderInfoMapper;
import com.ly.car.risk.process.repo.risk.mapper.HcCustomerMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcCustomer;
import com.ly.car.risk.process.service.dto.MtDriverConfig;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class MtDriverReceiveService extends MtFilterHandler{

    @Resource
    private RideOrderInfoMapper rideOrderInfoMapper;

    @Override
    public void doHandler(MtFilterContext context) {
        String driverId = context.getParam().getString("driverId");
        String mobile = context.getParam().getString("mobile");
        String plate = context.getParam().getString("plate");
        if(context.getParam().getString("productLine").equals("MT")){
            List<RideOrderInfo> orderInfos = this.rideOrderInfoMapper.selectList(new QueryWrapper<RideOrderInfo>()
                    .eq("driver_mobile",mobile)
                    .eq("order_status",4)  //4-已派车
                    .between("create_time", TimeUtil.currentDay(),new Date())
            );
            List<RideOrderInfo> oneDayOrderInfos = this.rideOrderInfoMapper.selectList(new QueryWrapper<RideOrderInfo>()
                    .eq("driver_mobile",mobile)
                    .eq("order_status",6)   //6-已完成
                    .between("create_time", TimeUtil.oneDay(),new Date())
            );
            MtDriverConfig config = context.getParam().getObject("config",MtDriverConfig.class);
            List<String> driverList = driverList();
            if(orderInfos != null && orderInfos.size() >= config.getReceiveOrderNum() && CollectionUtils.isEmpty(oneDayOrderInfos) && !driverList.contains(plate)){
//            context.getDto().setCode(1);
//            context.getDto().setMessage("当前司机同时存在多笔已接单订单且无完单");
                log.info("[][][][]当前命中恶意接单策略的司机:{},{}",plate,mobile);
                return;
            }
        } else {

        }

        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }

    }

    public List<String> driverList(){
        List<String> driverList = new ArrayList<>();
        try {
            String configJson = ConfigCenterClient.get("virtual_driver_list");
            log.info("获取车头列表:{}",configJson);
            if(StringUtils.isNotBlank(configJson)){
                driverList = Arrays.asList(configJson.split(","));
            }
        } catch (Exception e) {
        }
        return driverList;
    }
}
