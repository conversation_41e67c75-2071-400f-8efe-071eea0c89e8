package com.ly.car.risk.process.service;

import com.ly.car.exception.SilentException;
import com.ly.car.log.PrintLog;
import com.ly.car.monitor.HealthCheckService;
import com.ly.car.risk.process.bean.properties.KafKaProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class TodoService implements ApplicationListener<ApplicationStartedEvent> {
    @Resource
    private HealthCheckService healthCheckService;
    @Resource
    private KafKaProperties kafKaProperties;
    @Resource
    private KafkaConsumer<String, String> szKafkaConsumer;
    @Resource
    private KafkaProducer<String, String> szKafkaProducer;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        if (kafKaProperties.isTodoConsumer()) {
            Executors.newSingleThreadExecutor().execute(() -> {
                log.info("Todo消费开始...");
                szKafkaConsumer.subscribe(Arrays.asList(kafKaProperties.getTodoTopic()));
                ConsumerRecords<String, String> records;
                while (healthCheckService.isHealth()) {
                    records = szKafkaConsumer.poll(1000);
                    if (records.count() > 0) {
                        log.info("Todo拉取消息{}条", records.count());
                        for (ConsumerRecord<String, String> record : records) {
                            consumerTodo(record.value());
                        }
                    }
                }
                log.info("Todo消费结束...");
                szKafkaConsumer.close();
            });
        }
    }

    @PrintLog
    @SilentException
    public void consumerTodo(String value) {
        // todo something
    }

    public void send(String value) {
        ProducerRecord<String, String> record = new ProducerRecord<>(kafKaProperties.getTodoTopic(), value);
        szKafkaProducer.send(record);
    }

}
