package com.ly.car.risk.process.supplier.yueyue;

import java.util.Optional;
import java.util.stream.Stream;

public enum YueYueEnv {

    QA("qa", "https://apitest.yueyuechuxing.cn", "1001", "1", "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALywp19QfR8o40rxd5UUlOM2WmW3ALAKh4ajWzM9PAAQxy4MhMy/pMbnS/Ewp+yeqMk1jEDiXVcAHFOGg3VaE8yZp8VXJfXIt98S+eP7Iaczv/CEjQzhDMApbd250UrVCiF30D2UgrS0LJzdJ6ESIrJx85cjYiuEFPcZUjblu3ulAgMBAAECgYEAuO7Km/hfKXMBkioncc3CPw6+eoJqAh/PxH3GnOmfLNFE5jUO0z3hWbDIFZQCgT7fXpVE6TLzGJnbKWHk5ddrHqQ9S+Fhe247GdZP5bUy/mY52k87C3pMku6WKrb7ROOgpoq6C4j8TwIGgGJg+D17przoYvFmUaKyLQ2UOKI3ieECQQDtfRoflNNUaSrVnbByKs7ieswhO4ZTjMSmH2JPxs607Iy42xAJTpmFzzTzPLZwASYmH7LcabtXjtfP07Aspdw3AkEAy2XQXiAuSNrV97/jSyM2uOziSYjVkqo9iG+CWDGXdG2Bso1mqycAb803CAsDgmP6dGNfR7sMb3dMBZtz2ofRAwJAT3ulOJiougGQsnnaSfuFzkrm8uBnLqNYVXj/gE+7ctYQqvW2QkcqHpygzh+tt4LK78uAI0DH0KAgHpBs0CSatQJAY9NridbXgJBO9ZJrFsSIpgg1I4wR7jK5ko3P9kMYp17bieuWWSgCMd0RRlRJe6RgFuFysPzfpJFzcUkTekywSwJBAKVbMPGRNY9BNMTCCYUTAdi9iSzlZQgs1bzv61ZmB7cP4u0iNJ/yrY5lswSleNsS6zt/EX67kAhvVdto4dWglQc="),
    STAGE("stage", "https://apitest.yueyuechuxing.cn", "1001", "1", "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALywp19QfR8o40rxd5UUlOM2WmW3ALAKh4ajWzM9PAAQxy4MhMy/pMbnS/Ewp+yeqMk1jEDiXVcAHFOGg3VaE8yZp8VXJfXIt98S+eP7Iaczv/CEjQzhDMApbd250UrVCiF30D2UgrS0LJzdJ6ESIrJx85cjYiuEFPcZUjblu3ulAgMBAAECgYEAuO7Km/hfKXMBkioncc3CPw6+eoJqAh/PxH3GnOmfLNFE5jUO0z3hWbDIFZQCgT7fXpVE6TLzGJnbKWHk5ddrHqQ9S+Fhe247GdZP5bUy/mY52k87C3pMku6WKrb7ROOgpoq6C4j8TwIGgGJg+D17przoYvFmUaKyLQ2UOKI3ieECQQDtfRoflNNUaSrVnbByKs7ieswhO4ZTjMSmH2JPxs607Iy42xAJTpmFzzTzPLZwASYmH7LcabtXjtfP07Aspdw3AkEAy2XQXiAuSNrV97/jSyM2uOziSYjVkqo9iG+CWDGXdG2Bso1mqycAb803CAsDgmP6dGNfR7sMb3dMBZtz2ofRAwJAT3ulOJiougGQsnnaSfuFzkrm8uBnLqNYVXj/gE+7ctYQqvW2QkcqHpygzh+tt4LK78uAI0DH0KAgHpBs0CSatQJAY9NridbXgJBO9ZJrFsSIpgg1I4wR7jK5ko3P9kMYp17bieuWWSgCMd0RRlRJe6RgFuFysPzfpJFzcUkTekywSwJBAKVbMPGRNY9BNMTCCYUTAdi9iSzlZQgs1bzv61ZmB7cP4u0iNJ/yrY5lswSleNsS6zt/EX67kAhvVdto4dWglQc="),
    PRODUCT("product", "https://api2.yueyuechuxing.cn", "1001", "1", "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIRue0sVKzQ8/asp9CAUG5xXiRxa/MEjeeH8zAfZB4eQovIWleACVgD/DX4ofcYKGC+03phNA2cSchOxswaa9zSfHe8li7epBSgAtyeFutQGZM5dZYDHs768FsLCr+FO3FGrLjnwbj4jWkJz6e7I570UZ7TtubyE3APCB8rWrrF3AgMBAAECgYA1nyZ8+E1OEYQE0i/u7TVg0HDL8mt1KJApWNqkMtRBp+bSbyKwWUHJlXw5INwMW/n/FxUqRcSi720RXFpIymZdaXM309Vg0l9ZsJd65avn1tDYiKh5iMPoWIffKiS5wTWq6iMZW+m1if0fFV/7z6cQDfUCCFfKowiCT+hUWp6xwQJBANVvJF9rRjSAvXrpNw5APw2RkZ3Q19EP8AJa/uTeyixmxnw0nWsErk+QtJoBPmRIUmRpaOEO0RTXcH+MGKkU/k0CQQCe18LpMUzgBlhPJqkauOODWnWw+/r961dJvu6uUI0VMKk1KyBGGL4FduLM3/TahfNmFcJjoT5geYaZNLpoYXjTAkAlEVZp1AGvYKSH0jUjnnjU7HEzzW+N+YyffBpEuYS3TWovbBvi2D9FxkX3cTvawsy8b6otXBtn2TrKjBREWUcJAkEAjd38ylNq6qcgFklcPW6mXPndJZ5YVJLt2gK6oFt3NlbIuaDz6UdN6Al/lSXtRYLfmeyTOnKYCIhhq9IJLxLnWwJAXtSjRi1rAa6Hz4smBLrz7EqcBhfosTFh+PFijvK27f9KVElWtez2Pj0NL1m+EI0frpPfr0n4Ov4eob37dnO4yQ==");

    public String env;
    public String host;
    public String appId;
    public String tenantId;
    public String privateKey;


    YueYueEnv(String env, String host, String appId, String tenantId, String privateKey) {
        this.env = env;
        this.host = host;
        this.appId = appId;
        this.tenantId = tenantId;
        this.privateKey = privateKey;
    }

    public static YueYueEnv get(String env) {
        return Optional.ofNullable(Stream.of(QA, STAGE, PRODUCT).filter(yueYueEnv -> yueYueEnv.env.equals(env)).findFirst().get())
                .orElse(null);
    }
}