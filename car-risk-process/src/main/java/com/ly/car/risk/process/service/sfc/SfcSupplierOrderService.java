package com.ly.car.risk.process.service.sfc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.process.repo.order.mapper.SfcSupplierOrderMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SfcSupplierOrderService {

    @Resource
    private SfcSupplierOrderMapper sfcSupplierOrderMapper;

    public SfcSupplierOrder getByOrderId(String orderId,String supplierOrderId){
        return sfcSupplierOrderMapper.selectOne(
                new QueryWrapper<SfcSupplierOrder>()
                        .eq("order_id",orderId)
                        .eq("supplier_order_id",supplierOrderId)
                        .last("limit 1")
        );

    }

    public List<SfcSupplierOrder> getListByOrderId(String orderId){
        return sfcSupplierOrderMapper.selectList(
                new QueryWrapper<SfcSupplierOrder>()
                        .eq("order_id",orderId));
    }
}
