package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.controller.params.CommonParams;
import com.ly.car.risk.process.controller.params.DriverSyncParams;
import com.ly.car.risk.process.controller.params.HcSyncCustomerParam;
import com.ly.car.risk.process.repo.risk.mapper.SensitiveWordsMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveWords;
import com.ly.car.risk.process.repo.risk.mapper.entity.TcSupplierWorkOrder;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.SupplierWorkOrderService;
import com.ly.car.risk.process.service.workOrder.SupplierYueYueClient;
import com.ly.car.sharding.order.entity.OrderDriver;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;

@RestController
@RequestMapping("sync")
public class DataSyncController {

    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private SensitiveWordsMapper sensitiveWordsMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderDriverMapper orderDriverMapper;
    @Resource
    private SupplierWorkOrderService supplierWorkOrderService;

    //test
    @Resource
    private SupplierYueYueClient supplierYueYueClient;

    @RequestMapping("driverBlack")
    public String syncDriver(@RequestBody DriverSyncParams params){
        return this.riskCustomerService.syncDriver(params);
    }

    @RequestMapping("syncDriverByErp")
    public String syncDriverByErp(@RequestBody DriverSyncParams params){
        return this.riskCustomerService.syncDriverByErp(params);
    }

    @RequestMapping("syncHcDriver")
    public String syncHcDriver(@RequestBody HcSyncCustomerParam params){
        return this.riskCustomerService.syncHcDriver(params);
    }

    @RequestMapping("syncSensitiveWords")
    public void syncSensitiveWords(@RequestBody SensitiveWords words){
        sensitiveWordsMapper.insert(words);
    }

    @RequestMapping("generateWorkOrder")
    public void generateWorkOrder(@RequestBody CommonParams params){
        OrderInfo byOrderId = this.orderInfoMapper.findByOrderId(params.getOrderId());
        OrderDriver byOrderId1 = this.orderDriverMapper.findByOrderId(params.getOrderId());
        this.supplierWorkOrderService.saveWorkOrder(byOrderId,byOrderId1,"025",null);
    }

    @RequestMapping("testYueYueWorkerOrder")
    public void testYueYueWorkerOrder(@RequestBody JSONObject jsonObject){
        String orderId = jsonObject.getString("orderId");
        Integer beforeViolationId = jsonObject.getInteger("beforeViolationId");
        BigDecimal amount = jsonObject.getBigDecimal("amount");
//        TcSupplierWorkOrder tcSupplierWorkOrder = new TcSupplierWorkOrder();
//        tcSupplierWorkOrder.set
//        this.supplierYueYueClient.syncWorkerOrder(tcSupplierWorkOrder);
        this.supplierYueYueClient.syncDispose(orderId,beforeViolationId,amount);
    }
}
