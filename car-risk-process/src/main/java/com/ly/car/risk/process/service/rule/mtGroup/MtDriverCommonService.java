package com.ly.car.risk.process.service.rule.mtGroup;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.service.DriverHistoryService;
import com.ly.car.risk.process.service.dto.HcSceneConfig;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.dto.HitchBlackSendData;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@Scope("prototype")
public class MtDriverCommonService extends MtFilterHandler{

    @Resource
    private DriverHistoryService driverHistoryService;
    @Resource
    private MqSendConvertService mqSendConvertService;

    @Override
    public void doHandler(MtFilterContext context) {
        //对车牌进行加密
        Integer count = driverHistoryService.queryDriver(DigestUtils.md5Hex(context.getParam().getString("plate")));
        log.info("[HcDriverHistoryService][][][]司机认证查询完单司机库:{}",count);

        //查询测试配置，有就验证后续的流程，否则走下面的
        List<String> filterDriverList = queryConfig();
        if(filterDriverList != null && filterDriverList.contains(context.getParam().getString("plate"))){
            if(this.nextHandler == null && context.getDto().getCode() == 0){
                mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(context.getDto()));
            } else {
                this.nextHandler.doHandler(context);
            }
            return;
        }

        Map<String,Object> data = new HashMap<>();
        Map<String,Object> resultMap = new HashMap<>();
        data.put("driverId",context.getParam().getString("driverId"));
        data.put("idCard",context.getParam().getString("idCard"));
        context.getDto().setObj(data);
        if(count > 0){
            //通过
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(context.getDto()));
            return;
        }
        if(this.nextHandler == null && context.getDto().getCode() == 0){
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(context.getDto()));
        } else {
            this.nextHandler.doHandler(context);
        }
    }

    private List<String> queryConfig(){
        try {
            String configJson = ConfigCenterClient.get("filter_driver_history_config");
            if(StringUtils.isNotBlank(configJson)){
                return Arrays.asList(configJson.split(","));
            }
        } catch (Exception e) {
            log.error("获取汇川规则配置错误:",e);
        }
        return null;
    }
}
