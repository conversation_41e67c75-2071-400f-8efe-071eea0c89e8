package com.ly.car.risk.process.service.dto.order;

import com.ly.car.risk.process.constants.ProductLineEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Description of CarOrderDetail
 *
 * <AUTHOR>
 * @date 2024/3/11
 * @desc
 */
@Data
public class CarOrderDetail {

    /** 订单号 */
    private String          orderId;

    /** 供应链订单号 */
    private String          businessOrderNo;
    /** 供应商订单号 */
    private String          supplierOrderId;
    /** 渠道号 */
    private Integer         orderChannel;
    /** 会员MemberId */
    private String          memberId;
    /** 会员unionId */
    private String          unionId;
    /** 产品线 {@link ProductLineEnum} */
    private String productLine;
    /** 订单状态 参考wiki: http://wiki.17usoft.com/pages/viewpage.action?pageId=********* */
    private int             orderState;
    /** 生产:prod，预发:stage，测试:qa，dev测试:test，集成测试:uat */
    private String          env;
    /**  */
    private BaseOrderInfo   baseInfo = new BaseOrderInfo();

    private OrderTripInfo orderTrip = new OrderTripInfo();

    private List<UsedDiscountInfo> usedCoupons = new ArrayList<>();

    private CarPassenger passengerInfo = new CarPassenger();

    private CarInfo carInfo = new CarInfo();

    /** {@link com.ly.travel.car.tradecore.model.enums.OrderType} crmType */
    private int crmType;

    private boolean distributionOrder;

}