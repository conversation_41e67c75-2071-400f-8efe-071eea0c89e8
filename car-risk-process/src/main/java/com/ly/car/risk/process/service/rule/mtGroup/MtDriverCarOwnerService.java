package com.ly.car.risk.process.service.rule.mtGroup;

import com.ly.car.risk.process.api.CommonRiskClient;
import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyReqExtConst;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverCheck;
import com.ly.car.risk.process.service.DriverCheckService;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.StrategyUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class MtDriverCarOwnerService extends MtFilterHandler {

    @Resource
    private TianChuangRiskClient tianChuangRiskClient;
    @Resource
    private MqSendConvertService mqSendConvertService;
    @Resource
    private DriverCheckService driverCheckService;

    @Override
    public void doHandler(MtFilterContext context) {
        RiskResultNewDTO dto = new RiskResultNewDTO();
        Map<String, Object> data = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();
        data.put("driverId", context.getParam().get("driverId"));
        data.put("idCard", context.getParam().getString("idCard"));
        dto.setObj(data);
        if (!validParam(context.getParam())) {
            dto.setCode(1);
            dto.setMessage("行驶证异常，请更换");
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
            return;
        }
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setName(context.getParam().getString("carName").replace("(", "（").replace(")", "）"));
        param.setPlate(context.getParam().getString("plate"));
        if (param.getPlate().length() == 8) {
            param.setPlateType("52");
        } else {
            param.setPlateType((String) context.getParam().get("plateType"));
        }
        param.setProductLine(context.getParam().getString("productLine"));

        //验证次数
        List<DriverCheck> driverCheckList = context.getDriverCheckService()
                .queryResult(3, context.getParam().getString("carName")
                        , context.getParam().getString("plate"), context.getParam().getString("idCard"));

        if (CollectionUtils.isNotEmpty(driverCheckList)) {
            List<DriverCheck> failDriverCheckList = driverCheckList.stream().filter(ver -> ver.getResult() == 1).collect(Collectors.toList());
            if (failDriverCheckList.size() > 3) {
                //发送mq
                dto.setCode(1);
                dto.setMessage(failDriverCheckList.get(0).getRemark());
                if (dto.getMessage().equals("证件信息有误，请重新上传")) {
                    dto.setRecheck(1);
                }
                resultMap.put("drivingPermit", 1);
                data.put("resultMap", resultMap);
                dto.setObj(data);
                mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
                return;
            }
            List<DriverCheck> successDriverCheckList = driverCheckList.stream().filter(ver -> ver.getResult() == 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(successDriverCheckList)) {
                if (this.nextHandler == null && dto.getCode() == 0) {
                    mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
                } else {
                    this.nextHandler.doHandler(context);
                }
                return;
            }
        }
        StringBuilder msg = new StringBuilder();
//        Integer result = this.tianChuangRiskClient.verifyCarOwner(param,msg);
        StringBuilder msgB = new StringBuilder();
        Integer resultB = this.tianChuangRiskClient.verifyCarInfo(param, msgB);
        context.getDriverCheckService().insert(context.getParam(), 3, resultB, msgB.toString());
        if (resultB != 0) {
            //发送mq
            dto.setCode(1);
            dto.setMessage(StringUtils.isNotBlank(msg) ? msg.toString() : msgB.toString());
            if (dto.getMessage().equals("证件信息有误，请重新上传")) {
                dto.setRecheck(1);
            }
            resultMap.put("drivingPermit", 1);
            data.put("resultMap", resultMap);
            dto.setObj(data);
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
            return;
        }
        if (dto.getMessage().equals("证件信息有误，请重新上传")) {
            dto.setRecheck(1);
            resultMap.put("drivingPermit", 1);
            data.put("resultMap", resultMap);
            dto.setObj(data);
        }
        if (this.nextHandler == null && dto.getCode() == 0) {
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
        } else {
            this.nextHandler.doHandler(context);
        }
    }

    public Boolean validParam(Map<String, Object> context) {
        if (context.get("driverId") == null) {
            return false;
        }
        if (context.get("plate") == null) {
            return false;
        }
        if (context.get("plateType") == null) {
            return false;
        }
        return true;
    }

    public RiskSceneResult carOwnerCheck(UnifyCheckRequest request) {

        String carNum = request.getCarNum();
        String plateType = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.PLATE_TYPE);
        String carOwnerName = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CAR_OWNER_NAME);
        String certNo = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO);

        RiskResultNewDTO dto = new RiskResultNewDTO();
        Map<String, Object> data = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();
        data.put("driverId", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID));
        data.put("idCard", certNo);
        dto.setObj(data);

        if (StringUtils.isAnyBlank(carNum,plateType)) {
            dto.setCode(1);
            dto.setMessage("行驶证异常，请更换");
            mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
            return RiskSceneResult.fail("行驶证异常，请更换");
        }

        //验证次数
        List<DriverCheck> driverCheckList = driverCheckService.queryResult(3, carOwnerName, carNum, certNo);

        if (CollectionUtils.isNotEmpty(driverCheckList)) {
            List<DriverCheck> successDriverCheckList = driverCheckList.stream().filter(ver -> ver.getResult() == 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(successDriverCheckList)) {
                return RiskSceneResult.pass("");
            }
            List<DriverCheck> failDriverCheckList = driverCheckList.stream().filter(ver -> ver.getResult() == 1).collect(Collectors.toList());
            if (failDriverCheckList.size() > 3) {
                //发送mq
                dto.setCode(1);
                dto.setMessage(failDriverCheckList.get(0).getRemark());
                if (dto.getMessage().equals("证件信息有误，请重新上传")) {
                    dto.setRecheck(1);
                }
                resultMap.put("drivingPermit", 1);
                data.put("resultMap", resultMap);
                dto.setObj(data);
                mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
                return RiskSceneResult.fail("证件信息有误，请重新上传");
            }
        }

        StringBuilder msg = new StringBuilder();
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setName(carOwnerName.replace("(", "（").replace(")", "）"));
        param.setPlate(carNum);
        if (param.getPlate().length() == 8) {
            param.setPlateType("52");
        } else {
            param.setPlateType(plateType);
        }
        param.setProductLine(request.getProductLine());
        Integer result = this.tianChuangRiskClient.verifyCarInfo(param, msg);
        driverCheckService.insert(request, 3, result, msg.toString());

        if (result != 0) {
            //发送mq
            dto.setCode(1);
            dto.setMessage(msg.toString());
            if (dto.getMessage().equals("证件信息有误，请重新上传")) {
                dto.setRecheck(1);
            }
            resultMap.put("drivingPermit", 1);
            data.put("resultMap", resultMap);
            dto.setObj(data);
            mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
            return RiskSceneResult.fail(msg.toString());

        }
        return RiskSceneResult.pass("");
    }
}
