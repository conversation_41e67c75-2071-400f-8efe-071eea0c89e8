package com.ly.car.risk.process.turboMQ.listen;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.turboMQ.consumer.BingLogYncOrderInfoConsumer;
import com.ly.car.risk.process.turboMQ.consumer.SfcStatusChangeConsumer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class SfcStatusListen implements ApplicationListener<ApplicationStartedEvent>, DisposableBean {

    private static final String GROUP = "risk_group_sfc_status_listen";
    private static final String TOPIC = "yc_topic_sfc_order_status_change";

    @Resource
    private UrlsProperties urlsProperties;
    
    private DefaultMQPushConsumer consumer;
    
    @SneakyThrows
    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        consumer = new DefaultMQPushConsumer(GROUP);
        consumer.setNamesrvAddr(urlsProperties.getPublicMqServer());
        //每次拉去N条消息
        consumer.setConsumeMessageBatchMaxSize(1024);
        //设置消费模式=集群
        consumer.setMessageModel(MessageModel.CLUSTERING);
        //订阅PushTopic下tag为push的消息
        consumer.subscribe(TOPIC,"");
        consumer.registerMessageListener(new SfcStatusChangeConsumer());
        consumer.start();
        log.info("[MqStartupRunner][initTurboConsumer][SfcStatusChangeConsumer][]启动tuborMQ消费者-监听顺风车订单状态");
    }
    
    @Override
    public void destroy() {
        if(null != consumer){
            consumer.shutdown();
        }
    }
}
