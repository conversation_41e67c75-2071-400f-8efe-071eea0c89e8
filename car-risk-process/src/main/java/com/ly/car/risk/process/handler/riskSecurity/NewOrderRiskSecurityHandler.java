package com.ly.car.risk.process.handler.riskSecurity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.common.enums.VoiceApiProviderEnum;
import com.ly.car.risk.common.enums.VoiceProductTypeEnum;
import com.ly.car.risk.process.api.TencentCloudApiClient;
import com.ly.car.risk.process.client.VirtualPhoneApiClient;
import com.ly.car.risk.process.repo.dcdbcarrisk.mapper.RiskChargeVoiceMapper;
import com.ly.car.risk.process.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice;
import com.ly.car.risk.process.repo.dcdbcarrisk.mapper.entity.RiskChargeVoice.Ext;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderVirtualCallRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderVirtualCallRecord;
import com.ly.car.risk.process.service.RiskWarnService;
import com.ly.car.risk.process.service.VirtualPhoneRecordService;
import com.ly.car.risk.process.service.ability.AutoCallService;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.rule.imGrooup.ImFilterContext;
import com.ly.car.risk.process.service.rule.imGrooup.SensitiveTextService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.utils.JsonUtils;
import com.ly.car.virtualphoneApi.rsp.CallLogSearchRsp;
import com.ly.dal.util.DateUtil;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/**
 * Description of NewOrderRiskSecurityHandler
 *
 * <AUTHOR>
 * @date 2024/3/14
 * @desc
 */
@Service
@Slf4j
public class NewOrderRiskSecurityHandler extends AbstractRiskSecurityHandler {

    @Resource
    private CarOrderService carOrderService;

    @Resource(name = "virtualPhoneRecordService")
    private VirtualPhoneRecordService virtualPhoneRecordService;

    @Resource(name = "riskSecurityProducer")
    private MqRiskProducer riskSecurityProducer;

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource(name = "tencentCloudApiClient")
    private TencentCloudApiClient tencentCloudApiClient;

    @Resource(name = "textRiskProducer")
    private MqRiskProducer textRiskProducer;

    @Resource
    private SensitiveTextService sensitiveTextService;

    @Resource
    private VirtualPhoneApiClient virtualPhoneApiClient;

    @Resource
    private RiskOrderVirtualCallRecordMapper riskOrderVirtualCallRecordMapper;

    @Resource
    private AutoCallService autoCallService;

    @Resource
    private RiskWarnService riskWarnService;
    
    @Resource
    private RiskChargeVoiceMapper riskChargeVoiceMapper;


    @Override
    public String supportOrderType() {
        return "new";
    }

    @Override
    public void doHandler(String body, String tag) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        if (tag.equals(MqTagEnum.car_risk_self_security_task.name())) {
            // 司机接单后，发给此tag,让本tag去处理该订单的虚拟通话轮询
            dealTencentRecTask(body);
        } else if (tag.equals(MqTagEnum.car_self_tencent_get_describe_task.name())) {
            // 处理录音转文字的具体任务
            dealTencentDescTask(body);
        } else if (tag.equals(MqTagEnum.car_risk_self_auto_call_check_task.name())) {
            // 这是检查外呼是否执行成功
            checkAutoCall(jsonObject);
        } else if (tag.equals(MqTagEnum.car_risk_Self_on_car_sms_send.name())) {
            // 检查外呼，外呼失败了则发短信。意思是，调用方延迟5分钟调外呼检查，延迟10分钟调用外呼失败发短信，这是一套流程
            checkSmsSend(jsonObject);
        } else if (tag.equals(MqTagEnum.car_risk_warn_record.name())) {
            // 这是当定时任务校验出行程中，有长时间停留、车速块、偏移时，额外落库的异步操作
            saveWarnRisk(jsonObject);
        }
    }

    private void saveWarnRisk(JSONObject jsonObject) {
        String orderId = jsonObject.getString("orderId");
        Integer warnType = jsonObject.getInteger("warnType");
        String productLine = jsonObject.getString("productLine");
        riskWarnService.saveRiskWarn(orderId, warnType, productLine);
        LoggerUtils.info(log,"存储风险警示");
    }

    private void checkSmsSend(JSONObject jsonObject) {
        String callId = jsonObject.getString("callId");
        String orderId = jsonObject.getString("orderId");
        String mobile = jsonObject.getString("mobile");
        Integer plateId = jsonObject.getInteger("plateId");
        autoCallService.checkRecord(callId, orderId, mobile, plateId);
    }

    private void checkAutoCall(JSONObject jsonObject) {
        String callId = jsonObject.getString("callId");
        String orderId = jsonObject.getString("orderId");
        autoCallService.checkAutoCall(callId, orderId);
    }

    private void dealTencentDescTask(String body) {
        LoggerUtils.info(log, "开始进行腾讯云语音获取任务");

        JSONObject taskIdJson = JSONObject.parseObject(body);
        long taskId = taskIdJson.getLong("taskId");
        String orderId = taskIdJson.getString("orderId");
        String callId = taskIdJson.getString("callId");
        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);

        if (null == orderDetail) {
            LoggerUtils.info(log, "未查询到订单，不再处理");
            return;
        }

        // 查询是否已经处理过了，处理过则不再处理
        Long count = riskOrderVirtualCallRecordMapper.selectCount(new QueryWrapper<RiskOrderVirtualCallRecord>()
                .eq("order_id", orderDetail.getOrderId()).eq("call_id", callId));
        if (count > 0) {
            LoggerUtils.info(log, "该记录已处理过，不再处理,callId:{}", callId);
            return;
        }

        JSONObject jsonObject = tencentCloudApiClient.describeTaskStatus(taskId);
        LoggerUtils.info(log, "[][][][]录音转文本返回{}", JsonUtils.json(jsonObject));
        if (jsonObject == null) {
            return;
        }
        String text = jsonObject.getString("text");
        if (StringUtils.isNotBlank(text)) {
            //发送出去，业务组要存起来
            JSONObject textBody = new JSONObject();
            textBody.put("orderId", taskIdJson.getString("orderId"));
            textBody.put("text", text);
            textBody.put("callId", taskIdJson.getString("callId"));
            textRiskProducer.send(MqTagEnum.car_risk_sfc_text, textBody.toJSONString(), 0);
            LoggerUtils.info(log, "将解析后的文本数据发送给业务组，tag:{},msg:{}", MqTagEnum.car_risk_sfc_text.name(), JSON.toJSONString(textBody));

            //过下敏感词
            LoggerUtils.info(log,"将解析后的文本，进行敏感词校验");
            ImFilterContext context = new ImFilterContext();
            context.setUnionId(taskIdJson.getString("unionId"));
            context.setMemberId(taskIdJson.getString("memberId"));
            context.setText(text);
            context.setSource(taskIdJson.getInteger("source"));//虚拟号
            context.setOrderId(taskIdJson.getString("orderId"));
            context.setDriverCardNo(taskIdJson.getString("driverCardNo"));
            sensitiveTextService.doHandler(context);

            // 处理过的，这种recordId可以记录缓存，同时新增一张表进行存储ß
            RiskOrderVirtualCallRecord callRecord = new RiskOrderVirtualCallRecord();
            callRecord.setOrderId(orderId);
            callRecord.setVirtualPhoneNo(orderDetail.getCarInfo().getDriverVirtualPhone());
            callRecord.setCallId(taskIdJson.getString("callId"));
            callRecord.setCallContent(text);
            callRecord.setCreateTime(new Date());
            callRecord.setUpdateTime(new Date());
            riskOrderVirtualCallRecordMapper.insert(callRecord);
            
            // 保存调用统计
            RiskChargeVoice riskChargeVoice = new RiskChargeVoice();
            riskChargeVoice.setOrderNo(orderId);
            riskChargeVoice.setSource(String.valueOf(orderDetail.getOrderChannel()));
            riskChargeVoice.setProductLine(orderDetail.getProductLine());
            riskChargeVoice.setApiProvider(VoiceApiProviderEnum.TX_VOICE.getCode());
            riskChargeVoice.setProductType(VoiceProductTypeEnum.VIRTUAL_PHONE.getCode());
            BigDecimal audioDuration = jsonObject.getBigDecimal("audioDuration");
            riskChargeVoice.setVoiceDuring(audioDuration == null ? BigDecimal.ZERO : audioDuration);
            riskChargeVoice.setCharge(new BigDecimal(0.46/60/60).multiply(riskChargeVoice.getVoiceDuring()).setScale(6, RoundingMode.HALF_UP));
            RiskChargeVoice.Ext ext = new Ext();
            ext.setReq(String.valueOf(taskId));
            riskChargeVoice.setExt(JSON.toJSONString(ext));
            riskChargeVoice.setCreateTime(new Date());
            riskChargeVoice.setUpdateTime(new Date());
            riskChargeVoiceMapper.insert(riskChargeVoice);
        }
    }


    public void dealTencentRecTask(String body) {
        LoggerUtils.info(log, "开始发起虚拟通话轮训任务");
        // 统一配置开关
        Map<String, Boolean> configMap = queryConfig();
        if (configMap != null && !configMap.get("recTask")) {
            LoggerUtils.info(log, "未开启虚拟通话处理开关，不再监听");
            return;
        }
        JSONObject param = JSONObject.parseObject(body);
        //判断当前时间距离是否超过24h
        Date startTime = param.getDate("startTime");
        String orderId = param.getString("orderId");
        if (DateUtil.addHour(startTime, 24).before(new Date())) {
            LoggerUtils.info(log, "该任务开始时间已超过24小时，不再监听{}", orderId);
            return;
        }

        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
        if (null == orderDetail) {
            LoggerUtils.info(log, "未查询到该订单，不再监听{}", orderId);
            return;
        }
        // 只考虑 已接单 or 已到达 的场景
        if (orderDetail.getOrderState() != OrderState.RECEIVING_ORDER.getCode() && orderDetail.getOrderState() != OrderState.AWAITING_TRAVEL.getCode()) {
            LoggerUtils.info(log, "当前订单非已接单，不再监听,当前状态", orderDetail.getOrderState());
            return;
        }

        String virtualPhone = orderDetail.getCarInfo().getDriverVirtualPhone();
        if (StringUtils.isBlank(virtualPhone)) {
            LoggerUtils.info(log, "未从订单中获取到虚拟号码，退出监听");
            return;
        }

        List<CallLogSearchRsp> callResults = virtualPhoneApiClient.search(orderDetail.getOrderId(), virtualPhone);
        // 3分钟之后再处理一次
        param.put("startTime", new Date());
        riskSecurityProducer.send(MqTagEnum.car_risk_self_security_task, JsonUtils.json(param), DateUtil.addMinute(new Date(), 3).getTime());

        if (CollectionUtils.isEmpty(callResults)) {
            LoggerUtils.info(log, "当前订单未查到通话记录，发送下次监听消息{}", JSON.toJSONString(param));
            return;
        }

        List<String> callIdList = riskOrderVirtualCallRecordMapper.selectList(new QueryWrapper<RiskOrderVirtualCallRecord>().eq("order_id", orderId))
                .stream().map(p -> p.getCallId()).collect(Collectors.toList());

        if (null != callIdList && callIdList.size() > 0) {
            callResults = callResults.stream().filter(p -> !callIdList.contains(p.getCallId())).collect(Collectors.toList());
        }
        callResults = callResults.stream().filter(p->StringUtils.isNotBlank(p.getSoundUrl())).collect(Collectors.toList());
        LoggerUtils.info(log, "需处理的通话记录:{}", JSON.toJSONString(callResults));

        // Map<callId,soundUrl>
        Map<String, String> videoAndUrlMap = callResults.stream().filter(p -> StringUtils.isNotBlank(p.getSoundUrl()))
                .collect(Collectors.toMap(p -> p.getCallId(), v -> v.getSoundUrl(), (k1, k2) -> k1));

        //请求腾讯给他们
        for (Map.Entry<String, String> entry : videoAndUrlMap.entrySet()) {
            Long recTaskId = tencentCloudApiClient.createRecTask(0, entry.getValue());
            JSONObject taskJsonObject = new JSONObject();
            taskJsonObject.put("taskId", recTaskId);
            taskJsonObject.put("times", 1);
            taskJsonObject.put("unionId", orderDetail.getUnionId());
            taskJsonObject.put("memberId", orderDetail.getMemberId());
            taskJsonObject.put("orderId", orderDetail.getOrderId());
            taskJsonObject.put("driverCardNo", orderDetail.getCarInfo().getCarNum());
            taskJsonObject.put("source", 2);
            taskJsonObject.put("callId", entry.getKey());
            if (recTaskId != null) {
                //发送获取结果mq
                riskSecurityProducer.send(MqTagEnum.car_self_tencent_get_describe_task, JsonUtils.json(taskJsonObject), DateUtil.addMinute(new Date(), 5).getTime());
                LoggerUtils.info(log, "获取到腾讯云的taskId:{}，mq触发获取结果任务,tag:{}, msg:{}", recTaskId,
                        MqTagEnum.car_self_tencent_get_describe_task.name(), JSON.toJSONString(taskJsonObject));
            }
        }
    }

    @NotNull
    private static String alreadyDealCallId(CarOrderDetail orderDetail) {
        return "newOrder:virtualPhone:record:order:" + orderDetail.getOrderId();
    }


}