package com.ly.car.risk.process.controller.params;

import lombok.Data;

@Data
public class DriverBlackParam {

    //乘车人手机号
    private String passengerCellphone;
    //车牌号
    private String driverCardNo;
    //司机名称
    private String driverName;
    //拉黑原因
    private String remark;
    //绑定订单，纯记录
    private String orderId;
    //上车点
    private String startAddress;
    //下车点
    private String endAddress;
    //用车时间
    private String useTime;
}
