package com.ly.car.risk.process.supplier.Tsan;

import lombok.Data;

@Data
public class T3WorkOrderParam extends T3BaseReq{

    private Integer operateType;//操作类型 0：创建工单 1：回复工单 2：完结工单
    private String platformOrderNo;//T3平台订单号
    private String platformWorkOrderNo;//T3工单号
    private String cpWorkOrderNo;//合作方工单号
    private String woCateCode;//自定义风险类型
    private Long eventTime;//事件发生或处理时间（毫秒级时间戳）
    private String msg;//描述
    private String userType;
    private String userMobile;//投诉人手机号
    private String attachments;//用户反馈证据
    private JudgeInfo judgeInfo;//判责信息,若判定为司机有责，将按照判责信息调整订单流水



    @Data
    public static class JudgeInfo{
        private String unionId;//处罚的唯一标识
        private Integer judgeType;//处罚类型 6-价格清零，7-退款，8-调价
        private String reason;//处罚原因
        private PriceChangeDetail priceChangeDetail;//流水扣减明细
    }

    @Data
    public static class PriceChangeDetail{
        private String changeFee;//总扣款金额
        private String changeOrderFee;//行程费扣减金额
        private String changeAttachFee;//附加扣减费用
        private String changeCrossCityFee;//跨城费扣减金额
        private String changeFestivalFee;//节日服务费扣减金额
        private String changeParkingFee;//停车费扣减金额
        private String changeHighwayFee;//高速费扣减金额
        private String changeRoadBrigeFee;//路桥费扣减金额
        private String changeOtherFee;//其他费用扣减金额
    }


}
