package com.ly.car.risk.process.service.safecenter.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Joiner;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.api.LabelClient;
import com.ly.car.risk.process.api.rsp.LabelRsp;
import com.ly.car.risk.process.client.AssistServiceClient;
import com.ly.car.risk.process.client.OrderClient;
import com.ly.car.risk.process.client.WorkOrderClient;
import com.ly.car.risk.process.client.model.WordOrderRes;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.controller.request.safecenter.AppealOrderRequest;
import com.ly.car.risk.process.controller.request.safecenter.EmergencyContactRequest;
import com.ly.car.risk.process.controller.request.safecenter.ReminderRequest;
import com.ly.car.risk.process.model.enums.SafeReminderEventEnum;
import com.ly.car.risk.process.model.enums.WorkOrderEnum;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.repo.risk.mapper.SafeCenterAppealRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.SafeCenterShareMapper;
import com.ly.car.risk.process.repo.risk.mapper.SensitiveRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.SafeCenterAppealRecord;
import com.ly.car.risk.process.repo.risk.mapper.entity.SafeCenterShare;
import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveRecord;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.order.BaseOrderInfo;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.dto.order.OrderTripInfo;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.rule.common.SafeWarningResult;
import com.ly.car.risk.process.service.safecenter.SafeCenterService;
import com.ly.car.risk.process.service.safecenter.dto.AppearWorkOrderResult;
import com.ly.car.risk.process.service.safecenter.dto.SafetyCenterShareResult;
import com.ly.car.risk.process.service.safecenter.dto.SaveCenterFeedBackQueryDto;
import com.ly.car.risk.process.service.workOrder.dto.FormItems;
import com.ly.car.risk.process.support.UiResultWrapper;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.DriverLocationResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/**
 * Description of SafeCenterServiceImpl
 *
 * <AUTHOR>
 * @date 2024/3/13
 * @desc
 */
@Service
@Slf4j
public class SafeCenterServiceImpl implements SafeCenterService {

    @Resource
    private SafeCenterShareMapper safeCenterShareMapper;
    @Resource
    private SafeCenterAppealRecordMapper safeCenterAppealRecordMapper;

    @Resource
    private WorkOrderClient workOrderClient;

    @Resource
    private CarOrderService carOrderService;

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @Resource
    private OrderClient orderClient;

    @Resource
    private LabelClient labelClient;

    @Resource
    SensitiveRecordMapper sensitiveRecordMapper;
    
    @Resource
    private AssistServiceClient assistServiceClient;

    // 远行程阈值  原逻辑是100公里，因为新订单存的是米，所以转换下
    private static final BigDecimal FAR_TRIP_DISTANCE = new BigDecimal(100000);


    @Override
    public UiResult querySafeReminder(ReminderRequest request) {
        if (!checkParam(request)) {
            UiResult fail = UiResult.fail(-1);
            fail.setMsg("司机安全预警错误-参数缺失");
            return fail;
        }
        SafeReminderEventEnum eventType = SafeReminderEventEnum.findByEvent(request.getEventType());
        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(request.getOrderId());
        if (null == orderDetail) {
            return UiResult.ok();
        }
        String noHintRuleOrderCache = String.format(RedisKeyConstants.SAFE_REMINDER_ORDER_NO_HINT_RULE_CACHE,orderDetail.getOrderId());
        RMap<String, String> noHintRuleMap = redissonClient.getMap(noHintRuleOrderCache);
        switch (eventType) {
            case SERVICE_CHECK:
                return serviceCheckReminder(request, orderDetail,noHintRuleMap);
            case SERVICE_START:
                return serviceStartReminder(request, orderDetail,noHintRuleMap);
            case SERVICE_CANCEL:
                return serviceCancelReminder(request);
            case CONFIRM_ARRIVAL:
                return confirmArrivalReminder(request, orderDetail);
            case NO_NEED_HINT_RULE:
                return noNeedHintRuleMark(request,orderDetail);
            default:
                return UiResult.ok();
        }
    }

    private UiResult noNeedHintRuleMark(ReminderRequest request, CarOrderDetail orderDetail) {
        String noHintRuleNos = request.getNoHintRuleNos();
        if(StringUtils.isBlank(noHintRuleNos)){
            return UiResult.ok();
        }
        List<String> ruleNos = StrUtil.split(noHintRuleNos, ",", true, true);
        if(CollUtil.isEmpty(ruleNos)){
            return UiResult.ok();
        }
        String cacheKey = String.format(RedisKeyConstants.SAFE_REMINDER_ORDER_NO_HINT_RULE_CACHE,orderDetail.getOrderId());
        RMap<String, String> map = redissonClient.getMap(cacheKey);
        for(String rule : ruleNos){
            map.put(rule,"1");
        }
        redissonClient.getKeys().expire(cacheKey, 1, TimeUnit.DAYS);
        return UiResult.ok();
    }

    private UiResult confirmArrivalReminder(ReminderRequest request, CarOrderDetail orderDetail) {
        OrderTripInfo orderTrip = orderDetail.getOrderTrip();
        List<SafeWarningResult> safeReminderList = new ArrayList<>();
        //确认到达目的地
        DriverLocationResponse locationResp = orderClient.driverLocation(request.getOrderId());
        if (null != locationResp) {
            BigDecimal lng = locationResp.getLongitude();
            BigDecimal lat = locationResp.getLatitude();
            double distance = CoordUtil.getDistance(lng, lat, orderTrip.getArrivalLng(), orderTrip.getArrivalLat());
            if (new BigDecimal(distance).compareTo(new BigDecimal("3000")) > 0) {
                SafeWarningResult safeWarningResult = new SafeWarningResult();
                safeWarningResult.setRuleNo("aq010");
                safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
                safeWarningResult.setText("平台检测到您暂未到达目的地，您确定已经到达并结束行程？");
                safeWarningResult.setSort(3);
                safeReminderList.add(safeWarningResult);
            }
        }
        return UiResult.ok(safeReminderList);
    }

    private UiResult serviceCancelReminder(ReminderRequest request) {
        List<SensitiveRecord> recordList = sensitiveRecordMapper.selectList(new QueryWrapper<SensitiveRecord>()
                .eq("order_id", request.getOrderId())
        );
        List<SafeWarningResult> safeReminderList = new ArrayList<>();
        if (recordList.stream().anyMatch(data -> data.getWordType() == 5)) {
            RiskResultDTO dto = new RiskResultDTO(1, "风控不通过-命中敏感词", "", null);
            SafeWarningResult safeWarningResult = new SafeWarningResult();
            safeWarningResult.setRuleNo("cancel_001");
            safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
            safeWarningResult.setText("检测到线下交易风险，确认取消吗？");
            safeWarningResult.setSort(3);
            safeReminderList.add(safeWarningResult);
        }

        return UiResult.ok(safeReminderList);
    }

    private UiResult serviceStartReminder(ReminderRequest request, CarOrderDetail orderDetail,RMap<String, String> noHintRuleMap) {
        // 不是行程中，则不反回
        if (orderDetail.getOrderState() != OrderState.IN_TRIP.getCode()) {
            return UiResult.ok();
        }
        List<SafeWarningResult> safeReminderList = new ArrayList<>();
        OrderTripInfo orderTrip = orderDetail.getOrderTrip();
        if (orderTrip.getEstimateRunDistance().compareTo(FAR_TRIP_DISTANCE) > 0) {
            SafeWarningResult resultFar = new SafeWarningResult();
            resultFar.setRuleNo("aq012");
            resultFar.setHitTime(DateUtil.date2String(new Date()));
            resultFar.setText("确认上车，路线较远");
            resultFar.setSort(4);
            safeReminderList.add(resultFar);
        }
        SafeWarningResult onCarReminder = new SafeWarningResult();
        onCarReminder.setRuleNo("aq016");
        onCarReminder.setHitTime(DateUtil.date2String(new Date()));
        onCarReminder.setText("请系好安全带，尽量避免交谈");
        onCarReminder.setSort(7);
        safeReminderList.add(onCarReminder);

        LabelRsp.DetailRsp detailRsp = labelClient.queryLabel(orderDetail.getOrderChannel(),orderDetail.getMemberId(), orderDetail.getUnionId());
        if (detailRsp != null && (detailRsp.getGender() == 2 || detailRsp.getStudent() == 1)) {
            SafeWarningResult resultStu = new SafeWarningResult();
            resultStu.setRuleNo("aq017");
            resultStu.setHitTime(DateUtil.date2String(new Date()));
            resultStu.setText("请避免过度交谈，不要透露个人信息");
            resultStu.setSort(2);
            safeReminderList.add(resultStu);
        }

        // 订单备注
        BaseOrderInfo baseOrderInfo = orderDetail.getBaseInfo();
        String remark = baseOrderInfo.getRemark();

        if (StringUtils.isNotBlank(remark)) {
            if (remark.contains("孕妇") || remark.contains("老人")) {
                SafeWarningResult specialUserReminder = new SafeWarningResult();
                specialUserReminder.setRuleNo("aq018");
                specialUserReminder.setHitTime(DateUtil.date2String(new Date()));
                specialUserReminder.setText("行程中如有不适请告知车主或联系客服");
                specialUserReminder.setSort(3);
                safeReminderList.add(specialUserReminder);
            }
            // 如果拼车成功
            if (remark.contains("宠物")) {
                SafeWarningResult animalReminder = new SafeWarningResult();
                animalReminder.setRuleNo(baseOrderInfo.isCarPooling() ? "aq019" : "aq020");
                animalReminder.setHitTime(DateUtil.date2String(new Date()));
                animalReminder.setText("请照顾好您的宠物，避免" + (baseOrderInfo.isCarPooling() ? "打扰车主驾驶" : "影响同行人"));
                animalReminder.setSort(baseOrderInfo.isCarPooling() ? 5 : 6);
                safeReminderList.add(animalReminder);

            }
        }

        return UiResult.ok(safeReminderList);
    }

    private UiResult serviceCheckReminder(ReminderRequest request, CarOrderDetail orderDetail,RMap<String, String> noHintRuleMap) {
        List<SafeWarningResult> safeReminderList = new ArrayList<>();

        OrderTripInfo orderTrip = orderDetail.getOrderTrip();
        BaseOrderInfo baseOrderInfo = orderDetail.getBaseInfo();

        // 如果当前状态是行程中
        if (orderDetail.getOrderState() == OrderState.IN_TRIP.getCode()) {

            // 网约车 校验：长时间停留、路线偏移、车速过快的3个提示
            if(Objects.equals(orderDetail.getProductLine(), ProductLineEnum.YNC.getCode())){
                RMap<String, String> rMap = redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING + orderDetail.getOrderId());
                log.info("[][][][]命中安全预警获取缓存:{}", JsonUtils.json(rMap));
                if (rMap != null && !rMap.isEmpty()) {
                    for (Map.Entry<String, String> entry : rMap.entrySet()) {
                        String ruleNo = entry.getKey();
                        // 如果用户确认过该弹窗，则不再提示
                        if(noHintRuleMap.isExists() && noHintRuleMap.containsKey(ruleNo)){
                            continue;
                        }
                        SafeWarningResult safeWarningResult = new SafeWarningResult();
                        safeWarningResult.setRuleNo(ruleNo);
                        safeWarningResult.setHitTime(entry.getValue().split(",")[0]);
                        safeWarningResult.setText(entry.getValue().split(",")[1]);
                        safeReminderList.add(safeWarningResult);
                    }
                }
            }

            // 顺风车 校验: 行程过半、长时间未到达
            if(Objects.equals(orderDetail.getProductLine(), ProductLineEnum.SFC.getCode()) && null!=orderDetail.getOrderTrip().getOldEstimateKilo()
                    && orderDetail.getOrderTrip().getOldEstimateKilo().compareTo(new BigDecimal(100)) >= 0){
                // 如果不存在aq009 确认弹窗
                if(!noHintRuleMap.isExists() || !noHintRuleMap.containsKey("aq009")){
                    double originalDistance = CoordUtil.getDistance(orderTrip.getDepartureLng(), orderTrip.getDepartureLat(), orderTrip.getArrivalLng(), orderTrip.getArrivalLat());
                    DriverLocationResponse locationResp = orderClient.driverLocation(orderDetail.getOrderId());
                    if (null != locationResp && locationResp.isSuccess() && null != locationResp.getLongitude() && null != locationResp.getLatitude()) {
                        BigDecimal lng = locationResp.getLongitude();
                        BigDecimal lat = locationResp.getLatitude();
                        double nowDistance = CoordUtil.getDistance(orderTrip.getDepartureLng(), orderTrip.getDepartureLat(), lng, lat);
                        if (new BigDecimal(nowDistance).divide(new BigDecimal(originalDistance), 4, BigDecimal.ROUND_DOWN).compareTo(new BigDecimal("0.5")) > 0) {
                            SafeWarningResult resultHalf = new SafeWarningResult();
                            resultHalf.setRuleNo("aq009");
                            resultHalf.setHitTime(DateUtil.date2String(new Date()));
                            resultHalf.setText("行程已过半，若有任何问题，请及时联系我们，安全助手持续为您护航");
                            resultHalf.setSort(3);
                            safeReminderList.add(resultHalf);
                        }
                    }
                }

                // 如果不存在aq011确认弹窗
                if(!noHintRuleMap.isExists() || !noHintRuleMap.containsKey("aq011")){
                    // 长时间未结束车程
                    if (DateUtil.addMinute(DateUtil.addMinute(baseOrderInfo.getGmtDeparture(), orderTrip.getOldEstimateMinute()), 60).before(new Date())
                            && orderDetail.getOrderState() == OrderState.IN_TRIP.getCode()) {
                        SafeWarningResult safeWarningResult = new SafeWarningResult();
                        safeWarningResult.setRuleNo("aq011");
                        safeWarningResult.setHitTime(DateUtil.date2String(new Date()));
                        safeWarningResult.setText("平台检测到当前行程已超过预期结束时间，请确认是否到达目的地？");
                        safeWarningResult.setSort(3);
                        safeReminderList.add(safeWarningResult);
                    }
                }
            }

        }

        // 下面这是不仅限于行程中的
        // 如果已接单或司机已到达， 超过用车时间10分钟
        if(Objects.equals(orderDetail.getProductLine(), ProductLineEnum.SFC.getCode())){
            if(!noHintRuleMap.isExists() || !noHintRuleMap.containsKey("aq008")){
                if (DateUtil.addMinute(baseOrderInfo.getGmtUsage(), 10).before(new Date())
                        && (orderDetail.getOrderState() == OrderState.RECEIVING_ORDER.getCode() || orderDetail.getOrderState() == OrderState.AWAITING_TRAVEL.getCode())) {
                    SafeWarningResult unBoardReminder = new SafeWarningResult();
                    unBoardReminder.setRuleNo("aq008");
                    unBoardReminder.setHitTime(DateUtil.date2String(new Date()));
                    unBoardReminder.setText("平台检测到您未在预期开始时间内确认上车，请确认是否已经上车？");
                    unBoardReminder.setSort(1);
                    safeReminderList.add(unBoardReminder);
                }
            }
        }

        return UiResult.ok(safeReminderList);

    }

    private boolean checkParam(ReminderRequest request) {
        if (StringUtils.isBlank(request.getOrderId()) || StringUtils.isBlank(request.getProductLine())) {
            return false;
        }
        if (!"YNC".equalsIgnoreCase(request.getProductLine()) && !"SFC".equalsIgnoreCase(request.getProductLine())) {
            return false;
        }
        if (null == SafeReminderEventEnum.findByEvent(request.getEventType())) {
            return false;
        }
        return true;
    }


    @Override
    public UiResult feedBackWorkOrder(AppealOrderRequest request) throws BizException {
        if (!checkWorkOrderBasicParam(request.getOrderId(), request.getMemberId())) {
            return UiResultWrapper.fail(-1, "安全中心反馈错误-参数缺失");
        }
        List<SafeCenterAppealRecord> safeCenterFeedbacks = getSafeCenterAppealRecord(request);
        if (CollUtil.isNotEmpty(safeCenterFeedbacks) &&
                safeCenterFeedbacks.stream().findFirst().get().getAppealResults() == 1) {

            return UiResultWrapper.fail(500, "请勿重复反馈");
        }
        CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(request.getOrderId());
        if (carOrderDetail == null) {
            return UiResultWrapper.fail(500, "订单不存在");
        }
        WordOrderRes wordOrderRes = submitWorkOrder(request, carOrderDetail);
        if (CollUtil.isNotEmpty(safeCenterFeedbacks)) {
            SafeCenterAppealRecord safeCenterAppealRecord = safeCenterFeedbacks.stream().findFirst().get();
            safeCenterAppealRecord.setAppealTime(new Date());
            safeCenterAppealRecord.setCurrentCarNo(request.getCurrentCarNo());
            safeCenterAppealRecord.setAppealContent(request.getQuestionDesc());
            safeCenterAppealRecord.setAppealImg(CollUtil.isEmpty(request.getDescImage()) ? "" :
                    Joiner.on(",").skipNulls().join(request.getDescImage()));
            safeCenterAppealRecord.setAppealResults(wordOrderRes == null || !wordOrderRes.getCode().equals("10000") ? 0 : 1);
            safeCenterAppealRecord.setOriginalCarNo(carOrderDetail.getCarInfo().getCarNo());
            safeCenterAppealRecord.setWorkOrderNo(wordOrderRes == null
                    || wordOrderRes.getData() == null ? "" : wordOrderRes.getData().getOrderId());
            safeCenterAppealRecordMapper.updateById(safeCenterAppealRecord);
            return UiResult.ok();
        }
        saveAppearWorkOrder(request, wordOrderRes, carOrderDetail, WorkOrderEnum.YC_CAR_NUMBER_NOT_MATCH);
        return UiResult.ok();
    }

    @Override
    public UiResult queryWorkOrderFeedBack(AppealOrderRequest request) {
        request.setWorkOrderType(WorkOrderEnum.findByType(request.getQueryFeedBackType()));
        SaveCenterFeedBackQueryDto res = new SaveCenterFeedBackQueryDto();
        res.setFeedBackResult(0);

        List<SafeCenterAppealRecord> safeCenterFeedback = getSafeCenterAppealRecord(request);
        if (CollUtil.isEmpty(safeCenterFeedback)) {
            return UiResult.ok(res);
        }
        res.setFeedBackResult(safeCenterFeedback.stream().findFirst().get().getAppealResults());
        return UiResult.ok(res);
    }


    @Override
    public UiResult saveEmergencyContact(EmergencyContactRequest request) {
        if (StringUtils.isBlank(request.getMemberId())) {
            return UiResultWrapper.fail(-1, "安全中心紧急联系人错误-参数缺失");
        }
        if (!Validator.isMobile(request.getEmergencyPhone())) {
            return UiResultWrapper.fail(-1, "安全中心紧急联系人错误-手机号不正确");
        }
        
        SafeCenterShare shareUserInfo = getRecentUpdateShareInfo(request.getMemberId(), request.getTraceId());
        
        if (shareUserInfo == null) {
            // 更新
            SafeCenterShare safeCenterShare = new SafeCenterShare();
            safeCenterShare.setCreateTime(new Date());
            safeCenterShare.setUpdateTime(new Date());
            safeCenterShare.setUnionId(request.getUnionId());
            safeCenterShare.setMemberId(request.getMemberId());
            safeCenterShare.setChannel(request.getChannel());
            safeCenterShare.setEmergencyContactName(request.getEmergencyContactName());
            safeCenterShare.setEmergencyPhone(request.getEmergencyPhone());
            safeCenterShareMapper.insert(safeCenterShare);
            return UiResult.ok();
        }
        //更新紧急联系人
        shareUserInfo.setEmergencyContactName(request.getEmergencyContactName());
        shareUserInfo.setEmergencyPhone(request.getEmergencyPhone());
        safeCenterShareMapper.updateById(shareUserInfo);

        return UiResult.ok();
    }

    @Override
    public UiResult saveShareInfo(EmergencyContactRequest request) {
        if (StringUtils.isBlank(request.getMemberId())) {
            return UiResultWrapper.fail(-1, "安全中心紧急联系人错误-参数缺失");
        }
        
        SafeCenterShare shareUserInfo = getRecentUpdateShareInfo(request.getMemberId(), request.getTraceId());

        if (ObjectUtil.isEmpty(shareUserInfo)) {
            //保存紧急联系人
            SafeCenterShare safeCenterShare = new SafeCenterShare();
            safeCenterShare.setCreateTime(new Date());
            safeCenterShare.setUnionId(request.getUnionId());
            safeCenterShare.setMemberId(request.getMemberId());
            safeCenterShare.setUpdateTime(new Date());
            safeCenterShare.setChannel(request.getChannel());
            safeCenterShare.setJourneyShareSwitch(request.getJourneyShareSwitch());
            safeCenterShare.setShareBeginTime(request.getShareBeginTime());
            safeCenterShare.setShareEndTime(request.getShareEndTime());
            safeCenterShare.setAuthorizedRecordingSwitch(request.getAuthorizedRecordingSwitch());
            safeCenterShareMapper.insert(safeCenterShare);
            return UiResult.ok();
        }
        //更新
        shareUserInfo.setJourneyShareSwitch(request.getJourneyShareSwitch() == null
                ? shareUserInfo.getJourneyShareSwitch() : request.getJourneyShareSwitch());
        shareUserInfo.setShareBeginTime(StringUtils.isBlank(request.getShareBeginTime())
                ? shareUserInfo.getShareBeginTime() : request.getShareBeginTime());
        shareUserInfo.setShareEndTime(StringUtils.isBlank(request.getShareEndTime())
                ? shareUserInfo.getShareEndTime() : request.getShareEndTime());
        shareUserInfo.setAuthorizedRecordingSwitch(request.getAuthorizedRecordingSwitch() == null
                ? shareUserInfo.getAuthorizedRecordingSwitch() : request.getAuthorizedRecordingSwitch());
        safeCenterShareMapper.updateById(shareUserInfo);
        return UiResult.ok();
    }

    @Override
    public UiResult saveAlterWorkOrder(AppealOrderRequest request) throws BizException {
        if (!checkWorkOrderBasicParam(request.getOrderId(), request.getMemberId())) {
            return UiResultWrapper.fail(-1, "安全中心反馈错误-参数缺失");
        }
        CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(request.getOrderId());
        if (carOrderDetail == null) {
            return UiResultWrapper.fail(-1, "安全中心反馈错误 订单不存在");
        }
        // 110告警工单重试3次
        int times = 0;
        int retryTimes = 3;
        WordOrderRes wordOrderRes = null;
        do {
            try {
                wordOrderRes = submitWorkOrder(request, carOrderDetail);
                break;
            } catch (BizException e) {
                times++;
                if(times > retryTimes){
                    LoggerUtils.error(log, "安全中心反馈错误，重试后仍然失败, 放弃尝试", e);
                    throw e;
                }
                LoggerUtils.warn(log, "安全中心反馈错误，准备进行第{}次重试", e, times);
            }
        } while (times <= retryTimes);

        saveAppearWorkOrder(request, wordOrderRes, carOrderDetail, WorkOrderEnum.SAFETY_POLICE);
        return UiResult.ok();
    }
    
    @Override
    public UiResult queryUserShareInfo(EmergencyContactRequest request) {
        if (StringUtils.isBlank(request.getMemberId())) {
            return UiResultWrapper.fail(-1, "安全中心紧急联系人错误-参数缺失");
        }
        
        SafetyCenterShareResult result = new SafetyCenterShareResult();
        SafeCenterShare shareInfo = getRecentUpdateShareInfo(request.getMemberId(), request.getTraceId());
        
        if (null == shareInfo) {
            return UiResult.ok(result);
        }
        
        result.setEmergencyPhone(shareInfo.getEmergencyPhone());
        result.setEmergencyContactName(shareInfo.getEmergencyContactName());
        result.setShareBeginTime(shareInfo.getShareBeginTime());
        result.setShareEndTime(shareInfo.getShareEndTime());
        result.setJourneyShareSwitch(shareInfo.getJourneyShareSwitch());
        result.setAuthorizedRecordingSwitch(shareInfo.getAuthorizedRecordingSwitch());
        result.setEmergencyContactFlag(StringUtils.isBlank(shareInfo.getEmergencyContactName())
                || StringUtils.isBlank(shareInfo.getEmergencyPhone()) ? 0 : 1);
        return UiResult.ok(result);
    }
    
    @Override
    public SafeCenterShare getRecentUpdateShareInfo(String memberId, String traceId) {
        // 获取0/33体系下保存的信息
        traceId = StringUtils.isBlank(traceId) ? UUID.randomUUID().toString() : traceId;
        List<String> memberIds = assistServiceClient.queryMembers(memberId, traceId);
        List<SafeCenterShare> shareInfoList = safeCenterShareMapper.selectList(new QueryWrapper<SafeCenterShare>()
                .in("member_id", memberIds));
        
        if (CollectionUtils.isEmpty(shareInfoList)) {
            return null;
        }
        
        // 存在多个，取最近更新的
        shareInfoList.sort(Comparator.comparing(SafeCenterShare::getUpdateTime).reversed());
        return shareInfoList.get(0);
    }

    @Override
    public UiResult queryFeeAppearWorkOrder(AppealOrderRequest request) {
        AppearWorkOrderResult appearWorkOrderResult = new AppearWorkOrderResult();
        List<SafeCenterAppealRecord> safeCenterAppealRecords = getSafeCenterAppealRecord(request);
        if (CollUtil.isEmpty(safeCenterAppealRecords)) {
            return UiResult.ok();
        }
        SafeCenterAppealRecord safeCenterAppealRecord = safeCenterAppealRecords.stream().findFirst().get();
        appearWorkOrderResult.setDescImage(StringUtils.isBlank(safeCenterAppealRecord.getAppealImg()) ? new ArrayList<>()
                : Arrays.asList(safeCenterAppealRecord.getAppealImg().split(",")));
        appearWorkOrderResult.setQuestionDesc(safeCenterAppealRecord.getAppealContent());
        appearWorkOrderResult.setAppearQuestions(StringUtils.isBlank(safeCenterAppealRecord.getAppearQuestions()) ? new ArrayList<>() :
                Arrays.asList(safeCenterAppealRecord.getAppearQuestions().split(",")));
        return UiResult.ok(appearWorkOrderResult);
    }

    private void saveAppearWorkOrder(AppealOrderRequest request,
                                     WordOrderRes wordOrderRes,
                                     CarOrderDetail carOrderDetail, WorkOrderEnum workOrderEnum) {
        SafeCenterAppealRecord safeCenterAlter = new SafeCenterAppealRecord();
        safeCenterAlter.setMemberId(request.getMemberId());
        safeCenterAlter.setCreateTime(new Date());
        safeCenterAlter.setUpdateTime(new Date());
        safeCenterAlter.setAppealTime(new Date());
        safeCenterAlter.setShowFlag(0);
        safeCenterAlter.setAppearContactPhone(request.getAppearContactPhone());
        safeCenterAlter.setAppearQuestions(CollUtil.isEmpty(request.getAppearQuestions()) ? StringUtils.EMPTY :
                Joiner.on(",").skipNulls().join(request.getAppearQuestions()));
        safeCenterAlter.setAppealType(workOrderEnum.getType());
        safeCenterAlter.setAppealContent(request.getQuestionDesc());
        safeCenterAlter.setAppealImg(CollUtil.isEmpty(request.getDescImage()) ? "" :
                Joiner.on(",").skipNulls().join(request.getDescImage()));
        safeCenterAlter.setAppealResults(wordOrderRes == null || !wordOrderRes.getCode().equals("10000") ? 0 : 1);
        safeCenterAlter.setCurrentCarNo(request.getCurrentCarNo());
        safeCenterAlter.setOriginalCarNo(carOrderDetail.getCarInfo().getCarNo());
        safeCenterAlter.setUnionId(request.getUnionId());
        safeCenterAlter.setOrderId(request.getOrderId());
        safeCenterAlter.setWorkOrderNo(wordOrderRes == null
                || wordOrderRes.getData() == null ? "" : wordOrderRes.getData().getOrderId());
        safeCenterAppealRecordMapper.insert(safeCenterAlter);
    }

    @Override
    public UiResult saveItemMissingAppear(AppealOrderRequest request) throws BizException {
        if (!checkWorkOrderBasicParam(request.getOrderId(), request.getMemberId())) {
            return UiResultWrapper.fail(-1, "安全中心反馈错误-参数缺失");
        }
        List<SafeCenterAppealRecord> safeCenterFeedbacks = getSafeCenterAppealRecord(request);
        if (CollUtil.isNotEmpty(safeCenterFeedbacks) &&
                safeCenterFeedbacks.stream().findFirst().get().getAppealResults() == 1) {

            return UiResultWrapper.fail(500, "请勿重复反馈");
        }
        CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(request.getOrderId());
        if (carOrderDetail == null) {
            return UiResultWrapper.fail(500, "订单不存在");
        }
        WordOrderRes wordOrderRes = submitWorkOrder(request, carOrderDetail);
        if (CollUtil.isNotEmpty(safeCenterFeedbacks)) {
            SafeCenterAppealRecord safeCenterAppealRecord = safeCenterFeedbacks.stream().findFirst().get();
            safeCenterAppealRecord.setAppealTime(new Date());
            safeCenterAppealRecord.setCurrentCarNo(request.getCurrentCarNo());
            safeCenterAppealRecord.setAppealContent(request.getQuestionDesc());
            safeCenterAppealRecord.setAppealImg(CollUtil.isEmpty(request.getDescImage()) ? "" :
                    Joiner.on(",").skipNulls().join(request.getDescImage()));
            safeCenterAppealRecord.setAppealResults(wordOrderRes == null || !wordOrderRes.getCode().equals("10000") ? 0 : 1);
            safeCenterAppealRecord.setOriginalCarNo(carOrderDetail.getCarInfo().getCarNo());
            safeCenterAppealRecord.setWorkOrderNo(wordOrderRes == null
                    || wordOrderRes.getData() == null ? "" : wordOrderRes.getData().getOrderId());
            safeCenterAppealRecordMapper.updateById(safeCenterAppealRecord);
            return UiResult.ok();
        }
        saveAppearWorkOrder(request, wordOrderRes, carOrderDetail, WorkOrderEnum.ITEM_MISSING);
        return UiResult.ok();
    }

    private List<SafeCenterAppealRecord> getSafeCenterAppealRecord(AppealOrderRequest request) {
        if (StringUtils.isBlank(request.getOrderId()) || StringUtils.isBlank(request.getMemberId())) {
            return new ArrayList<>();
        }
        QueryWrapper<SafeCenterAppealRecord> query = new QueryWrapper<SafeCenterAppealRecord>()
                .eq("order_id", request.getOrderId())
                .eq("appeal_type", request.getWorkOrderType().getType())
                .eq("member_id", request.getMemberId());
        List<SafeCenterAppealRecord> safeCenterAppealRecords = safeCenterAppealRecordMapper.selectList(query);
        return safeCenterAppealRecords;
    }

    private WordOrderRes submitWorkOrder(AppealOrderRequest request, CarOrderDetail carOrderDetail) throws BizException {
        switch (request.getWorkOrderType()) {
            case SAFETY_POLICE:
                //调用工单
                return workOrderClient.saveWorkOrder("255",
                        carOrderDetail.getBaseInfo().getContactName(), carOrderDetail.getMemberId(),
                        getAlertWorkOrder(carOrderDetail), request.getOrderId());
            case YC_CAR_NUMBER_NOT_MATCH:
                //人车不符工单
                return workOrderClient.saveWorkOrder("270",
                        carOrderDetail.getBaseInfo().getContactName(), carOrderDetail.getMemberId(),
                        this.buildYCCarNumberNotMatchParams(request, carOrderDetail), request.getOrderId());
            case FEE_APPEAL:
                //费用申诉工单
                return workOrderClient.saveWorkOrder("270",
                        carOrderDetail.getBaseInfo().getContactName(), carOrderDetail.getMemberId(),
                        this.buildFeeAppearParams(request, carOrderDetail), request.getOrderId());
            case ITEM_MISSING:
                //物品遗失工单
                return workOrderClient.saveWorkOrder("267",
                        carOrderDetail.getBaseInfo().getContactName(), carOrderDetail.getMemberId(),
                        this.buildItemMissingParams(request, carOrderDetail), request.getOrderId());
        }
        return null;
    }

    private boolean checkWorkOrderBasicParam(String orderId, String memberId) {
        if (StringUtils.isBlank(orderId) || StringUtils.isBlank(memberId)) {
            return false;
        }
        return true;
    }

    /**
     * 人车不符工单反馈参数构造
     *
     * @param request
     * @param orderInfo
     * @return
     */
    public List<FormItems> buildYCCarNumberNotMatchParams(AppealOrderRequest request, CarOrderDetail orderInfo) {
        List<FormItems> formItemList = new ArrayList<>();
        formItemList.add(FormItems.builder().ctrlCode("OrderId").value(orderInfo.getOrderId()).build());
        formItemList.add(FormItems.builder().ctrlCode("PhoneNumber").value(orderInfo.getBaseInfo().getContactPhone()).build());
        formItemList.add(FormItems.builder().ctrlCode("CustomerName").value(StringUtils.defaultIfBlank(orderInfo.getBaseInfo().getContactName(),"同程用户")).build());
        formItemList.add(FormItems.builder().ctrlCode("ProblemDescription").value(request.getQuestionDesc()).build());
        formItemList.add(FormItems.builder().ctrlCode("QuestionType").valueCode("rcbf").build());
        if (CollUtil.isNotEmpty(request.getDescImage())) {
            formItemList.add(FormItems.builder().ctrlCode("Pics").value(Joiner.on(",").skipNulls().join(request.getDescImage())).build());
        }
        return formItemList;
    }

    /**
     * 110告警工单参数构造
     *
     * @param orderInfo
     * @return
     */
    private List<FormItems> getAlertWorkOrder(CarOrderDetail orderInfo) {
        List<FormItems> formItemList = new ArrayList<>();
        formItemList.add(FormItems.builder().ctrlCode("orderId").value(orderInfo.getOrderId()).build());
        formItemList.add(FormItems.builder().ctrlCode("level").valueCode("hurry").build());

        return formItemList;
    }


    private List<FormItems> buildFeeAppearParams(AppealOrderRequest request, CarOrderDetail carOrderDetail) {
        List<FormItems> formItemList = new ArrayList<>();
        //todo 主逻辑还未实现
        return formItemList;
    }

    private List<FormItems> buildItemMissingParams(AppealOrderRequest request, CarOrderDetail carOrderDetail) {
        List<FormItems> formItemList = new ArrayList<>();
        formItemList.add(FormItems.builder().ctrlCode("orderId").value(request.getOrderId()).build());
        formItemList.add(FormItems.builder().ctrlCode("phone").value(request.getAppearContactPhone()).build());
        formItemList.add(FormItems.builder().ctrlCode("remark").value(request.getQuestionDesc()).build());
        formItemList.add(FormItems.builder().ctrlCode("level").valueCode("hurry").build());
        return formItemList;
    }
}