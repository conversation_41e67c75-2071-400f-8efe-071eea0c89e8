package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerCustomerTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerOptionTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerStatusEnum;
import com.ly.car.risk.process.constants.RiskCustomerTtlEnum;
import com.ly.car.risk.process.controller.params.DriverSyncParams;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.controller.params.HcSyncCustomerParam;
import com.ly.car.risk.process.repo.hitchorder.entity.DriverInfo;
import com.ly.car.risk.process.repo.hitchorder.mapper.DriverInfoMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcSupplierOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.HcCustomerMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcCustomer;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskCustomerRecord;
import com.ly.car.risk.process.service.dto.CommonCustomerParam;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class RiskCustomerService {

    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private OrderDriverMapper orderDriverMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private SfcSupplierOrderMapper sfcSupplierOrderMapper;
    @Resource
    private RiskCustomerRecordMapper riskCustomerRecordMapper;
    @Resource
    private DriverInfoMapper driverInfoMapper;
    @Resource
    private HcCustomerMapper hcCustomerMapper;
    @Resource
    private CarOrderService carOrderService;

    public String invalid() {
        List<RiskCustomerManage> list = riskCustomerManageMapper.selectInvalidData();
        log.info("[RiskCustomerService][invalid][],[]需要失效的风控名单数据：{}", list.size());
        if (!CollectionUtils.isEmpty(list)) {
            int count = 0;
            for (RiskCustomerManage entity : list) {
                entity.setStatus(2);
                entity.setUpdateTime(new Date());
                entity.setInvalidTime(new Date());
                entity.setOptionType(1);
                riskCustomerManageMapper.updateById(entity);
                count++;
            }
            log.info("[RiskCustomerService][invalid][],[]需要失效的风控名单执行成功：{}", count);
        }
        return "success";
    }

    public List<RiskCustomerManage> getListByValue(FilterParams params, Date date) {
        List<RiskCustomerManage> manageList = riskCustomerManageMapper.getListByValue(params, date);
        return manageList;
    }

    public List<RiskCustomerManage> getListByValueByGroup(CommonCustomerParam params, Date date) {
        if (StringUtils.isBlank(params.getMemberId()) && StringUtils.isBlank(params.getDeviceId()) && StringUtils.isBlank(params.getDriverCardNo())
                && StringUtils.isBlank(params.getDriverId()) && StringUtils.isBlank(params.getUserPhone()) && StringUtils.isBlank(params.getInvitePhone())
                && StringUtils.isBlank(params.getMobile()) && StringUtils.isBlank(params.getPassengerCellphone()) && StringUtils.isBlank(params.getPayAccount())
                && StringUtils.isBlank(params.getUnionId()) && CollectionUtils.isEmpty(params.getDriverCardNos()) && CollectionUtils.isEmpty(params.getIdCardNos())) {
            return new ArrayList<>();
        }
        List<RiskCustomerManage> manageList = riskCustomerManageMapper.getListByValueByGroup(params, date);
        return manageList;
    }

    public static void main(String[] args) {
        String str = "{\"userPhone\":null,\"passengerCellphone\":null,\"deviceId\":null,\"memberId\":null,\"driverCardNo\":null,\"payAccount\":null,\"unionId\":\"\",\"driverId\":null,\"invitePhone\":null,\"mobile\":null,\"driverCardNos\":[\"\"]}";
        CommonCustomerParam params = JSONObject.parseObject(str, CommonCustomerParam.class);
        System.out.println(params.getDriverCardNos().size());
        System.out.println(StringUtils.isBlank(params.getMemberId()) && StringUtils.isBlank(params.getDeviceId()) && StringUtils.isBlank(params.getDriverCardNo())
                && StringUtils.isBlank(params.getDriverId()) && StringUtils.isBlank(params.getUserPhone()) && StringUtils.isBlank(params.getInvitePhone())
                && StringUtils.isBlank(params.getMobile()) && StringUtils.isBlank(params.getPassengerCellphone()) && StringUtils.isBlank(params.getPayAccount())
                && StringUtils.isBlank(params.getUnionId()) && params.getDriverCardNos().isEmpty());
    }

    public String syncDriver(DriverSyncParams params) {
        log.info("[RiskCustomerService][syncDriver][][]系统同步黑名单:{}", JsonUtils.json(params));
        //黑名单司机同步的话目前这样设计，如是没有司机，则直接插入，如是数据库已有，则查询当前是否失效，失效则插入，未失效则延长失效时间
        RiskCustomerManage riskCustomerManage = riskCustomerManageMapper.selectOne(
                new QueryWrapper<RiskCustomerManage>()
                        .eq("customer_value", params.getDriverCardNo())
                        .eq("customer_type", 6)
                        .eq("risk_type", 1)
        );
        if (riskCustomerManage == null && StringUtils.isNotBlank(params.getFlag()) && params.getFlag().equals("update")) {
            return "success";
        }
        //插入的时候需要记录
        RiskCustomerRecord record = new RiskCustomerRecord();
        if (riskCustomerManage == null || riskCustomerManage.getInvalidTime().before(new Date())) {
            //没有就直接插入
            RiskCustomerManage manage = new RiskCustomerManage();
            manage.setRiskType(1);
            manage.setCustomerType(6);
            manage.setCustomerValue(params.getDriverCardNo());
            manage.setStatus(1);
            manage.setTtl(params.getTtl());
            manage.setOptionType(params.getOptionType());
            manage.setCreateUser(StringUtils.isNotBlank(params.getOptionName()) ? params.getOptionName() : "系统风控");
            manage.setOptionName(StringUtils.isNotBlank(params.getOptionName()) ? params.getOptionName() : "系统风控");
            manage.setCreateUser(StringUtils.isNotBlank(params.getOptionName()) ? params.getOptionName() : "系统风控");
            manage.setRiskRemark(params.getRiskRemark());
            manage.setCreateTime(new Date());
            manage.setUpdateTime(new Date());
            manage.setInvalidTime(DateUtil.string2Date(params.getInvalidTime()));
            manage.setBindUser("");
            manage.setSupplierName(StringUtils.defaultIfBlank(params.getSupplierName(), StringUtils.EMPTY));
            manage.setMemberId(StringUtils.defaultIfBlank(params.getMemberId(), StringUtils.EMPTY));
            //顺风车的去看下有没有driverId
            if (StringUtils.isNotBlank(params.getOrderId())) {
                // 新订单
                if (OrderUtils.isNewOrder(params.getOrderId())) {
                    CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(params.getOrderId());

                    if (null != carOrderDetail && null != carOrderDetail.getCarInfo()) {
                        riskCustomerManage.setDriverId(carOrderDetail.getCarInfo().getDriverCode());
                    }

                } else if (params.getOrderId().startsWith("SFC")) {
                    // 旧订单
                    SfcOrder sfcOrder = this.sfcOrderMapper.queryByOrderId(params.getOrderId());

                    if (sfcOrder != null && StringUtils.isNotBlank(params.getOrderId())) {
                        SfcSupplierOrder sfcSupplierOrder = this.sfcSupplierOrderMapper.selectOne(new QueryWrapper<SfcSupplierOrder>()
                                .eq("order_id", sfcOrder.getOrderId()).eq("supplier_order_id", sfcOrder.getSupplierOrderId())
                        );
                        riskCustomerManage.setDriverId(sfcSupplierOrder.getDriverId());
                    }
                }
            }
            riskCustomerManageMapper.insert(manage);

            record.setCustomerId(manage.getId());
            record.setOperateType(1);//新增
            record.setCreateUser(manage.getCreateUser());
            record.setOperateUser(StringUtils.isNotBlank(params.getOptionName()) ? params.getOptionName() : "系统风控");
            record.setCustomerType(6);
            record.setRemark(params.getRiskRemark());
            record.setCreateTime(new Date());
            record.setCustomerValue(manage.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
            return "success";
        }

        if (riskCustomerManage != null && riskCustomerManage.getInvalidTime().after(new Date())) {
            //存在并且还没有到失效时间则更新失效时间
            riskCustomerManage.setInvalidTime(DateUtil.string2Date(params.getInvalidTime()));
            if (StringUtils.isNotBlank(params.getFlag()) && params.getFlag().equals("update")) {
                riskCustomerManage.setInvalidTime(new Date());
            }
            riskCustomerManage.setUpdateTime(new Date());
            riskCustomerManageMapper.updateById(riskCustomerManage);
            record.setCustomerId(riskCustomerManage.getId());
            record.setOperateType(1);//修改
            record.setCreateUser(riskCustomerManage.getCreateUser());
            record.setOperateUser(StringUtils.isNotBlank(params.getOptionName()) ? params.getOptionName() : "系统风控");
            record.setCustomerType(6);
            record.setRemark(params.getRiskRemark());
            record.setCreateTime(new Date());
            record.setCustomerValue(riskCustomerManage.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
            return "success";
        }
        return null;
    }

    public String syncDriverByErp(DriverSyncParams params) {
        String lockName = "lock:syncDriver:" + params.getDriverCardNo();
        RLock lock = null;
        try {
            lock = redissonClient.getLock(lockName);
            Boolean lockFlag = lock.tryLock(2, TimeUnit.SECONDS);
            if (!lockFlag) {
                log.info("[][][][]erp同步黑名单司机相同司机拉黑锁获取失败，直接返回");
                return "fail";
            }
            log.info("[RiskCustomerService][syncDriverByErp][][]erp同步黑名单:{}", JsonUtils.json(params));
            //先来查询当前这个司机在当前有效期内有没有
            List<RiskCustomerManage> manageList = riskCustomerManageMapper.selectList(
                    new QueryWrapper<RiskCustomerManage>().eq("customer_value", params.getDriverCardNo())
                            .gt("invalid_time", new Date())
            );
            String startAddress = "";
            String endAddress = "";
            Date useTime = null;
            String driverId = "";
            RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
            if (StringUtils.isNotBlank(params.getOrderId())) {
                if (OrderUtils.isNewOrder(params.getOrderId())) {
                    CarOrderDetail order = carOrderService.queryOrderDetail(params.getOrderId());
                    if (null != order) {
                        if (null != order.getOrderTrip()) {
                            startAddress = order.getOrderTrip().getDepartureAddress();
                            endAddress = order.getOrderTrip().getArrivalAddress();
                        }
                        if (null != order.getBaseInfo()) {
                            useTime = order.getBaseInfo().getGmtUsage();
                        }
                        if (null != order.getCarInfo()) {
                            driverId = order.getCarInfo().getDriverCode();
                        }
                    }
                } else {
                    if (params.getOrderId().startsWith("YNC") || params.getOrderId().startsWith("GNC")) {
                        OrderInfo orderInfo = orderInfoMapper.findByOrderId(params.getOrderId());
                        OrderAddress orderAddress = orderAddressMapper.findByOrderId(params.getOrderId());
                        if (orderAddress != null) {
                            startAddress = orderAddress.getStartAddress();
                            endAddress = orderAddress.getEndAddress();
                        }
                        if (orderInfo != null) {
                            useTime = orderInfo.getUseTime();
                        }
                    } else {
                        SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(params.getOrderId());
                        OrderAddress orderAddress = orderAddressMapper.findByOrderId(params.getOrderId());
                        List<SfcSupplierOrder> sfcSupplierOrderList = sfcSupplierOrderMapper.selectList(new QueryWrapper<SfcSupplierOrder>()
                                .eq("order_id", sfcOrder.getOrderId()).eq("supplier_order_id", sfcOrder.getSupplierOrderId()).orderByDesc("created")
                        );
                        if (orderAddress != null) {
                            startAddress = orderAddress.getStartAddress();
                            endAddress = orderAddress.getEndAddress();
                        }
                        if (sfcOrder != null) {
                            useTime = sfcOrder.getUseTime();
                        }
                        if (!CollectionUtils.isEmpty(sfcSupplierOrderList)) {
                            driverId = sfcSupplierOrderList.get(0).getDriverId();
                        }
                    }
                }
            }

            riskCustomerManage.setRiskType(params.getCustomerType() == 0 ? RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode() : 1);
            riskCustomerManage.setCustomerType(RiskCustomerCustomerTypeEnum.car_number.getCode());
            riskCustomerManage.setCustomerValue(params.getDriverCardNo());
            riskCustomerManage.setStatus(RiskCustomerStatusEnum.valid.getCode());
            riskCustomerManage.setTtl(params.getCustomerType() == 0 ? RiskCustomerTtlEnum.one_year.getCode() : RiskCustomerTtlEnum.forever.getCode());
            riskCustomerManage.setOptionType(RiskCustomerOptionTypeEnum.user_server.getCode());
            riskCustomerManage.setOptionName(params.getOptionName());
            riskCustomerManage.setRiskRemark(params.getRiskRemark());
            riskCustomerManage.setCreateTime(new Date());
            riskCustomerManage.setUpdateTime(new Date());
            riskCustomerManage.setInvalidTime(params.getCustomerType() == 0 ? DateUtil.addMonth(new Date(), 12) : DateUtil.string2Date("2099-12-31 23:59:59"));
            riskCustomerManage.setBindUser(params.getBindUser());//手机号
            riskCustomerManage.setBindOrder(params.getOrderId());
            riskCustomerManage.setBlackType(0);
            riskCustomerManage.setBlackTypeName(params.getShieldingType());
            riskCustomerManage.setBlackChildType(params.getShieldingTypeChild());
            riskCustomerManage.setDriverName(params.getDriverName());
            riskCustomerManage.setStartAddress(startAddress);
            riskCustomerManage.setEndAddress(endAddress);
            riskCustomerManage.setUseTime(DateUtil.date2String(useTime));
            riskCustomerManage.setDriverId(driverId);
            riskCustomerManage.setCreateUser(params.getOptionName());
            if (CollectionUtils.isEmpty(manageList)) {
                //直接插入 名单类型 1-黑名单 2-白名单 3-禁止领券名单 4-禁止奖励名单 5-禁止派单名单 6-禁止接单名单
                riskCustomerManageMapper.insert(riskCustomerManage);
                RiskCustomerRecord record = new RiskCustomerRecord();
                record.setCustomerId(riskCustomerManage.getId());
                record.setOperateType(1);//新增
                record.setCreateUser(riskCustomerManage.getCreateUser());
                record.setOperateUser(StringUtils.isNotBlank(params.getOptionName()) ? params.getOptionName() : "系统风控");
                record.setCustomerType(RiskCustomerCustomerTypeEnum.car_number.getCode());
                record.setRemark(params.getRiskRemark());
                record.setCreateTime(new Date());
                record.setCustomerValue(riskCustomerManage.getCustomerValue());
                riskCustomerRecordMapper.insert(record);
                return "success";
            }
            //当前名单有效期内且是全部拉黑的
            if (params.getCustomerType() == 1) {
                RiskCustomerManage allBlackManage = manageList.stream().filter(e -> e.getRiskType() == 1 && StringUtils.isBlank(e.getBindUser()))
                        .findFirst().orElse(null);
                if (allBlackManage != null && allBlackManage.getTtl() != -1) {
                    //不是永久黑名单才更新
                    allBlackManage.setTtl(riskCustomerManage.getTtl());
                    allBlackManage.setUpdateTime(new Date());
                    allBlackManage.setInvalidTime(riskCustomerManage.getInvalidTime());
                    riskCustomerManageMapper.updateById(allBlackManage);

                    RiskCustomerRecord record = new RiskCustomerRecord();
                    record.setCustomerId(riskCustomerManage.getId());
                    record.setOperateType(1);//新增
                    record.setCreateUser(riskCustomerManage.getCreateUser());
                    record.setCreateUser(params.getOptionName());
                    record.setOperateUser(params.getOptionName());
                    record.setCustomerType(params.getCustomerType() == 0 ? RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode() : 1);
                    record.setRemark(params.getRiskRemark());
                    record.setCreateTime(new Date());
                    record.setCustomerValue(riskCustomerManage.getCustomerValue());
                    riskCustomerRecordMapper.insert(record);
                    return "success";
                } else {
                    //这里的意思是一对一的直接插入
                    riskCustomerManageMapper.insert(riskCustomerManage);
                    RiskCustomerRecord record = new RiskCustomerRecord();
                    record.setCustomerId(riskCustomerManage.getId());
                    record.setOperateType(1);//新增
                    record.setCreateUser(riskCustomerManage.getCreateUser());
                    record.setOperateUser(StringUtils.isNotBlank(params.getOptionName()) ? params.getOptionName() : "系统风控");
                    record.setCustomerType(6);
                    record.setRemark(params.getRiskRemark());
                    record.setCreateTime(new Date());
                    record.setCustomerValue(riskCustomerManage.getCustomerValue());
                    riskCustomerRecordMapper.insert(record);
                    return "success";
                }
            }
            log.info("[][][][]erp同步黑名单司机查询当前已有司机:{}", JsonUtils.json(manageList));
            //下面是1对1，其实一对一只有插入，更新的话
            RiskCustomerManage allBlackManage = manageList.stream().filter(e -> e.getRiskType() == 7 && StringUtils.isNotBlank(e.getBindUser()))
                    .filter(e -> e.getBindUser().equals(params.getBindUser())).findFirst().orElse(null);
            if (allBlackManage == null) {
                riskCustomerManageMapper.insert(riskCustomerManage);
                RiskCustomerRecord record = new RiskCustomerRecord();
                record.setCustomerId(riskCustomerManage.getId());
                record.setOperateType(1);//修改
                record.setCreateUser(riskCustomerManage.getCreateUser());
                record.setOperateUser(params.getOptionName());
                record.setCustomerType(params.getCustomerType() == 0 ? RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode() : 1);
                record.setRemark(params.getRiskRemark());
                record.setCreateTime(new Date());
                record.setCustomerValue(riskCustomerManage.getCustomerValue());
                riskCustomerRecordMapper.insert(record);
                return "success";
            } else {
                //不是永久黑名单才更新
                allBlackManage.setTtl(riskCustomerManage.getTtl());
                allBlackManage.setUpdateTime(new Date());
                allBlackManage.setInvalidTime(riskCustomerManage.getInvalidTime());
                riskCustomerManageMapper.updateById(allBlackManage);

                RiskCustomerRecord record = new RiskCustomerRecord();
                record.setCustomerId(riskCustomerManage.getId());
                record.setOperateType(1);//修改
                record.setCreateUser(riskCustomerManage.getCreateUser());
                record.setOperateUser(params.getOptionName());
                record.setCustomerType(params.getCustomerType() == 0 ? RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode() : 1);
                record.setRemark(params.getRiskRemark());
                record.setCreateTime(new Date());
                record.setCustomerValue(riskCustomerManage.getCustomerValue());
                riskCustomerRecordMapper.insert(record);
                return "success";
            }
        } catch (Exception e) {
            log.error("[][][][]erp同步黑名单司机相同司机拉黑锁获取报错{}", e);
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
        return "success";
    }

    public String syncHcDriver(HcSyncCustomerParam params) {
        log.info("[RiskCustomerService][syncHcDriver][][]汇川同步黑名单:{}", JsonUtils.json(params));
        //反查下汇川库
        DriverInfo driverInfo = this.driverInfoMapper.selectOne(new QueryWrapper<DriverInfo>()
                .eq("member_id", params.getDriverId())
        );
        if (driverInfo == null) {
            return "success";
        }
        HcCustomer customer = hcCustomerMapper.selectOne(new QueryWrapper<HcCustomer>()
                .eq(StringUtils.isNotBlank(driverInfo.getCarNumber()), "driver_card_no", driverInfo.getCarNumber())
                .eq(StringUtils.isNotBlank(driverInfo.getRegisterPhone()), "driver_phone", driverInfo.getRegisterPhone())
                .eq("customer_type", 1)
                .gt("invalid_time", new Date())
                .last("limit 1")
        );
        if (params.getFlag().equals("insert") && customer == null) {
            HcCustomer hcCustomer = new HcCustomer();
            hcCustomer.setDriverId(params.getDriverId());
            hcCustomer.setIdCardNo(driverInfo.getIdCardNo());
            hcCustomer.setDriverCardNo(driverInfo.getCarNumber());
            hcCustomer.setDriverPhone(driverInfo.getRegisterPhone());
            hcCustomer.setCreateTime(new Date());
            hcCustomer.setCreateUser("卡罗拉同步");
            hcCustomer.setUpdateTime(new Date());
            hcCustomer.setUpdateUser("卡罗拉同步");
            hcCustomer.setCustomerType(1);
//            hcCustomer.setOrderId(params.getOrderId());
            hcCustomer.setTtl(7);
            hcCustomer.setRemark(params.getRiskRemark());
            hcCustomer.setUnRemark("");
            hcCustomer.setInvalidTime(DateUtil.string2Date("2099-12-31 0:00:00"));
            this.hcCustomerMapper.insert(hcCustomer);
        } else if (params.getFlag().equals("insert") && customer != null) {
            customer.setInvalidTime(DateUtil.string2Date("2099-12-31 0:00:00"));
            customer.setUpdateUser("卡罗拉同步");
            customer.setUpdateTime(new Date());
            this.hcCustomerMapper.updateById(customer);
        } else if (customer != null) {
            //解除黑名单
            customer.setInvalidTime(new Date());
            customer.setUpdateUser("卡罗拉同步");
            customer.setUpdateTime(new Date());
            this.hcCustomerMapper.updateById(customer);
        } else {

        }
        return "success";
    }

    public String initCustomer() {
        List<RiskCustomerManage> manageList = riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>().gt("id", 400)
        );
        for (RiskCustomerManage manage : manageList) {
            if (manage.getTtl() == -1) {
                manage.setInvalidTime(DateUtil.string2Date("2099-12-31 23:59:59"));
                riskCustomerManageMapper.updateById(manage);
            }
        }
        return "success";
    }

    /**
     * 升级一对一黑名单司机，每天凌晨1点
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void upgradeDriver() {
        log.info("[RiskCustomerService][upgradeDriver][][]开始升级黑名单司机");
        Date date = new Date();
        List<RiskCustomerManage> manageList = riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>().gt("invalid_time", date)
                        .eq("risk_type", 7)
        );
        Map<String, List<RiskCustomerManage>> collect = manageList.stream().collect(Collectors.groupingBy(RiskCustomerManage::getCustomerValue));
        for (Map.Entry<String, List<RiskCustomerManage>> entry : collect.entrySet()) {
            if (entry.getValue().size() >= getUpgradeDriverConfig()) {
                DriverSyncParams params = new DriverSyncParams();
                params.setDriverCardNo(entry.getKey());
                params.setInvalidTime("2099-12-31 23:59:59");
                params.setOptionName("job司机升级");
                params.setOptionType(RiskCustomerOptionTypeEnum.system.getCode());
                params.setRiskRemark("多次一对一升级拉黑");
                params.setTtl(-1);
                params.setCustomerType(1);
                params.setOrderId(entry.getValue().get(0).getBindOrder());
                syncDriver(params);
                //同时失效当前一对一
                for (RiskCustomerManage manage : entry.getValue()) {
                    manage.setInvalidTime(new Date());
                    manage.setUpdateTime(new Date());
                    riskCustomerManageMapper.updateById(manage);
                    RiskCustomerRecord record = new RiskCustomerRecord();
                    record.setCustomerId(manage.getId());
                    record.setOperateType(1);//修改
                    record.setCreateUser(manage.getCreateUser());
                    record.setOperateUser("job司机升级");
                    record.setCustomerType(params.getCustomerType() == 0 ? RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode() : 1);
                    record.setRemark(params.getRiskRemark());
                    record.setCreateTime(new Date());
                    riskCustomerRecordMapper.insert(record);
                }
            }
        }
    }

    public Integer getUpgradeDriverConfig() {
        try {
            String configNumber = ConfigCenterClient.get("upgrade_driver_one");
            return Integer.valueOf(configNumber);
        } catch (Exception e) {
            log.error("获取升级司机黑名单打开错误:", e);
        }
        return null;
    }

    public void syncBanRegister(String driverId, String blackChildType) {
        log.info("[][][][]风控同步汇川司机认证-禁止认证:{}", driverId);
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        riskCustomerManage.setRiskType(RiskCustomerRiskTypeEnum.ban_register_list.getCode());
        riskCustomerManage.setCustomerType(RiskCustomerCustomerTypeEnum.hc_member_id.getCode());
        riskCustomerManage.setCustomerValue(driverId);
        riskCustomerManage.setStatus(RiskCustomerStatusEnum.valid.getCode());
        riskCustomerManage.setTtl(RiskCustomerTtlEnum.forever.getCode());
        riskCustomerManage.setOptionType(RiskCustomerOptionTypeEnum.system.getCode());
        riskCustomerManage.setOptionName("注册风控");
        riskCustomerManage.setRiskRemark("");
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        riskCustomerManage.setInvalidTime(DateUtil.string2Date("2099-12-31 23:59:59"));
        riskCustomerManage.setBlackType(0);
        riskCustomerManage.setBlackChildType(blackChildType);
        riskCustomerManageMapper.insert(riskCustomerManage);
    }

    //规则动作拉黑
    public void addRiskCustomer(String customerValue, Integer riskType, Integer customerType, Integer ttl) {
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        riskCustomerManage.setRiskType(riskType);
        riskCustomerManage.setCustomerType(customerType);
        riskCustomerManage.setCustomerValue(customerValue);
        riskCustomerManage.setStatus(1);
        riskCustomerManage.setTtl(ttl);
        riskCustomerManage.setOptionType(1);
        riskCustomerManage.setCreateUser("规则拉黑");
        riskCustomerManage.setOptionName("规则拉黑");
        riskCustomerManage.setRiskRemark("");
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        Date invalidTime = new Date();
        if (ttl == -1) {
            invalidTime = DateUtil.string2Date("2099-12-31 23:59:59");
        } else {
            invalidTime = DateUtil.addDay(new Date(), ttl);
        }
        riskCustomerManage.setInvalidTime(invalidTime);
        riskCustomerManageMapper.insert(riskCustomerManage);

        RiskCustomerRecord record = new RiskCustomerRecord();
        record.setCustomerId(riskCustomerManage.getId());
        record.setOperateType(1);//新增
        record.setCreateUser(riskCustomerManage.getCreateUser());
        record.setOperateUser(riskCustomerManage.getCreateUser());
        record.setCustomerType(6);
        record.setRemark("");
        record.setCreateTime(new Date());
        riskCustomerRecordMapper.insert(record);
    }

    public void addRiskCustomerOne(String customerValue, Integer riskType, Integer customerType, Integer ttl, String passengerPhone, String orderId) {
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        riskCustomerManage.setRiskType(riskType);
        riskCustomerManage.setCustomerType(customerType);
        riskCustomerManage.setCustomerValue(customerValue);
        riskCustomerManage.setStatus(1);
        riskCustomerManage.setTtl(ttl);
        riskCustomerManage.setOptionType(1);
        riskCustomerManage.setCreateUser("规则拉黑");
        riskCustomerManage.setOptionName("规则拉黑");
        riskCustomerManage.setRiskRemark("");
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        riskCustomerManage.setBindUser(passengerPhone);
        riskCustomerManage.setBindOrder(orderId);
        Date invalidTime = new Date();
        if (ttl == -1) {
            invalidTime = DateUtil.string2Date("2099-12-31 23:59:59");
        } else {
            invalidTime = DateUtil.addDay(new Date(), ttl);
        }
        riskCustomerManage.setInvalidTime(invalidTime);
        riskCustomerManageMapper.insert(riskCustomerManage);
    }


    public List<RiskCustomerManage> validAllRiskCarNoBySupplier(String supplierName) {
        List<RiskCustomerManage> riskCustomers = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .eq("supplier_name", supplierName)
                .eq("status", 1)
                .eq("customer_type", 6)
                .gt("invalid_time", new Date())
                .in("risk_type", 1, 7)
        );
        return riskCustomers;
    }

    public List<RiskCustomerManage> validIncrRiskCarNoBySupplier(String supplierName) {
        // 实际推送时间 2点15、12点15、18点15
        // 时间点获取的数据不一样，如果是 凌晨0点-上午10点推送的，取当前时间前12小时内的
        // 如果是11点-15点，取当前时间前14小时的
        // 如果是16点-24点，取当前时间前10小时的
        LocalDateTime localDateTime = LocalDateTime.now();
        int hour = localDateTime.getHour();
        if (hour >= 0 && hour <= 10) {
            localDateTime = localDateTime.minusHours(12);
        } else if (hour >= 11 && hour <= 15) {
            localDateTime = localDateTime.minusHours(14);
        } else {
            localDateTime = localDateTime.minusHours(10);
        }
        Date startTime = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        List<RiskCustomerManage> riskCustomers = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .eq("supplier_name", supplierName)
                .eq("customer_type", 6)
                .between("update_time", startTime, new Date())
                .in("risk_type", 1, 7)
        );
        return riskCustomers;

    }

    public List<RiskCustomerManage> queryAllValidRiskRecord(int offset, int limit) {
        List<RiskCustomerManage> riskCustomers = riskCustomerManageMapper.queryAllValidRiskRecord(offset,limit);
        return riskCustomers;
    }
}
