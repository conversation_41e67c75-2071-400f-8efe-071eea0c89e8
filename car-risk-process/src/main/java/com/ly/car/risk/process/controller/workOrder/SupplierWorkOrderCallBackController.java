package com.ly.car.risk.process.controller.workOrder;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.supplier.SupplierClient;
import com.ly.car.risk.process.supplier.SupplierClientStrategy;
import com.ly.car.risk.process.supplier.SupplierYueYueClientAbstract;
import com.ly.car.risk.process.supplier.yueyue.YueYueDisposeRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/supplier/callback")
public class SupplierWorkOrderCallBackController {

    @Resource
    private SupplierYueYueClientAbstract supplierYueYueClientAbstract;

    /**
     * 下发的结果通知也是要回调
     * */
    @RequestMapping("disposeCallback")
    public UiResult disposeCallback(@RequestBody JSONObject jsonObject){
        supplierYueYueClientAbstract.syncDisposeResult(jsonObject);
        return UiResult.ok();
    }

    /**
     * 供应商申诉接口
     */
    @RequestMapping("syncAppeal")
    public UiResult syncAppeal(@RequestBody JSONObject jsonObject){
        String tcAppealId = supplierYueYueClientAbstract.syncAppeal(jsonObject);
        return UiResult.ok(tcAppealId);
    }

    /**
     * 我们自己的erp审核，是否让供应商的申诉通过
     * */
    @RequestMapping("adminAudit")
    public UiResult adminAudit(@RequestBody JSONObject jsonObject){
        supplierYueYueClientAbstract.syncAppealResult(jsonObject);
        return UiResult.ok();
    }








}
