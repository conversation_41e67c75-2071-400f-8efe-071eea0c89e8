package com.ly.car.risk.process.service.riskMetrics.impl;

import com.ly.car.risk.process.repo.riskmetrics.entity.CarOfflineRiskOrderDetail;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderDetailMapper;
import com.ly.car.risk.process.service.riskMetrics.OfflineRiskMetricsService;
import com.ly.car.risk.process.strategy.OfflineRiskStrategyHandler;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service(value = "offlineRiskMetricsService")
public class OfflineRiskMetricsServiceImpl implements OfflineRiskMetricsService {
    
    @Resource
    private OfflineRiskStrategyHandler offlineRiskStrategyHandler;
    
    @Resource
    private CarRiskOrderDetailMapper orderDetailMapper;
    
    /**
     * 当前订单实际金额＞预估金额x%以上
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverRealMoneyExceedRate(List<String> productLineList, Double rate, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverRealMoneyExceedRate(productLineList, rate, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单实际金额与预估金额差值金额大于n元
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverRealMoneyExceedAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverRealMoneyExceedAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单附加费金额＞预估金额x%
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverSurchargeExceedRate(List<String> productLineList, Double rate, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverSurchargeExceedRate(productLineList, rate, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前用户完单订单实际里程与预估里程差X公里左右
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverRealDistanceExceedKm(List<String> productLineList, Double distance, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverRealDistanceExceedKm(productLineList, distance, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前用户完单订单实际时长与预估时长差前后X分钟
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverRealTimeExceedMinute(List<String> productLineList, Double minute, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverRealTimeExceedMinute(productLineList, minute, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前用户完单订单待付补款需支付金额大于X元
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverSupplementaryExceedAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverSupplementaryExceedAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前用户完单订单附加费金额与实际里程金额差值大于X元
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverSurchargeRealExceedAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverSurchargeRealExceedAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单实际里程小于X公里
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverDistanceLessKm(List<String> productLineList, Double distance, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverDistanceLessKm(productLineList, distance, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单实际完成时长小于X分钟
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverTimeLessMinute(List<String> productLineList, Double minute, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverTimeLessMinute(productLineList, minute, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单预估时长大于X分钟
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverTimeExceedMinute(List<String> productLineList, Double minute, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverTimeExceedMinute(productLineList, minute, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前完成订单金额支付异常（包括未支付、补差未支付）
     * pay_state=0、2
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverStateExp(List<String> productLineList, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverStateExp(productLineList, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前前付订单用户下单实际支付金额小于X元
     * （支付状态=前付，订单状态=7、8）
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverScorePayLessAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverScorePayLessAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前前付订单完单后待补款金额大于X元
     * （支付状态=前付，订单状态=7、8）
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverScorePaySupplementaryAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverScorePaySupplementaryAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单完单金额X1元＞金额＜X2元
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverRangeAmount(List<String> productLineList, Double leftAmount, Double rightAmount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverRangeAmount(productLineList, leftAmount, rightAmount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单预估金额小于X元
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverBookLessAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverBookLessAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单完单金额大于X
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverMoreAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverMoreAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单完单起点和终点一致（地址名称完全一致)
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverODSame(List<String> productLineList, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverODSame(productLineList, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前司机完单预估里程为0
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverZeroEstimateDistance(List<String> productLineList, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverZeroEstimateDistance(productLineList, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单实际金额大于预估金额X%
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverRealAmountMoreRate(List<String> productLineList, Double rate, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverRealAmountMoreRate(productLineList, rate, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单实际金额-预估金额大于X元
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverRealAmountMoreAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverRealAmountMoreAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单实际时长大于预估时长X%
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverTimeMoreRate(List<String> productLineList, Double rate, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverTimeMoreRate(productLineList, rate, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单用户二次需要支付金额大于x元
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverSecondPayMoreAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverSecondPayMoreAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单实付金额小于x元
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverLessAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverLessAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单应付金额大于x元
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverShouldPayMoreAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverShouldPayMoreAmount(productLineList, amount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单实付金额大于等于X1元且小于X2元
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverRealRangeAmount(List<String> productLineList, Double leftAmount, Double rightAmount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverRealRangeAmount(productLineList, leftAmount, rightAmount, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
    
    /**
     * 当前订单实际完成时长小于X分钟
     */
    @Override
    public List<CarOfflineRiskOrderDetail> driverTimeHasLessMinute(List<String> productLineList, Double minute, String startTime, String endTime, List<String> channels, List<String> supplierCodeList) {
        List<CarOfflineRiskOrderDetail> orderList = orderDetailMapper.driverTimeHasLessMinute(productLineList, minute, startTime, endTime, channels, supplierCodeList);
        return orderList;
    }
}