package com.ly.car.risk.process.service.core;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.repo.risk.mapper.RiskSceneMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskEngineRule;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskScene;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RiskSceneCache {

    private static final String KEY = "risk_scene_";

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @Resource
    private RiskSceneMapper riskSceneMapper;

    @Scheduled(fixedRate = 1000L * 60L,initialDelay = 0)
    public void init(){
        List<RiskScene> list = this.riskSceneMapper.selectList(new QueryWrapper<RiskScene>()
                .eq("is_deleted", 0)
        );
        for(RiskScene scene : list){
            redissonClient.getBucket(KEY+scene.getGuid()).set(JSONObject.toJSONString(scene),5,TimeUnit.MINUTES);
        }
        //对场景进行区分存储
        list = list.stream().filter(data-> StringUtils.isNotBlank(data.getSceneNo())).collect(Collectors.toList());
        for(RiskScene scene : list){
            redissonClient.getBucket(KEY+scene.getSceneNo()).set(JSONObject.toJSONString(scene),5,TimeUnit.MINUTES);
        }

    }

    public RiskScene loadScene(String key){
        RBucket<String> riskSceneRBucket = redissonClient.getBucket(KEY + key);
        if(riskSceneRBucket == null){
            return null;
        }
        return JSONObject.parseObject(riskSceneRBucket.get(),RiskScene.class);
    }

}
