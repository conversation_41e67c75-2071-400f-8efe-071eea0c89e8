package com.ly.car.risk.process.service.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderRiskContext {

    private String orderId;
    private String finishTime;
    private String memberId;
    private String unionId;
    private String driverCardNo;
    private BigDecimal startLat;
    private BigDecimal startLng;
    private BigDecimal endLat;
    private BigDecimal endLng;
    private BigDecimal totalAmount;
    private BigDecimal estimateKilo;
    private BigDecimal actualKilo;
    private Integer estimateDuration;
    private Integer actualDuration;
    //司机开始服务时间与完单时间的间隔
    private Integer intervalTime;
    private String passengerCellphone;
    //优惠券,不包含显示优惠
    private BigDecimal couponAmount;
    public String createTime;
}
