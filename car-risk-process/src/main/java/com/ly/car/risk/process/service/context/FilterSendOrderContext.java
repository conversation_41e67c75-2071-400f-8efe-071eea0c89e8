package com.ly.car.risk.process.service.context;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.service.redis.OrderStatusDTO;
import com.ly.car.risk.process.service.rule.SpecialCarRuleConfig;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class FilterSendOrderContext {

    private FilterParams params;

    private List<SendOrderContext> payAccountList = new ArrayList<>();
    private List<SendOrderContext> deviceList = new ArrayList<>();
    private List<SendOrderContext> memberList = new ArrayList<>();
    private List<SendOrderContext> driverList = new ArrayList<>();
    private List<OrderStatusDTO> orderStatusDTOList = new ArrayList<>();
    private List<RuleChain> ruleList = new ArrayList<>();
    private SpecialCarRuleConfig specialCarRuleConfig;
    private String orderId;
    private UiResult uiResult;
    private String userPhone;
    private String passengerCellphone;
    private String deviceId;
    private String memberId;
    private String driverCardNo;
    private String unionId;

    private Integer mainScene;
    private Integer childScene;
    //规则配置

    //起终点经纬度
    private String startLat;
    private String startLng;
    private String endLat;
    private String endLng;

    private String ruleNo;

    private List<OrderList> orderList;



}
