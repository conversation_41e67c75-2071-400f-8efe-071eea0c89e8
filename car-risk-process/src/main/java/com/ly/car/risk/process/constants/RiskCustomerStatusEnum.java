package com.ly.car.risk.process.constants;

import org.apache.commons.lang3.StringUtils;


public enum RiskCustomerStatusEnum {

    valid(1, "有效"),
    invalid(2, "失效"),
    del_flag(3, "标记为已删除"),
    ;
    private Integer code;
    private String msg;

    RiskCustomerStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (RiskCustomerStatusEnum enumItem : RiskCustomerStatusEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
}
