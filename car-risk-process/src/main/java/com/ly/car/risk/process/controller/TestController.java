package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.DistributionInfo;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.entity.OrderComplete;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.api.EsQueryClient;
import com.ly.car.risk.process.api.dto.CarOwnerTrackInfo;
import com.ly.car.risk.process.client.lbs.LbsClient;
import com.ly.car.risk.process.client.lbs.NavigateCondition;
import com.ly.car.risk.process.client.lbs.NavigateCostDto;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.api.TencentCloudApiClient;
import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.IdCardVerifyParam;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.controller.params.CommonParams;
import com.ly.car.risk.process.kafka.dto.PanelDataDTO;
import com.ly.car.risk.process.repo.hitchorder.entity.PassengerOrderAddress;
import com.ly.car.risk.process.repo.hitchorder.entity.PassengerOrderInfo;
import com.ly.car.risk.process.repo.hitchorder.mapper.PassengerOrderAddressMapper;
import com.ly.car.risk.process.repo.hitchorder.mapper.PassengerOrderInfoMapper;
import com.ly.car.risk.process.repo.order.mapper.BlackDriverBlacklistRecordMapper;
import com.ly.car.risk.process.repo.order.mapper.DistributionInfoMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcSupplierOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.*;
import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveWords;
import com.ly.car.risk.process.repo.risk.mapper.entity.TcSupplierWorkOrder;
import com.ly.car.risk.process.service.HcRiskOrderService;
import com.ly.car.risk.process.service.MqSendService;
import com.ly.car.risk.process.service.RiskOrderManageService;
import com.ly.car.risk.process.service.SupplierWorkOrderService;
import com.ly.car.risk.process.service.dto.DriverWarningDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.dto.order.CarPassenger;
import com.ly.car.risk.process.service.dto.order.OrderTripInfo;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.service.redis.UpdateScoreSortedSetService;
import com.ly.car.risk.process.service.sensitiveWords.SensitiveWordsService;
import com.ly.car.risk.process.service.sfc.SfcSupplierOrderService;
import com.ly.car.risk.process.task.SfcFinishOrCancelData;
import com.ly.car.risk.process.utils.AmountUtil;
import com.ly.car.risk.process.utils.GroovyScriptUtil;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.risk.process.utils.SensitiveWordUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderDriver;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.redisson.api.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.FileNotFoundException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private DistributionRiskManageMapper distributionRiskManageMapper;
    @Resource
    private RiskOrderManageService riskOrderManageService;
    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;
    @Resource
    private SaveScoredSortedSetService saveScoredSortedSetService;
    @Resource
    private OrderCompleteMapper orderCompleteMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private SfcSupplierOrderService sfcSupplierOrderService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UpdateScoreSortedSetService updateScoreSortedSetService;
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private BlackDriverBlacklistRecordMapper blackDriverBlacklistRecordMapper;
    @Resource
    private TianChuangRiskClient tianChuangRiskClient;
    @Resource
    private SensitiveWordsService sensitiveWordsService;
    @Resource
    private HcRiskOrderService hcRiskOrderService;
    @Resource
    private PassengerOrderInfoMapper passengerOrderInfoMapper;
    @Resource
    private PassengerOrderAddressMapper passengerOrderAddressMapper;
    @Resource
    private DistributionInfoMapper distributionInfoMapper;
    @Resource
    private SupplierWorkOrderService supplierWorkOrderService;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderDriverMapper orderDriverMapper;
    @Resource
    private SensitiveWordsMapper sensitiveWordsMapper;
    @Resource
    private TcSupplierWorkOrderMapper tcSupplierWorkOrderMapper;
    @Resource
    private KafkaProducer<String, String> szKafkaProducer;
    @Resource
    private MqSendService mqSendService;
    @Resource
    private TencentCloudApiClient tencentCloudApiClient;

    @Resource
    private EsQueryClient esQueryClient;

    @Resource
    private LbsClient lbsClient;

    @RequestMapping("/syncOrder")
    public String syncHistory(){

        List<DistributionRiskManage> manageList = distributionRiskManageMapper.selectList(
                new QueryWrapper<DistributionRiskManage>().between("create_time","2022-09-20 00:00:00","2022-11-20 23:59:59")
        );
        for(DistributionRiskManage manage : manageList){
            RiskOrderManage req = new RiskOrderManage();
            req.setOrderId(manage.getOrderId());
            req.setRuleNo(manage.getRuleNoList());
            this.riskOrderManageService.addRiskOrder(req);
        }
        return "success";

    }

    @RequestMapping("delRiskOrder")
    public String delRiskOrder(){
        List<Long> ids = new ArrayList<>();
        ids.add(27398L);
        ids.add(27400L);
        ids.add(27399L);
        riskOrderManageMapper.deleteBatchIds(ids);
        return "success";
    }

    @RequestMapping("getRedisScore")
    public Object getRedisScore(@RequestBody CommonParams params){
        return saveScoredSortedSetService.get(params.getStartTime());
    }

    @RequestMapping("setRedsis")
    public String setRedis(@RequestBody CommonParams params){
        return saveScoredSortedSetService.set(params.getStartTime());
    }

    @RequestMapping("getRedisList")
    public List<Object> getRedisList(@RequestBody CommonParams params){
        return updateScoreSortedSetService.getRedisList(params.getStartTime());
    }


    @RequestMapping("redisInit")
    public String initRedis(){
        Date date = DateUtil.addDay(new Date(),-3);
        List<OrderComplete> completeList = orderCompleteMapper.selectList(new QueryWrapper<OrderComplete>().gt("create_time", date));
        List<String> memberIds = completeList.stream().map(OrderComplete::getMemberId).collect(Collectors.toList());
        List<String> driverNos = completeList.stream().map(OrderComplete::getDriverCardNo).collect(Collectors.toList());
        for(String str : memberIds){
            RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet("MEMBER.sliding.window_"+str);
            scoredSortedSet.clear();
        }
        for (String str : driverNos){
            RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet("DRIVER.sliding.window_"+str);
            scoredSortedSet.clear();
        }
        for(OrderComplete order : completeList){
            SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(order.getOrderId());
            SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getByOrderId(sfcOrder.getOrderId(),sfcOrder.getSupplierOrderId());
            OrderAddress orderAddress = orderAddressMapper.findByOrderId(sfcOrder.getOrderId());
            OrderRiskContext orderRiskContext = new OrderRiskContext();
            orderRiskContext.setOrderId(sfcOrder.getOrderId());
            orderRiskContext.setFinishTime(DateUtil.date2String(sfcOrder.getFinishTime()));
            orderRiskContext.setMemberId(String.valueOf(sfcOrder.getMemberId()));
            orderRiskContext.setUnionId(sfcOrder.getUnionId());
            orderRiskContext.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
            orderRiskContext.setStartLat(orderAddress.getStartLat());
            orderRiskContext.setStartLng(orderAddress.getStartLng());
            orderRiskContext.setEndLat(orderAddress.getEndLat());
            orderRiskContext.setEndLng(orderAddress.getEndLng());
            orderRiskContext.setTotalAmount(AmountUtil.changeF2Y(sfcOrder.getTotalAmount()));
            orderRiskContext.setEstimateKilo(orderAddress.getEstimateKilo());
            orderRiskContext.setActualKilo(orderAddress.getActualKilo());
            orderRiskContext.setEstimateDuration(orderAddress.getEstimateMinute());
            orderRiskContext.setActualDuration(orderAddress.getActualMinute());
            orderRiskContext.setIntervalTime((int) TimeUnit.MILLISECONDS.toMinutes(sfcOrder.getFinishTime().getTime()-sfcSupplierOrder.getAcceptTime().getTime()));


            if(orderRiskContext.getMemberId().equals("0")){
                saveScoredSortedSetService.save(RedisKeyConstants.DRIVER_SLIDING_WINDOW+orderRiskContext.getDriverCardNo(),
                        3 * 24 * 60 * 60L,
                        orderRiskContext,
                        DateUtil.string2Date(orderRiskContext.getFinishTime()).getTime());
            } else {
                saveScoredSortedSetService.save(RedisKeyConstants.USER_SLIDING_WINDOW+orderRiskContext.getMemberId(),
                        3 * 24 * 60 * 60L,
                        orderRiskContext,
                        DateUtil.string2Date(orderRiskContext.getFinishTime()).getTime());
                saveScoredSortedSetService.save(RedisKeyConstants.DRIVER_SLIDING_WINDOW+orderRiskContext.getDriverCardNo(),
                        3 * 24 * 60 * 60L,
                        orderRiskContext,
                        DateUtil.string2Date(orderRiskContext.getFinishTime()).getTime());
            }

        }

        return "success";

    }

    @RequestMapping("getCount")
    public Long countRedis(){
        return redissonClient.getKeys().count();
    }

    @RequestMapping("dealCustomerManage")
    public void dealCustomerManage(){
        //先查询所有1v1名单
        List<RiskCustomerManage> manageList = riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>().eq("risk_type",7));

        for(RiskCustomerManage manage : manageList){
//            if(StringUtils.isNotBlank(manage.get)){
//
//            }
        }
    }


    @RequestMapping("tianchuang")
    public void tianchuang(){
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setName("杨现");
        param.setIdCard("360502195912191108");
        tianChuangRiskClient.verifyIdCard(param,null);
    }

    @RequestMapping("testRedis")
    public void testRedis(@RequestBody CommonParams params){
        redissonClient.getBitSet("").set(1232132,false);
//
//        RKeys keys = redissonClient.getKeys();
//        Iterable<String> keys1 = keys.getKeys();
//        for(String key : keys1){
//            RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
//            if(scoredSortedSet.remainTimeToLive() == -1 ){
//                //        //重新给个过期时间
//                int b=(int)(Math.random()*1000);
//                redissonClient.getKeys().expire(key, 1000+b, TimeUnit.SECONDS);
////                System.out.println(scoredSortedSet.remainTimeToLive());
//            }
//        }

        // 序列化
//        if (params.getEndTime() instanceof String) {
//            scoredSortedSet.remove(params.getEndTime());
//            scoredSortedSet.add(1995, (String) params.getEndTime());
//        }


//        //重新给个过期时间
//        redissonClient.getKeys().expire(params.getStartTime(), 1000, TimeUnit.SECONDS);

//        RMapCache<String, String> mapCache = redissonClient.getMapCache(RedisKeyConstants.ORDER_DRIVER_LOCATION_MINUTES);
//        mapCache.put("123456","123456");
        redissonClient.getBucket("YNC_1234").set("123",1,TimeUnit.DAYS);
        redissonClient.getBucket("YNC_1234").set("456",1,TimeUnit.DAYS);
    }

    @RequestMapping("initWords")
    public void initWords() throws FileNotFoundException {
        sensitiveWordsService.initWords();
    }

    @RequestMapping("syncWords")
    public void syncWords() throws FileNotFoundException {
        List<SensitiveWords> sensitiveWordsList = sensitiveWordsService.queryAllWordsList();
        for(SensitiveWords sensitiveWords : sensitiveWordsList){
            OkHttpClientUtil.getInstance().post("http://tcwireless.t.17usoft.com/car_risk_process/sync/syncSensitiveWords", JsonUtils.json(sensitiveWords),null,500l);
        }
    }

    @RequestMapping("insertWords")
    public void insertWords(@RequestBody CommonParams params){
        SensitiveWords sensitiveWords = new SensitiveWords();
        sensitiveWords.setWord(params.getStartTime());
        sensitiveWords.setWordType(5);
        sensitiveWords.setHitCount(0);
        sensitiveWords.setDeleted(0);
        sensitiveWords.setLevel(1);
        sensitiveWords.setCreateTime(new Date());
        sensitiveWords.setUpdateTime(new Date());
        this.sensitiveWordsMapper.insert(sensitiveWords);

    }

    @RequestMapping("matchWords")
    public Map<String, Long> matchWords(@RequestBody CommonParams params){

        return null;
    }

    @RequestMapping("syncHcOrder")
    public void syncHcOrder(@RequestBody CommonParams params){
//        SfcOrder sfcOrder, PassengerOrderInfo orderInfo, DistributionInfo distributionInfo,
//                PassengerOrderAddress orderAddress;
        PassengerOrderInfo orderInfo = passengerOrderInfoMapper.selectByPassengerOrderId(params.getStartTime());
        PassengerOrderAddress orderAddress = passengerOrderAddressMapper.selectByPassengerOrderId(params.getStartTime());
        SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(orderInfo.getDistributionOrderId());
        DistributionInfo distributionInfo = distributionInfoMapper.selectByRefId(sfcOrder.getRefId());
        hcRiskOrderService.syncDataBase(sfcOrder,orderInfo,distributionInfo,orderAddress);
    }

    @RequestMapping("delHc")
    public void delHc(@RequestBody CommonParams params){
        hcRiskOrderService.delHc(params.getStartTime());
    }

    @RequestMapping("delRedisKey")
    public void delRedisKey(@RequestBody CommonParams params){
        boolean delete = redissonClient.getAtomicLong(params.getStartTime()).delete();
    }


    @RequestMapping("generateWorkOrder")
    public void generateWorkOrder(@RequestBody CommonParams params){
        OrderInfo byOrderId = this.orderInfoMapper.findByOrderId(params.getOrderId());
        OrderDriver byOrderId1 = this.orderDriverMapper.findByOrderId(params.getOrderId());
        this.supplierWorkOrderService.saveWorkOrder(byOrderId,byOrderId1,"025",null);
    }

    @RequestMapping("syncWork")
    public void syncWork(@RequestBody CommonParams params){
        this.supplierWorkOrderService.sync(params.getOrderId(),true);
    }

    @RequestMapping("updateWorker")
    public void updateWorker(@RequestBody CommonParams params) {
        for(String str : params.getOrderIds()){
            OrderInfo orderInfo = this.orderInfoMapper.findBySupplerOrderId(str);
            RiskOrderManage manage = this.riskOrderManageMapper.selectOne(new QueryWrapper<RiskOrderManage>().eq("order_id",orderInfo.getOrderId()));
            manage.setStatus(2);
            this.riskOrderManageMapper.updateById(manage);
            TcSupplierWorkOrder tcSupplierWorkOrder = this.tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>().eq("order_id",orderInfo.getOrderId()));
            tcSupplierWorkOrder.setJudgeResult(params.getStatus());
            tcSupplierWorkOrder.setUpdateTime(new Date());
            this.tcSupplierWorkOrderMapper.updateById(tcSupplierWorkOrder);
        }
    }

    @RequestMapping("testKafka")
    public void sendKafka(@RequestBody CommonParams params){
        String body = params.getOrderId();
        sfcOrderMapper = SpringContextUtil.getBean("sfcOrderMapper");
        SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(body);
        if(sfcOrder.getStatus() == 1000 && sfcOrder.getDistributorFlag() == 1){
            return;
        }
        SfcFinishOrCancelData data = new SfcFinishOrCancelData();
        data.setOrderId(body);
        data.setMemberId(String.valueOf(sfcOrder.getMemberId()));
        data.setFinishTime(sfcOrder.getFinishTime());
        data.setCreateTime(sfcOrder.getCreated());
        data.setCancelTime(sfcOrder.getCancelTime());
        data.setStatus(sfcOrder.getStatus());
        orderAddressMapper = SpringContextUtil.getBean("orderAddressMapper");
        OrderAddress orderAddress = orderAddressMapper.findByOrderId(body);
        if(orderAddress != null){
            data.setEstimateKilo(orderAddress.getEstimateKilo());
            data.setCityId(orderAddress.getStartCityId());
            data.setStartLat(orderAddress.getStartLat());
            data.setStartLng(orderAddress.getStartLng());
            data.setEndLat(orderAddress.getEndLat());
            data.setEndLng(orderAddress.getEndLng());
        }
        sfcSupplierOrderService = SpringContextUtil.getBean("sfcSupplierOrderService");
        if(sfcOrder.getStatus() == 1000){
            SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getListByOrderId(body).stream()
                    .filter(supplierOrder->StringUtils.isNotBlank(supplierOrder.getPlateNumber())).findFirst().orElse(null);
            if(sfcSupplierOrder == null){
                data.setCancelType(0);
            } else {
                data.setCancelType(1);
                data.setAcceptTime(sfcSupplierOrder.getAcceptTime());
                data.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
            }
        } else {
            SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getByOrderId(body,sfcOrder.getSupplierOrderId());
            data.setAcceptTime(sfcSupplierOrder.getAcceptTime());
            data.setDriverCardNo(sfcSupplierOrder.getPlateNumber());
            data.setIntervalTime((int) TimeUnit.MILLISECONDS.toMinutes(sfcOrder.getFinishTime().getTime()-sfcSupplierOrder.getAcceptTime().getTime()));
        }
        data.setPhone7(sfcOrder.getPassengerCellphone().substring(0,7));
        data.setPassengerCellphone(sfcOrder.getPassengerCellphone());
        szKafkaProducer = SpringContextUtil.getBean("szKafkaProducer");
        ProducerRecord<String, String> record = new ProducerRecord<>("car_flink_topic_risk", JsonUtils.json(data));
        szKafkaProducer.send(record);
    }

    @RequestMapping("sendMq")
    public String sendMq(@RequestBody Map<String,String> map){
        DriverWarningDTO dto = new DriverWarningDTO(map.get("supplierOrderId"),map.get("ruleNo"));
        this.mqSendService.driverWarnNotify(JsonUtils.json(dto),null,0L);
        return "success";
    }

    @RequestMapping("getMapSize")
    public Integer getMapSize(){
       return GroovyScriptUtil.getSize();
    }

    @RequestMapping("testGroovy")
    public void testGroovy(){
        String str = "class checkRule{    public boolean check(def data) { if(data.sfc_driver_and_user_24h_finish>3&&data.sfc_driver_and_user_1h_finish>3&&data.sfc_driver_and_user_24h_left_10_finish>=3){  return true ; } else {  return false ;  }}}";
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sfc_driver_and_user_24h_finish","5");
        jsonObject.put("sfc_driver_and_user_1h_finish","5");
        jsonObject.put("sfc_driver_and_user_24h_left_10_finish","2.1");
        map.put("sfc_driver_and_user_24h_finish",jsonObject.getInteger("sfc_driver_and_user_24h_finish"));
        map.put("sfc_driver_and_user_1h_finish",jsonObject.getInteger("sfc_driver_and_user_1h_finish"));
        map.put("sfc_driver_and_user_24h_left_10_finish",jsonObject.getDouble("sfc_driver_and_user_24h_left_10_finish"));
        Object[] args = {map};
        Boolean ret = false;
        try {
            ret = (Boolean) GroovyScriptUtil.invokeMethod(str, "check", args);
            System.out.println(ret);
        } catch (Exception e) {
        }
    }

    @RequestMapping("checkMobile")
    public Integer checkMobile(@RequestBody Map<String,String> map){
        String mobile = map.get("mobile");
        return tencentCloudApiClient.checkMobileStatus(mobile,false);
    }

    @RequestMapping("testRedisList")
    public void testRedisList(){
        RList<String> list = redissonClient.getList("aaabbbccc" +"_panel_1");
        PanelDataDTO dto = new PanelDataDTO();
        dto.setKey("bbb");
        dto.setValue("0.2");
        list.add(JSONObject.toJSONString(dto));
        redissonClient.getKeys().expireAt("aaabbbccc" +"_panel_1", DateUtil.addMinute(new Date(),100).getTime());
    }

    @RequestMapping("testRedisListV2")
    public void testRedisListV2(){
        RKeys keys = redissonClient.getKeys();
        long l = keys.countExists("aaa", "bbb");
        System.out.println(l);
    }

    //查询轨迹
    @RequestMapping("testCarOwnerTrackInfo")
    public CarOwnerTrackInfo testCarOwnerTrackInfo(@RequestBody String thirdOrderNo) {
        return esQueryClient.getTrackInfoByOrderNo(thirdOrderNo);
    }


    //LBS查询距离
    @RequestMapping("testLbsDistance")
    public List<NavigateCostDto> testLbsDistance(@RequestBody String orderSerialNo) {
        return lbsClient.navigateCost(NavigateCondition.builder()
                .startLat("30.067129")
                .startLng("118.178193")
                .startPoi("")
                .endLat("31.252501")
                .endLng("120.73913")
                .endPoi("")
                .traceId(UUID.randomUUID().toString())
                .orderChannel(852)
                .passengerId(null)
                .build());
    }

}
