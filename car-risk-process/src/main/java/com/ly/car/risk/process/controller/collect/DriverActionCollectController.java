package com.ly.car.risk.process.controller.collect;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.api.param.DriverActionCollectParam;
import com.ly.car.risk.process.repo.risk.mapper.DriverActionCollectMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverActionCollect;
import com.ly.car.risk.process.service.ability.DeviceTokenService;
import com.ly.car.risk.process.utils.Md5Util;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/driver/action")
@Slf4j
public class DriverActionCollectController {

    private static final String DEVICE_KEY = "driver:action:deviceToken:";

    @Resource
    private DriverActionCollectMapper driverActionCollectMapper;
    @Resource
    private DeviceTokenService deviceTokenService;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @RequestMapping("/collect")
    public UiResult driverActionCollect(@RequestBody DriverActionCollectParam param){
        DriverActionCollect collect = new DriverActionCollect();
        collect.setDriverId(param.getDriverId());
        collect.setMobile(param.getMobile());
        collect.setDriverCardNo(param.getPlate());
        collect.setEventType(param.getEventType());
        collect.setBoxId(param.getBoxId());//这边用缓存里面的deviceId来存
        if(param.getBoxId().startsWith("v2")){//腾讯的
            String md5String = Md5Util.md5Hex(param.getBoxId());
            RBucket<String> bucket = redissonClient.getBucket(DEVICE_KEY + md5String);
            if(bucket.isExists()){
                //存在则直接用存的设备id
                collect.setDeviceId(bucket.get());
            } else {
                //调用公共解密
                String deviceId = deviceTokenService.decryptDeviceId(param.getBoxId());
                if(StringUtils.isNotBlank(deviceId)){
                    collect.setDeviceId(deviceId);
                    bucket.set(deviceId,1, TimeUnit.DAYS);
                }
            }
        }
        collect.setCreateTime(new Date());
        collect.setProductLine(param.getProductLine());
        this.driverActionCollectMapper.insert(collect);
        return UiResult.ok();
    }

    @RequestMapping("collectDevice")
    public UiResult collectDevice(@RequestBody JSONObject jsonObject){
        String deviceToken = jsonObject.getString("deviceToken");
        //请求解密该token,拿到该deviceId

        //将deviceToken 做key存入redis,
        RBucket<String> bucket = redissonClient.getBucket(DEVICE_KEY + deviceToken);
        if(bucket.isExists()){
            return UiResult.ok();
        }


//        String url = "http://es.dss.17usoft.com/index/car-risk-driver-device/type/info/bulk";
//        Map<String,String> headMap = new HashMap<>();
//        headMap.put("Content-Type","application/json");
//        headMap.put("Authentication","3637ba0d-3bb0-454e-9c68-1fcb430a810b");
//        String post = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(null), headMap);
        return UiResult.ok();
    }

}
