package com.ly.car.risk.process.service.chain;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.RuleOnOffConfig;
import com.ly.car.utils.JsonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 名单就查下黑名单，有黑名单就不走规则了
 * */
@Component
@Slf4j
public class CustomerHandler extends FilterChainHandler{

    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private RuleRiskHandler ruleRiskHandler;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private RiskHitService riskHitService;

    @Override
    public UiResult doHandler(FilterParams params) {
        log.info("[CustomerHandler][doHandler][{}][{}]进入名单判断{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));
        this.next(ruleRiskHandler);
        Boolean ruleOnOff = getRuleOnOff(params.getSourceId());
        Date date = new Date();
        List<RiskCustomerManage> manageList = riskCustomerService.getListByValue(params,date);
        if(CollectionUtils.isEmpty(manageList) && !ruleOnOff){
            log.info("[CustomerHandler][doHandler][{}][{}]名单为空,进入规则判断{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));
            return this.nextHandler.doHandler(params);
        }
        if(CollectionUtils.isEmpty(manageList)){
            log.info("[CustomerHandler][doHandler][{}][{}]名单为空,不进入规则判断，直接返回{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));
            UiResult result = UiResult.ok();
            RiskResultDTO dto = new RiskResultDTO(0,"风控通过",null,null);
            result.setData(dto);
            return result;
        }
        log.info("[CustomerHandler][doHandler][{}][{}]名单不为空,进入具体名单判断{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));

        UiResult result;
        //判断白名单
        RiskCustomerManage whiteManage = manageList.stream().filter(e->e.getRiskType() == RiskCustomerRiskTypeEnum.white_list.getCode()).findFirst().orElse(null);
        if(whiteManage != null){
            result = UiResult.ok();
            RiskResultDTO dto = new RiskResultDTO(0,"风控通过",null,null);
            result.setData(dto);
            return result;
        }
        //判断是否黑名单
        RiskCustomerManage blackManage = manageList.stream().filter(e->e.getRiskType() == RiskCustomerRiskTypeEnum.black_list.getCode()).findFirst().orElse(null);
        log.info("[CustomerHandler][doHandler][{}][{}]命中黑名单111{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(blackManage));
        if(blackManage != null){
            log.info("[CustomerHandler][doHandler][{}][{}]命中黑名单{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(blackManage));
            result = UiResult.ok();
            result.setMsg("风控不通过");
            RiskResultDTO dto = new RiskResultDTO(405,"命中黑名单",null,params.getDriverCardNo());
            result.setData(dto);
            distributionRiskManageService.addByCustomerChain(params.getOrderId(),
                    RiskCustomerRiskTypeEnum.black_list.getCode().toString(),
                    params.getMainScene(), params.getChildScene(),
                    1,blackManage.getCustomerValue(),
                    RiskLevelEnum.HIGH.getCode());
            riskHitService.initHitRisk(params,new HitInfoDTO(RiskCustomerRiskTypeEnum.getMsgByCode(blackManage.getRiskType()),RiskLevelEnum.HIGH.getCode(),1,blackManage.getCustomerValue(),result));
            return result;
        }
        //判断一对一黑名单是否存在,存在验证是否绑定关系，是绑定关系则不通过
        RiskCustomerManage oneToOneManage = manageList.stream()
                .filter(e->e.getRiskType() == RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode()).findFirst().orElse(null);
        String phone = params.getUserPhone();
        if(params.getOrderId().startsWith("SFC")){
            SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(params.getOrderId());
            phone = sfcOrder.getPassengerCellphone();
        }
        if(oneToOneManage != null){
            if(phone != null && oneToOneManage.getBindUser().equals(phone)){
                log.info("[CustomerHandler][doHandler][{}][{}]命中司机一对一黑名单{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));
                result = UiResult.ok();
                result.setMsg("风控不通过");
                RiskResultDTO dto = new RiskResultDTO(405,"命中司机一对一黑名单",null,params.getDriverCardNo());
                result.setData(dto);
                distributionRiskManageService.addByCustomerChain(params.getOrderId(),
                        RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode().toString(),
                        params.getMainScene(), params.getChildScene(),
                        1,
                        oneToOneManage.getCustomerValue(),
                        RiskLevelEnum.HIGH.getCode());

                riskHitService.initHitRisk(params,new HitInfoDTO(RiskCustomerRiskTypeEnum.getMsgByCode(oneToOneManage.getRiskType()),
                        RiskLevelEnum.HIGH.getCode(),1,oneToOneManage.getCustomerValue(),result));
                return result;
            }
        }
        //当前sourceId是要走规则的话就继续走规则，否则直接过
        if(!ruleOnOff){
            return this.nextHandler.doHandler(params);
        }
        result = UiResult.ok();
        RiskResultDTO dto = new RiskResultDTO(0,"风控通过",null,null);
        result.setData(dto);
        return result;

    }

    public Boolean getRuleOnOff(String sourceId){
        try {
            String configJson = ConfigCenterClient.get("rule_on-off_config");
            Map<String,Map<String,Boolean>> configMap = JSONObject.parseObject(configJson, RuleOnOffConfig.class).getRuleOnOff();
            Map<String, Boolean> customerMap = configMap.get(sourceId);
            return customerMap.get("customer");
        } catch (Exception e) {
            log.error("获取是否只查询名单打开错误:",e);
        }
        return null;
    }
}
