package com.ly.car.risk.process.component;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SfcRuleServiceContextUtil {

    public static Map<String, List<String>> serviceNameMap;

    static {
        Map<String,List<String>> map = new HashMap<>();
        //司机接单
        List<String> PLACE_ORDER_RECEIVE_ORDER = new ArrayList<>();
        PLACE_ORDER_RECEIVE_ORDER.add("customerUserService");
        PLACE_ORDER_RECEIVE_ORDER.add("sfcRuleRisk018Service");
        PLACE_ORDER_RECEIVE_ORDER.add("sfcRuleRisk019Service");
        PLACE_ORDER_RECEIVE_ORDER.add("sfcRuleRisk020Service");
        PLACE_ORDER_RECEIVE_ORDER.add("sfcRuleRisk021Service");
        PLACE_ORDER_RECEIVE_ORDER.add("sfcRuleRisk022Service");
        PLACE_ORDER_RECEIVE_ORDER.add("sfcRuleRisk045Service");

        //用户预定
        List<String> PLACE_ORDER_USER_RESERVE   = new ArrayList<>();
        PLACE_ORDER_USER_RESERVE.add("customerUserService");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRisk023Service");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRisk024Service");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRisk038Service");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRisk039Service");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRisk040Service");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRisk041Service");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRisk042Service");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRisk043Service");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRisk044Service");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRiskKH003Service");
        PLACE_ORDER_USER_RESERVE.add("sfcRuleRiskKH004Service");

        map.put("2-1",PLACE_ORDER_RECEIVE_ORDER);
        map.put("2-2",PLACE_ORDER_USER_RESERVE);

        List<String> INVITE_DRIVER_SERVICE = new ArrayList<>();
        INVITE_DRIVER_SERVICE.add("sfcCustomerInviteService");
        map.put("2-4",INVITE_DRIVER_SERVICE);

        List<String> USER_CANCEL_ORDER = new ArrayList<>();
        USER_CANCEL_ORDER.add("sfcRuleRiskUserCancelService");
        map.put("2-6",USER_CANCEL_ORDER);

        List<String> USER_CANCEL_ORDER_AFTER = new ArrayList<>();
        USER_CANCEL_ORDER_AFTER.add("customerUserService");
        map.put("2-7",USER_CANCEL_ORDER_AFTER);
        
        List<String> DRIVER_FACE_TO_FACE_RECEIVE_ORDER = new ArrayList<>();
        DRIVER_FACE_TO_FACE_RECEIVE_ORDER.add("customerUserService");
        map.put("2-10",DRIVER_FACE_TO_FACE_RECEIVE_ORDER);

        List<String> USER_TO_DRIVER = new ArrayList<>();
        USER_TO_DRIVER.add("customerUserService");
        map.put("1-3",USER_TO_DRIVER);

//        List<String> USER_GET_PRICE = new ArrayList<>();
//        USER_GET_PRICE.add("customerUserService");
//        map.put("5-2",USER_GET_PRICE);

        //省钱中心
        List<String> REVENUE_SAVE_CENTER = new ArrayList<>();
        REVENUE_SAVE_CENTER.add("customerUserService");
        map.put("1-1",REVENUE_SAVE_CENTER);
        List<String> MILEAGE_CONVERSION = new ArrayList<>();
        MILEAGE_CONVERSION.add("a");
        map.put("1-2",MILEAGE_CONVERSION);
        serviceNameMap = map;
    }
}
