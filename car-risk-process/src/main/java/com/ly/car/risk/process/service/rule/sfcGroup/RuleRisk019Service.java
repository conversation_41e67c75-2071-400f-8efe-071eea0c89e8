package com.ly.car.risk.process.service.rule.sfcGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.rule.FilterOrderContext;
import com.ly.car.risk.process.service.rule.FilterRuleChain;
import com.ly.car.risk.process.service.rule.MapIsAllTrue;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 当天同userid用户完单时间差小于6分钟订单数大于2单，接单司机一致
 * */
@Service
@Slf4j
public class RuleRisk019Service extends FilterRuleChain {

    private static final String ruleNo = "019";

    @Resource
    private RuleRisk020Service ruleRisk020Service;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Override
    public UiResult doHandler(FilterOrderContext orderContext) {
        log.info("[FilterRuleChain][RuleRisk019Service][][]前置判断已通过，进入规则019判断{}");
        this.next(ruleRisk020Service);
        //判断当前是否执行此规则
        if(orderContext.getNeedRuleMap().get(ruleNo) == null && this.nextRule != null){
            return this.nextRule.doHandler(orderContext);
        }
        //没有下个规则就直接返回了
        if(this.nextRule == null && orderContext.getNeedRuleMap().get(ruleNo) == null){
            return orderContext.getUiResult();
        }
        //执行此规则就给个执行标记
        orderContext.getNeedRuleMap().put(ruleNo,true);

        //获取当天用户完单数
        List<OrderRiskContext> orderRiskContextList = orderContext.getUserContextList().stream().filter(context->context.getIntervalTime() != null)
                .filter(context->context.getIntervalTime() < orderContext.getSfcRiskRuleConfig().getTime019() )
                .filter(context-> DateUtil.string2Date(context.getFinishTime()).after(TimeUtil.currentDay()))
                .filter(context->StringUtils.isNotBlank(context.getMemberId()) && !context.getMemberId().equals("0"))
                .collect(Collectors.toList());
        if((!CollectionUtils.isEmpty(orderRiskContextList)) && orderRiskContextList.size() > orderContext.getSfcRiskRuleConfig().getOrderNum019()){
            //再判断当前司机是否一致
            Set<String> driverNos = orderRiskContextList.stream().map(OrderRiskContext::getDriverCardNo).collect(Collectors.toSet());
            if(!driverNos.isEmpty() && driverNos.size() == 1){
                String orderIds = StringUtils.join(orderRiskContextList.stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList()),",");
                log.info("[RuleRisk019Service][doHandler][{}][{}]命中019规则，关联用户为{},关联订单为{}"
                        ,orderContext.getMemberId(),orderContext.getDriverCardNo(),orderRiskContextList, JsonUtils.json(orderIds));
                distributionRiskManageService.addByRuleChain(orderRiskContextList,ruleNo,2,1,0,null, RiskLevelEnum.HIGH.getCode());
                UiResult result = UiResult.ok();
                result.setMsg("风控不通过");
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过019",null,null);
                result.setData(dto);
                orderContext.setUiResult(result);
            }
        }
        //判断是否已经全部结束，全部结束则返回通过
        if(MapIsAllTrue.isAllTrue(orderContext.getNeedRuleMap()) || this.nextRule == null){
//            UiResult result = UiResult.ok();
//            result.setData("0");
            return orderContext.getUiResult();
        }
        return this.nextRule.doHandler(orderContext);
    }
}
