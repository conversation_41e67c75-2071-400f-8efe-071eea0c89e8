package com.ly.car.risk.process.service.supplier.impl;

import com.ly.car.order.entity.SupplierInfo;
import com.ly.car.risk.process.client.SupplierApiClient;
import com.ly.car.risk.process.service.supplier.SupplierService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SupplierServiceImpl
 *
 * 供应商相关
 *
 * <AUTHOR>
 * @version Id : SupplierServiceImpl, v 1.0  2024-11-12 15:20,ling.yang Exp $
 */
@Service
public class SupplierServiceImpl implements SupplierService {

    @Resource
    private SupplierApiClient supplierApiClient;

    @Override
    public  Map<String, SupplierInfo> getSupplierMap() {
        List<SupplierInfo> supplierInfoList = supplierApiClient.getSupplierInfoList();
        if(CollectionUtils.isEmpty(supplierInfoList)){
            return new HashMap<>();
        }
        return  supplierInfoList.stream()
                .collect(Collectors.toMap(SupplierInfo::getCode,item -> item));
    }
}