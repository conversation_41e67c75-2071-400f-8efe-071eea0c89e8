package com.ly.car.risk.process.supplier.Tsan;

import lombok.Data;

@Data
public class T3WorkOrderCallBackParam {
    
    private Integer operateType;  //操作类型 0：创建工单 1：回复工单 2：完结工单
    private String platformOrderNo;  //T3平台订单号
    private String platformWorkOrderNo;  //T3工单号
    private String cpWorkOrderNo;  //合作方工单号
    private String woCateCode;  //工单类目编码code
    private Long eventTime;  //事件发生或处理时间（毫秒级时间戳）
    private String msg;  //描述
    private String userType;  //用户类型：1: 乘客 ; 2: 司机 ; 3: 其他
    private String userMobile;  //投诉人手机号
    private String attachments;  //用户反馈时上传的图片，json数组字符串，内容为图片url e.g. [“sample/url/for/pic1”,”sample/url/for/pic2”]
    private Integer judgeResult;  //判责结果, 1司机有责 2 司机无责
    private JudgeInfo judgeInfo; //判责信息

    @Data
    public static class JudgeInfo{
        private String unionId;
        private Integer judgeType;
        private String reason;
        private Integer disposeActType;//处置动作类型 1-执行处置 2-撤销处置
        private Integer disposeResult;//处置结果，1-成功 2-失败
    }


}
