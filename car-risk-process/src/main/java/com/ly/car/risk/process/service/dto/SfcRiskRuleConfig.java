package com.ly.car.risk.process.service.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SfcRiskRuleConfig {

    private Integer time015;
    private Integer orderNum015;
    private Integer orderNum034;
    private Integer orderNum035;
    private Integer distance035;
    private BigDecimal rate035;
    private Integer orderNum036;
    private Integer orderNum037;
    private Integer esKilo037;
    private BigDecimal rate037;

    private Integer time018;
    private Integer orderNum018;
    private Integer time019;
    private Integer orderNum019;
    private Integer orderNum020;
    private Integer distance020;
    private Integer orderNum021;
    private Integer distance022;
    private Integer orderNum022;
    private Integer distance023;
    private Integer orderNum023;
    private Integer orderNumByDriver023;
    private Integer time024;
    private Integer orderNum024;
    private Integer orderNumCurrent024;
    private Integer orderNumThreeDay024;
    private Integer time038;
    private Integer orderNum038;
    private Integer time039;
    private Integer orderNum039;
    private Integer phoneNum039;
    private Integer time040;
    private Integer orderNum040;
    private Boolean onOff040;
    private Integer time041;
    private Integer memberNum041;
    private Integer cityNum041;
    private Boolean onOff041;
    private Integer time042;
    private Integer orderNum042;
    private Integer cityNum042;
    private Boolean onOff042;
    private Boolean onOff043;
    private Boolean onOff044;
    private Integer time043;
    private Integer phoneNum043;
    private Integer orderNum043;
    private BigDecimal rate043;
    private Integer time044;
    private Integer orderNum044;
    private Integer kilo044;
    private BigDecimal rate044;
    private Boolean sync043;
    private Boolean sync044;
    private Boolean sync045;
    private Boolean onOff045;
    private Integer time045;
    private Integer orderNum045;
    private Integer kilo045;
    private BigDecimal rate045;
    private Boolean onOffKH003;
    private Boolean onOffKH004;
}
