package com.ly.car.risk.process.service.rule.mtGroup;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyReqExtConst;
import com.ly.car.risk.process.repo.risk.mapper.DriverCheckMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverCheck;
import com.ly.car.risk.process.service.DriverCheckService;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.StrategyUtil;
import com.ly.car.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class MtDriverIdCardService extends MtFilterHandler {

    @Resource
    private TianChuangRiskClient tianChuangRiskClient;
    @Resource
    private MqSendConvertService mqSendConvertService;
    @Resource
    private DriverCheckService driverCheckService;

    @Override
    public void doHandler(MtFilterContext context) {
        RiskResultNewDTO dto = new RiskResultNewDTO();
        Map<String, Object> data = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();
        data.put("driverId", context.getParam().getString("driverId"));
        data.put("idCard", context.getParam().getString("idCard"));
        dto.setObj(data);
        if (!validParam(context.getParam())) {
            dto.setCode(1);
            dto.setMessage("信息不匹配，请提交有效信息");
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
            context.getDto().setCode(1);
            context.getDto().setMessage("信息不匹配，请提交有效信息");
            resultMap.put("idCard", 1);
            context.getDto().setObj(resultMap);
            return;
        }

        //验证次数
        List<DriverCheck> driverCheckList = context.getDriverCheckService()
                .queryResult(0, context.getParam().getString("name"), context.getParam().getString("idCard"));
        driverCheckList = driverCheckList.stream().filter(driver -> driver.getResult() == 0).collect(Collectors.toList());
        //过了就不在验证三方，直接走下面的流程
        if (CollectionUtils.isNotEmpty(driverCheckList)) {
            if (this.nextHandler == null && dto.getCode() == 0) {
                mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
            } else {
                this.nextHandler.doHandler(context);
            }
            return;
        }

        StringBuilder msg = new StringBuilder();
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setIdCard((String) context.getParam().get("idCard"));
        param.setName((String) context.getParam().get("name"));
        param.setProductLine((String) context.getParam().get("productLine"));
        Integer result = tianChuangRiskClient.verifyIdCard(param, msg);
        context.getDriverCheckService().insert(context.getParam(), 0, result, msg.toString());
        if (result != 0) {
            //发送mq
            dto.setCode(1);
            dto.setMessage("信息不匹配，请提交有效信息");
            resultMap.put("idCard", 1);
            data.put("resultMap", resultMap);
            dto.setObj(data);
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
            context.getDto().setCode(1);
            context.getDto().setMessage(msg.toString());
            context.getDto().setObj(resultMap);
            return;
        }
        if (this.nextHandler == null && dto.getCode() == 0) {
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
        } else {
            this.nextHandler.doHandler(context);
        }
    }

    public Boolean validParam(JSONObject context) {
        if (context.get("driverId") == null) {
            return false;
        }
        if (context.get("name") == null) {
            return false;
        }
        if (context.get("idCard") == null) {
            return false;
        }
        return true;
    }

    public RiskSceneResult idCheck(UnifyCheckRequest request) {
        String certName = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NAME);
        String certNo = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO);

        Map<String, Object> data = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();
        data.put("driverId", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID));
        data.put("idCard", certNo);
        RiskResultNewDTO dto = new RiskResultNewDTO();

        if (StringUtils.isAnyBlank(certName, certNo)) {
            dto.setObj(data);
            dto.setCode(1);
            dto.setMessage("信息不匹配，请提交有效信息");
            mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
            return RiskSceneResult.fail("信息不匹配，请提交有效信息");
        }

        //验证次数
        List<DriverCheck> driverCheckList = driverCheckService.queryResult(0, certName, certNo);
        driverCheckList = driverCheckList.stream().filter(driver -> driver.getResult() == 0).collect(Collectors.toList());
        //过了就不在验证三方，直接走下面的流程
        if (CollectionUtils.isNotEmpty(driverCheckList)) {
           return RiskSceneResult.pass("");
        }

        StringBuilder msg = new StringBuilder();
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setIdCard(certNo);
        param.setName(certName);
        param.setProductLine(request.getProductLine());
        Integer result = tianChuangRiskClient.verifyIdCard(param, msg);
        driverCheckService.insert(request, 0, result,msg.toString());
        if (result != 0) {
            //发送mq
            dto.setCode(1);
            dto.setMessage("信息不匹配，请提交有效信息");
            resultMap.put("idCard", 1);
            data.put("resultMap", resultMap);
            dto.setObj(data);
            mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
            return RiskSceneResult.fail("信息不匹配，请提交有效信息");
        }
        return RiskSceneResult.pass("");
    }


}
