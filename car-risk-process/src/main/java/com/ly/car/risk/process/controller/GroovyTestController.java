package com.ly.car.risk.process.controller;

import com.ly.car.risk.process.utils.GroovyScriptUtil;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("groovy")
@Slf4j
public class GroovyTestController {

    @RequestMapping("test")
    public Boolean test(){
        String str = "class ActivationCheckScript {" +
                "   public boolean check(def data) {" +
                "       if(data.orderNum > 2){" +
                "           return true;" +
                "       } else {" +
                "           return false;" +
                "       }" +
                "   }" +
                "}";

        Map<String,Object> map = new HashMap<>();
        map.put("orderNum",5);
        GroovyClassLoader groovyClassLoader;
        ClassLoader parent = AutowiredAnnotationBeanPostProcessor.class.getClassLoader();
        groovyClassLoader = new GroovyClassLoader(parent);
        Class groovyClass = groovyClassLoader.parseClass(str);
        GroovyObject groovyObject = null;
        try {
            groovyObject = (GroovyObject) groovyClass.newInstance();
        } catch (Exception e) {

        }

        Object[] args = {map};
        Object ret = false;
        try {
//            ret = (Boolean) GroovyScriptUtil.invokeMethod(str, "check", args);
            ret = groovyObject.invokeMethod("check",args);
        } catch (Exception e) {
            log.error("验证特征脚本错误:",e);
        }

        return (Boolean) ret;
    }
}
