package com.ly.car.risk.process.controller.request;

import lombok.Data;

/**
 * Description of DriverBlackListResponse
 *
 * <AUTHOR>
 * @date 2024/4/16
 * @desc
 */
@Data
public class CarBlackInfo {
    // id
    private Long id;

    private String carNum;

    private String shieldTime;//拉黑时间

    private String invalidTime; //失效时间

    private int ttl;    //拉黑时长
    // 拉黑原因
    private String riskRemark;

    private int riskType;

    //操作类型
    private int optionType;

    private int status;

    //订单号
    private String bindOrder;

    //拉黑子类型
    private String blackChildType;

}