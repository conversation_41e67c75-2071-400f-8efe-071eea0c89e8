package com.ly.car.risk.process.supplier.yueyue;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class YueYueRiskSyncParam {
    private String app_id;    //约分配给合作方的唯⼀标识
    private String timestamp;    //请求发送时的秒级时间戳

    private String tenant_id;//租户id
    private Long driverId;//司机id
    private EventDTO event_info;//判罚依赖事件
    private OrderDTO order_info;
    private ViolationDTO violation_info;
    private List<DisposeDTOs> dispose_infos;


    @Data
    public static class EventDTO{
        private String event_id;//事件id
        private String event_source;//事件来源
        private String event_type;//事件类型
        private String event_type_name;
        private Long event_create_time;
        private Long event_operator_time;
        private String event_operator;
        private String eventContent;
        private String feedBackEventId;
    }

    @Data
    public static class OrderDTO{
        private String channel_main_order_id;
        private String channel_order_id;
        private String order_id;
        private String travel_id;
        private String order_type;
    }

    @Data
    public static class CanAppealReason{
        private String appealReasonCode;
        private String appealReason;
    }

    @Data
    public static class ViolationAppeal{
        private String can_appeal;
        private String can_not_appeal_reason;
        private List<CanAppealReason> can_appeal_reason;
        private Integer now_appeal_num;
        private Integer max_appeal_num;
        private Long appeal_end_time;
    }

    @Data
    public static class ViolationDTO{
        private String violation_id;
        private String violation_code;
        private Long violation_create_time;
        private String violation_desc;
        private Integer violation_amount;
        private List<String> violation_photos;
        private String violation_ext_info;
        private ViolationAppeal violation_appeal;
    }

    @Data
    public static class DisposeDTOs{
        private String dispose_id;
        private String dispose_type;
        private String dispose_status;
        private String dispose_reason;
        private String dispose_reason_code;
        private Long dispose_start_time;
        private Long dispose_end_time;
        private BigDecimal dispose_amount;
    }
}
