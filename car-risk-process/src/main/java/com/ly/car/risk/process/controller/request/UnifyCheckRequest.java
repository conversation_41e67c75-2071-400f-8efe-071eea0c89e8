package com.ly.car.risk.process.controller.request;

import com.ly.car.risk.common.enums.MetricStrategyProductLineEnum;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * Description of UnifyCheckRequest
 *
 * <AUTHOR>
 * @date 2024/7/24
 * @desc
 */
@Data
public class UnifyCheckRequest extends BaseRequest {
    
    private String scene;
    
    private String memberId;
    
    private String unionId;
    
    private String channel;
    
    /**
     * {@link MetricStrategyProductLineEnum}
     */
    private String productLine;
    
    private String orderId;
    
    private String userPhone;
    
    private String passengerPhone;
    
    private String carNum;
    
    private String ip;
    
    private String supplierCode;
    
    private String supplierName;
    
    // 用车分销单标识
    private boolean distributionFlag;
    // 权益单标识
    private boolean rightsOrderFlag;
    
    private String payAccount;
    
    // 证件号
    private List<String> cardNos;
    // 设备id
    private String       deviceId;
    
    /**
     * {@link UnifyReqExtConst}
     */
    private Map<String, Object> ext = new HashMap<>();
    
    //***********  内部调用参数 *********************
    
    /**
     * 需要校验的模块
     */
    private List<String> checkModule = new ArrayList<>();
    
    //***********  命中信息统计 *********************
    /**
     * 命中类型 0-策略 1-名单
     */
    private Integer hitType;
    
    /**
     * 命中规则
     * 策略时为规则编号
     * 名单时为名单类型
     */
    private String hitRule;
    
    /**
     * 命中规则为名单时，命中的值
     */
    private String customerValue;
    
    /**
     * 命中规则为名单时，命中的值的类型
     */
    private Integer customerType;
    
    /**
     * 命中规则为策略时，策略编号
     */
    private String strategyNos;
    
    /**
     * 命中规则为策略时，命中字段
     */
    private String hitField;
    
    /**
     * 命中规则为策略时，管控对象
     */
    private String controlTarget;
    
    /**
     * 命中规则为策略时，处置动作
     */
    private String disposeAction;
    
    //************* TO BE ADDED *******************
}
