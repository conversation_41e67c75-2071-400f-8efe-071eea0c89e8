package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.api.LabelClient;
import com.ly.car.risk.process.api.rsp.LabelRsp;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.MainSceneEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.controller.params.CustomerParams;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.controller.params.RiskOrderParam;
import com.ly.car.risk.process.controller.params.SubsidyParam;
import com.ly.car.risk.process.service.ConfigCenterService;
import com.ly.car.risk.process.service.DriverCheckService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.RiskOrderManageService;
import com.ly.car.risk.process.service.SafeWarningService;
import com.ly.car.risk.process.service.chain.CustomerHandler;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.core.RiskQueryService;
import com.ly.car.risk.process.service.dto.HcSceneConfig;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.MtDriverConfig;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SubsidyRsp;
import com.ly.car.risk.process.service.groovy.RiskAnalysisEngineService;
import com.ly.car.risk.process.service.rule.IPolicyProcess;
import com.ly.car.risk.process.service.rule.SfcConvertHandlerService;
import com.ly.car.risk.process.service.rule.YncConvertHandlerService;
import com.ly.car.risk.process.service.rule.common.CommonFilterContext;
import com.ly.car.risk.process.service.rule.common.CommonFilterHandler;
import com.ly.car.risk.process.service.rule.common.CommonHandlerMap;
import com.ly.car.risk.process.service.rule.hcGroup.HcConvertHandlerService;
import com.ly.car.risk.process.service.rule.imGrooup.ImConvertHandlerService;
import com.ly.car.risk.process.service.rule.imGrooup.ImFilterContext;
import com.ly.car.risk.process.service.rule.imGrooup.ImFilterHandler;
import com.ly.car.risk.process.service.rule.mtGroup.FilterTextConvertService;
import com.ly.car.risk.process.service.rule.mtGroup.MtFilterContext;
import com.ly.car.risk.process.service.rule.mtGroup.MtFilterHandler;
import com.ly.car.risk.process.service.rule.mtGroup.MtHandlerMap;
import com.ly.car.risk.process.service.rule.priceGroup.FilterCheckPriceHandler;
import com.ly.car.risk.process.service.rule.sendGroup.FilterSendOrderHandler;
import com.ly.car.risk.process.service.rule.sfcNewGroup.FilterSfcHandler;
import com.ly.car.risk.process.utils.RandomUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 风控对外接口
 * 所有转换在这一层做
 * */
@RestController
@RequestMapping("risk")
@Slf4j
public class CommonRiskQueryController {

    @Resource
    private CustomerHandler customerHandler;
    @Resource
    private YncConvertHandlerService yncConvertHandlerService;
    @Resource
    private SfcConvertHandlerService sfcConvertHandlerService;
    @Resource
    private HcConvertHandlerService hcConvertHandlerService;
    @Resource
    private ImConvertHandlerService imConvertHandlerService;
    @Resource
    private SafeWarningService safeWarningService;
    @Resource
    private IPolicyProcess iPolicyProcess;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskOrderManageService riskOrderManageService;
    @Resource
    private DriverCheckService driverCheckService;
    @Resource
    private RiskAnalysisEngineService riskAnalysisEngineService;
    @Resource
    private RiskQueryService riskQueryService;
    @Resource
    private FilterTextConvertService filterTextConvertService;
    @Resource
    private LabelClient labelClient;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    /**
     * 这一块目前时有几条业务线，汇川、专车、顺风车
     * 这一大坨可以优化为智保留queryResult()和dispatchByScene()
     * */
    @RequestMapping("query")
    public UiResult riskQuery(@RequestBody FilterParams params) throws Exception {
        log.info("[][][][]统一请求参数:{}", JsonUtils.json(params));
        if(params.getMainScene() == null){
            UiResult result = UiResult.fail(1000);
            return result;
        }

        // 判断是否顺风车面对面录单
        judgeFaceToFaceLd(params);
        
        //没有业务线的说明是通用的,目前先把场景放外面
        if(params.getMainScene() == 8){
            List<ImFilterHandler> imFilterHandlerList = imConvertHandlerService.getImHandlerList(params.getMainScene(), params.getChildScene());
            ImFilterContext imFilterContext = imConvertHandlerService.convertParam(params);
            imFilterHandlerList.get(0).doHandler(imFilterContext);
            return imFilterContext.getUiResult();
        }

        //CC8G0ZVJdOKfsUMP77DlZFzLXeeh3N9p-用户预定、司机接单
        //lg1G8BrFgg2UB5ifSjoepSL4OwjDxpJy-超级省钱卡quick-car
        //yUW1CJmDY99Flrbp3KC1XAeT5w3nYVYE 系统内部调用，已经不用了
        if (StringUtils.isNotBlank(params.getSourceId())
                && (params.getSourceId().equals("CC8G0ZVJdOKfsUMP77DlZFzLXeeh3N9p")
                || params.getSourceId().equals("yUW1CJmDY99Flrbp3KC1XAeT5w3nYVYE")
                || params.getSourceId().equals("lg1G8BrFgg2UB5ifSjoepSL4OwjDxpJy"))) {
            params.setProductLine("SFC");
        }
        //生成request_id
        String requestId = RandomUtil.getRandomString(16)+System.currentTimeMillis();
        log.info("[][][][]生成请求id{}",requestId);
        params.setRequestId(requestId);
        if(StringUtils.isNotBlank(params.getSourceId()) && params.getSourceId().equals("lg1G8BrFgg2UB5ifSjoepSL4OwjDxpJy")){
            return this.customerHandler.doHandler(params);
        }

        if(params.getMainScene()==MainSceneEnum.SAFE_WARNING.getCode()){
            return safeWarningService.getSafeByOrderId(params.getOrderId(),params.getChildScene(), params.getText());
        }

        if(StringUtils.isNotBlank(params.getProductLine()) && params.getProductLine().equals("SFC")){
            //司机接单场景的限制，会出现没有司机穿过来的情况
            List<FilterSfcHandler> filterSfcHandlerList = sfcConvertHandlerService.getSfcHandlerList(params.getMainScene(), params.getChildScene());
            if(filterSfcHandlerList == null){
                return UiResult.ok();
            }
            log.info("[][][][]获取执行器数量：{}",filterSfcHandlerList.size());
            if(params.getMainScene() == 2 && params.getChildScene() == 2 && (params.getHistorySplitFlag() == 1 || params.getSplitFlag() == 1)){
                return UiResult.ok();
            }
            // 去除分销判定逻辑
//            if(params.getMainScene() == 2 && params.getChildScene() == 2){
//                //查询
//                LabelRsp.DetailRsp detailRsp = labelClient.queryLabel(params.getMemberId(), params.getUnionId());
//                if(detailRsp != null && detailRsp.getNewSplitStatus() != null && detailRsp.getNewSplitStatus() > 0){
//                    return UiResult.ok();
//                }
//            }
            try {
                if(params.getMainScene() == 2 && params.getChildScene() == 1 && params.getSplitFlag() == 1){
                    String key = "risk:level:driver:"+params.getDriverCardNo();
                    RBucket<String> bucket = redissonClient.getBucket(key);
                    if(bucket.isExists()){
                        log.info("[][][][]命中拆单接单司机{}",params.getDriverCardNo());
                        RiskResultDTO dto = new RiskResultDTO(1, "风控不通过", "split_customer", params.getDriverCardNo());
                        HitInfoDTO hitInfoDTO = new HitInfoDTO("split_customer",5,0, params.getDriverCardNo(), null);
                        riskHitService.initHitRisk(params,hitInfoDTO);
                        String configJson = ConfigCenterClient.get("random_config");
                        if(StringUtils.isNotBlank(configJson)){
                            String[] split = configJson.split(",");
                            List<Integer> randomNum = new ArrayList<>();
                            for(String str : split){
                                randomNum.add(Integer.valueOf(str));
                            }
                            if(randomNum.size() == 1){
                                return UiResult.ok(dto);
                            }

                            Random rand = new Random();
                            int randomIndex = rand.nextInt(randomNum.size());
                            if(randomIndex == 0){
                                randomIndex = randomIndex +2;//不要命中
                            }
                            Integer selectNum = randomNum.get(randomIndex);
                            if(selectNum == 1){
                                return UiResult.ok(dto);
                            }
                            log.info("[][][][]命中拆单接单司机但不拦截{}",params.getDriverCardNo());
                        }
                    }
                }
            } catch (Exception e){
                log.error("[][][][]拆单接单报错",e);
            }

            //录单的接单不走风控了,等新模式开发完成切换
            if(params.getMainScene() == 2 && params.getChildScene() == 1 && StringUtils.isNotBlank(params.getSourceTag()) && params.getSourceTag().equals("LD")){
                return UiResult.ok();
            }

            //获取上下文参数，并将返回默认值塞入
            FilterSfcContext filterSfcContext = sfcConvertHandlerService.convertParams(params);
            //执行链路，对默认返回值进行修改
            if(filterSfcHandlerList != null && filterSfcHandlerList.size() > 0){
                //暂时兼容拆单，当前有创单及
                filterSfcHandlerList.get(0).doHandler(filterSfcContext);
            }
            try {
                UiResult<RiskResultDTO> result = riskQueryService.queryResult(params);
                if(result.getData() !=null && result.getData().getCode() == 1){
                    return result;
                }
            } catch (Exception e) {
                log.error("[][][][]规则引擎启动错误:{}",e);
            }
            //返回上下文的返回值
            return filterSfcContext.getUiResult();
        }

        if(StringUtils.isNotBlank(params.getProductLine()) && params.getProductLine().equals("YNC")){
            if(params.getMainScene().equals(MainSceneEnum.SEARCH_PRICE.getCode())){
                //名单类单独写个服务，每次都是一个方法就好了
                List<FilterCheckPriceHandler> filterChain =yncConvertHandlerService.getCheckPriceHandler(params.getMainScene(), params.getChildScene());
                return filterChain.get(0).doHandler(yncConvertHandlerService.convertCheckPrice(params));
            }
            //todo 后面优化放到场景服务里面去
            List<FilterSendOrderHandler> filterChain = yncConvertHandlerService.getSendOrderHandler(params.getMainScene(),params.getChildScene());
            FilterSendOrderContext context = yncConvertHandlerService.convertSendOrder(params);
            if(filterChain.isEmpty()){
            } else {
                filterChain.get(0).doHandler(context);
            }
            //判断上下文返回值
            UiResult result = convertResult(context);
            try {
                UiResult<RiskResultDTO> resultYNC = riskQueryService.queryResult(params);
                if(resultYNC.getData() !=null && resultYNC.getData().getCode() == 1){
                    log.info("[][][][]专车规则引擎命中返回{}",JsonUtils.json(resultYNC));
                    return resultYNC;
                }
            } catch (Exception e) {
                log.error("[][][][]专车规则引擎启动错误:{}",e);
            }
                log.info("[][][][]专车风控返回:{}",JsonUtils.json(result));
                return result;
        }


        //卡罗拉不用的，可以直接干掉，当初因为主体可能独立出去，所以又转了下
        if(StringUtils.isNotBlank(params.getProductLine()) && params.getProductLine().equals("HC")){
            //汇川转到B端请求
            params.setMainScene(11);
            if(params.getChildScene() == 5){
                params.setChildScene(4);
            } else if(params.getChildScene() == 21){
                params.setChildScene(8);//安全预警
            } else if(params.getChildScene() == 7){
                params.setChildScene(6);//司机提现
            } else if(params.getChildScene() == 11){
                params.setChildScene(7);
            } else if(params.getChildScene() == 8){
                params.setChildScene(12);
            } else if(params.getChildScene() == 9){
                params.setChildScene(13);
            } else if(params.getChildScene() == 12){
                params.setChildScene(13);
            } else if(params.getChildScene() == 4){
                params.setChildScene(11);
            }
            if(StringUtils.isNotBlank(params.getDriverCardNo())){
                params.setPlate(params.getDriverCardNo());
            }
            JSONObject convertJson = (JSONObject) JSONObject.toJSON(params);
            return this.mtQuery(convertJson);
        }

        return iPolicyProcess.dispatchByScene(params);
    }

//    @RequestMapping("tcQuery")
//    public UiResult tcQuery(@RequestBody FilterParams params){
//        return riskQueryService.queryResult(params);
//    }

    @RequestMapping("hcQuery")
    public UiResult hcQuery(@RequestBody CustomerParams params){

        return null;
    }


    public static void main(String[] args) {
        String strNum = "1,2,3,4,5";
        String[] split = strNum.split(",");
        List<Integer> randomNum = new ArrayList<>();
        for(String str : split){
            randomNum.add(Integer.valueOf(str));
        }
        if(randomNum.size() == 1){
            System.out.println("aaa"+randomNum.get(0));
        }
        Random rand = new Random();
        int randomIndex = rand.nextInt(randomNum.size()+1);
        System.out.println("随机数"+randomIndex);
        if(randomIndex == 0){
            randomIndex = randomIndex +2;
        }
        Integer selectNum = randomNum.get(randomIndex-1);
        System.out.println("bbb"+selectNum);
        if(selectNum == 1){
            System.out.println("ccc"+selectNum);
        }
    }

    @RequestMapping("commonQuery")
    public UiResult commonQuery(@RequestBody JSONObject map){
        log.info("[][][][]公共请求风控参数:{}",JsonUtils.json(map));
        String scene = map.getInteger("mainScene")+"-" + map.getInteger("childScene")+"";
        List<String> stringList = CommonHandlerMap.serviceNameMap.get(scene);
        if(CollectionUtils.isEmpty(stringList)){
            return UiResult.ok();
        }
        List<CommonFilterHandler> handlerList = new ArrayList<>();
        for(String serviceName : stringList){
            handlerList.add(SpringContextUtil.getBean(serviceName));
        }
        CommonFilterContext context = new CommonFilterContext();
        context.setParam(map);
        handlerList.get(0).doHandler(context);

        return UiResult.ok(context.getDto());
    }


    @RequestMapping("mtQuery")
    public UiResult mtQuery(@RequestBody JSONObject map){
        log.info("[][][][]司机端统一请求信息{}",JsonUtils.json(map));
        String scene = map.getInteger("mainScene")+"-" + map.getInteger("childScene")+"";
        List<String> stringList = MtHandlerMap.serviceNameMap.get(scene);
        String productLine = map.getString("productLine");
        if(StringUtils.isNotBlank(productLine) && productLine.equals("GJ") && scene.equals("11-2")){
            //该渠道暂时不验证行驶证
            stringList.remove("mtDriverCarOwnerService");
            stringList.remove("mtDriverCommonService");
        }
        if(CollectionUtils.isEmpty(stringList)){
            return UiResult.ok();
        }
        log.info("[][][][]司机端请求获取执行器:{}",JsonUtils.json(stringList));
        List<MtFilterHandler> handlerList = new ArrayList<>();
        for(String serviceName : stringList){
           handlerList.add(SpringContextUtil.getBean(serviceName));
        }
        for(int i = 1;i< handlerList.size();i++){
            handlerList.get(i-1).next(handlerList.get(i));
        }
        MtFilterContext context = new MtFilterContext();
        MtDriverConfig config = new MtDriverConfig();
        config.setLoginTimes(3);
        try {
            String configJson = ConfigCenterClient.get("mt_driver_config");
            if(StringUtils.isNotBlank(configJson)){
                config = JSONObject.parseObject(configJson,MtDriverConfig.class);
                map.put("config",config);
            }
        } catch (Exception e) {
        }
        context.setWhiteType(MtHandlerMap.customerWhiteMap.get(scene));
        context.setBlackType(MtHandlerMap.customerBlackMap.get(scene));
        context.setDriverCheckService(driverCheckService);
        context.setHcSceneConfig(queryConfig());
        context.setParam(map);
        handlerList.get(0).doHandler(context);
        log.info("[][][][]司机端请求风控未处理返回:{}",JsonUtils.json(context.getDto()));
        //根据返回值映射不同的文案
        if(context.getDto().getCode() !=0 ){
            String text = filterTextConvertService.convertText(scene, map.getString("productLine"));
            if(text != null){
                context.getDto().setMessage(text);
            }
        }
        log.info("[][][][]司机端请求风控已处理返回:{}",JsonUtils.json(context.getDto()));
        return UiResult.ok(context.getDto());
    }


    /**
     * 专车场景返回值封装
     * */
    public UiResult convertResult(FilterSendOrderContext context){
        UiResult result = UiResult.ok();
        RiskResultDTO dto = new RiskResultDTO();
        dto.setLevel(0);
        if(context.getRuleList().size() == 0){
            result.setData(dto);
        } else if(context.getRuleList().size() == 1){
            if(context.getRuleList().get(0).getLevel() == 5){
                dto = new RiskResultDTO(405,"",context.getRuleList().get(0).getRuleNo()
                        ,"", 1);
                result.setData(dto);
            } else {
                dto = new RiskResultDTO(405,"",context.getRuleList().get(0).getRuleNo()
                        ,"", 2);
                result.setData(dto);
            }
            if(context.getRuleList().get(0).getRuleNo().equals("1003") || context.getRuleList().get(0).getRuleNo().equals("2001")){

            } else {
                riskHitService.initHitRisk(context.getParams(), new HitInfoDTO(context.getRuleList().get(0).getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,result));
            }
        } else {
            String ruleNoList = StringUtils.join(context.getRuleList().stream()
                    .map(RuleChain::getRuleNo).distinct().collect(Collectors.toList()),",");
            dto = new RiskResultDTO(405,"",ruleNoList
                    ,"", 1);
            result.setData(dto);
            riskHitService.initHitRisk(context.getParams(), new HitInfoDTO(ruleNoList,
                    RiskLevelEnum.HIGH.getCode(),0,null,result));
        }
        return result;
    }


    @RequestMapping("querySubsidy")
    public UiResult querySubsidy(@RequestBody SubsidyParam param){
        log.info("[querySubsidy][][][]B补放开查询参数:{}",JsonUtils.json(param));
        SubsidyRsp rsp = new SubsidyRsp();
        rsp.setIsPass(true);
        log.info("[querySubsidy][][][]B补放开查询返回:{}",JsonUtils.json(rsp));
        return UiResult.ok(rsp);
    }

    //015，035，037 ，036，018，027，034
    @RequestMapping("queryRiskOrder")
    public UiResult queryRiskOrder(@RequestBody RiskOrderParam param){
        log.info("[queryRiskOrder][][][]风险单查询:{}",JsonUtils.json(param));
        return this.riskOrderManageService.queryRiskOrder(param.getOrderId(),param.getProductLine(),param.getOrderIds());
    }

    /**
     * 获取配置信息
     * */
    private HcSceneConfig queryConfig(){
        try {
            String configJson = ConfigCenterClient.get("hc_driver_risk");
            HcSceneConfig config = JSONObject.parseObject(configJson,HcSceneConfig.class);
            log.info("[][][][]获取汇川规则配置:{}",configJson);
            log.info("[][][][]获取汇川规则配置转换:{}", JsonUtils.json(config));
            return config;
        } catch (Exception e) {
            log.error("获取汇川规则配置错误:",e);
        }
        return null;
    }
    
    private void judgeFaceToFaceLd(FilterParams params) {
        // 1. 场景2-2
        // 2. sourceTag=LD
        // 3. sourceId=CC8G0ZVJdOKfsUMP77DlZFzLXeeh3N9p
        // 这3个条件都满足，就算面对面录单
        if (!Objects.equals(params.getMainScene(), 2)
                || !Objects.equals(params.getChildScene(), 2)
                || !Objects.equals(params.getSourceTag(), "LD")
                || !Objects.equals(params.getSourceId(), "CC8G0ZVJdOKfsUMP77DlZFzLXeeh3N9p")) {
            return;
        }
        
        // 配置：是否开启录单校验
        String faceToFaceLdReceiveOrderSwitch = ConfigCenterService.getDefault("FACE_TO_FACE_LD_RECEIVE_ORDER_SWITCH", "1");
        
        if (!Objects.equals(faceToFaceLdReceiveOrderSwitch, "1")) {
            return;
        }
        
        // 是面对面录单，附加内部处理逻辑
        params.setMainScene(2);
        params.setChildScene(10);
    }
}
