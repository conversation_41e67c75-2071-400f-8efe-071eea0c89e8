package com.ly.car.risk.process.service.core;

import cn.hutool.core.lang.Assert;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.api.LabelClient;
import com.ly.car.risk.process.api.rsp.LabelRsp;
import com.ly.car.risk.process.constants.RiskCustomerCustomerTypeEnum;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.controller.request.DriverCancelRequest;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskStrategy;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.groovy.HitInfoDTO;
import com.ly.car.risk.process.service.groovy.HitStrategyDTO;
import com.ly.car.risk.process.service.groovy.RiskAnalysisEngineService;
import com.ly.car.utils.JsonUtils;
import com.tencentcloudapi.cwp.v20180228.models.Strategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskQueryService {

    @Resource
    private RiskAnalysisEngineService riskAnalysisEngineService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskStrategyCache riskStrategyCache;
    @Resource
    private DisposeCenterService disposeCenterService;
    @Resource
    private LabelClient labelClient;


    public UiResult<RiskResultDTO> queryResult(FilterParams params){
        RiskResultDTO resultDTO = new RiskResultDTO();
        if(params.getMainScene() == 2 && params.getChildScene() == 1){
            params.setSourceId("IoITLz70HvAewf4tuPheV5G6kHqPKDbE");
            if(params.isDistributionOrder()){
                return UiResult.ok(resultDTO);
            }
        } else if(params.getMainScene() == 5 && params.getChildScene() == 2){
            params.setSourceId("wn65cBfIGu1qkNSeZGk9kaI0A0r9c25O");
            if((StringUtils.isNotBlank(params.getMemberId()) && params.getMemberId().equals("0"))
                    || (StringUtils.isNotBlank(params.getMemberId()) && params.getMemberId().equals("1832121"))){
                return UiResult.ok(resultDTO);
            }
        } else if(params.getMainScene() == 2 && params.getChildScene() == 3){
            params.setSourceId("XFJoz1VHavaa6Kkl2L8MmgKZdw44Iwhq");
        } else {
            return UiResult.ok(resultDTO);
        }

        Map<String, List<HitStrategyDTO>> resultMap = riskAnalysisEngineService.queryRisk(params);
        if(resultMap == null){
            return UiResult.ok(resultDTO);
        }
        log.info("[][][][]规则引擎查询命中结果未封装返回{}",JsonUtils.json(resultMap));



        List<HitStrategyDTO> customerList = resultMap.get("customer");
        if(CollectionUtils.isNotEmpty(customerList)){
            resultDTO = new RiskResultDTO(1,"命中名单",null,customerList.get(0).getValue());
            //命中黑名单直接返回
//            return UiResult.ok(resultDTO);
        }

        List<HitStrategyDTO> ruleList = resultMap.get("rule");

        // 老系统分销逻辑移除
//        if(CollectionUtils.isNotEmpty(customerList) || CollectionUtils.isNotEmpty(ruleList)){
//            LabelRsp.DetailRsp detailRsp = labelClient.queryLabel(params.getMemberId(), params.getUnionId());
//            if(detailRsp != null && detailRsp.getNewSplitStatus() != null && detailRsp.getNewSplitStatus() > 0){
//                params.setHistorySplitFlag(1);
//            }
//        }

        //询价判断
        if(params.getMainScene() == 5 && params.getChildScene() == 2 && (params.getHistorySplitFlag() == 1 || params.getSplitFlag() == 1)){
            log.info("[][][{}][{}]该用户有过拆单询价，不走风控",params.getMemberId(),params.getUnionId());
            return UiResult.ok(resultDTO);
        }
        if(params.getMainScene() == 2 && params.getChildScene() == 2 && (params.getHistorySplitFlag() == 1 || params.getSplitFlag() == 1)){
            log.info("[][][{}][{}]该用户有过拆单创单，不走风控{}",params.getMemberId(),params.getUnionId(),params.getOrderId());
            return UiResult.ok(resultDTO);
        }

        if(CollectionUtils.isNotEmpty(ruleList)){
            // todo
            resultDTO = convertByLine(ruleList,params);
            //对命中上线的命中策略进行处罚
            RiskStrategy riskStrategy = selectDisposeStrategy(ruleList);
            actionDispose(riskStrategy,params);
        }
        log.info("[][][][]规则引擎查询命中结果封装后返回{}",JsonUtils.json(resultDTO));
        if(resultDTO.getCode() != 0){
            //发送命中关联
            List<String> strategyNos = riskHitService.saveNewRiskHit(params,resultMap,resultDTO);
            log.info("[][][][]规则引擎查询命中名单:{},命中策略{}", JsonUtils.json(customerList),JsonUtils.json(ruleList));
            //虽然命中了，但是要看下这边的策略是否是测试，如果是测试，则
        }
        log.info("[][][][]规则引擎查询命中结果返回{}",JsonUtils.json(resultDTO));
        return UiResult.ok(resultDTO);
    }

    /**
     * 选取处置的策略
     * */
    public RiskStrategy selectDisposeStrategy(List<HitStrategyDTO> strategyDTOList){
        List<RiskStrategy> riskStrategyList = new ArrayList<>();
        for(HitStrategyDTO dto : strategyDTOList){
            riskStrategyList.add(riskStrategyCache.loadStrategy(dto.getStrategyNo()));
        }
        log.info("[][][][]选取需要处置的策略{}",JsonUtils.json(riskStrategyList));
        return riskStrategyList.stream().filter(data->data.getType()==1).findFirst().orElse(null);
    }

    /**
     * 处置的参数转换
     * */
    public void actionDispose(RiskStrategy riskStrategy,FilterParams params){
        if(riskStrategy == null){
            return;
        }
        String customerValue = null;
        String passengerCellphone = StringUtils.isNotBlank(params.getPassengerCellphone())?params.getPassengerCellphone():params.getUserPhone();
        String hitField = riskStrategy.getHitField();
        Integer riskType = riskStrategy.getHitAction();
        Integer customerType = 0;
        if(riskStrategy.getControlTime() == 0 || StringUtils.isBlank(hitField)){
            //说明当前是无处置动作的
            return;
        }
        if(hitField.equals("driverCardNo")){
            customerValue = params.getDriverCardNo();
            customerType = RiskCustomerCustomerTypeEnum.car_number.getCode();
        } else if(hitField.equals("userId")){
            customerValue = StringUtils.isNotBlank(params.getUnionId())?params.getUnionId():params.getMemberId();
            customerType = RiskCustomerCustomerTypeEnum.user_id.getCode();
        } else if(hitField.equals("phone")){
            customerValue = StringUtils.isNotBlank(params.getPassengerCellphone())?params.getPassengerCellphone():params.getUserPhone();
            customerType = RiskCustomerCustomerTypeEnum.user_phone.getCode();
        }
        //处理拉黑类型
        Integer resultDisposeType = 7;//默认是1v1
        if(riskType == 0){
            resultDisposeType = 1;//如果命中动作是0全局拉黑的情况
        }
        log.info("[][][][]规则引擎开始执行拉黑操作{}",customerValue);
        disposeCenterService.actionCustomer(customerValue,passengerCellphone,riskStrategy.getControlTime()
                ,params.getOrderId(),riskStrategy.getStrategyNo(), resultDisposeType, customerType, null);
    }

    public RiskResultDTO convertByLine(List<HitStrategyDTO> hitStrategyDTOS,FilterParams params){
        if(CollectionUtils.isEmpty(hitStrategyDTOS)){
            return new RiskResultDTO();
        }

        //查询所有策略
        List<RiskStrategy> riskStrategyList = new ArrayList<>();
        for(HitStrategyDTO dto : hitStrategyDTOS){
            riskStrategyList.add(riskStrategyCache.loadStrategy(dto.getStrategyNo()));
        }

        if(params.getProductLine().equals("YNC")){
            if(hitStrategyDTOS.size() == 0){
                return new RiskResultDTO();
            } else if(hitStrategyDTOS.size() == 1 && hitStrategyDTOS.get(0).getHitInfoDTO().size() == 1){
                RiskStrategy strategy = riskStrategyList.stream()
                            .filter(data->data.getStrategyNo().equals(hitStrategyDTOS.get(0).getStrategyNo())&&data.getType() == 0)
                            .findFirst().orElse(null);
                if(strategy != null && strategy.getType() == 0){
                    return new RiskResultDTO();
                }
                //查看是否有高风险的
                Integer riskLevel = 5;
                if(riskLevel == 5){
                    return new RiskResultDTO(1,"",hitStrategyDTOS.get(0).getHitInfoDTO().get(0).getHit(),"",1);
                } else {
                    return new RiskResultDTO(1,"",hitStrategyDTOS.get(0).getHitInfoDTO().get(0).getHit(),"",2);
                }
            } else {
                //多规则情况
                Integer riskLevel = 5;
                List<String> ruleNos = new ArrayList<>();
                for(HitStrategyDTO dto : hitStrategyDTOS){
                    RiskStrategy strategy = riskStrategyList.stream()
                            .filter(data->data.getStrategyNo().equals(hitStrategyDTOS.get(0).getStrategyNo())&&data.getType() == 0)
                            .findFirst().orElse(null);
                    if(strategy != null){
                        log.info("[][][][]当前策略在测试阶段，返回过滤清除{}",JsonUtils.json(strategy));
                        continue;
                    }
                    ruleNos.addAll(dto.getHitInfoDTO().stream().map(HitInfoDTO::getHit).collect(Collectors.toList()));
                }
                if(ruleNos.size() > 0){
                    return new RiskResultDTO(1,"风控不通过",StringUtils.join(new TreeSet<>(ruleNos)),"",2);
                }
            }
        } else if(params.getProductLine().equals("SFC")){
            List<String> ruleNos = new ArrayList<>();
            for(HitStrategyDTO dto : hitStrategyDTOS){
                RiskStrategy strategy = riskStrategyList.stream()
                        .filter(data->data.getStrategyNo().equals(hitStrategyDTOS.get(0).getStrategyNo())&&data.getType() == 0)
                        .findFirst().orElse(null);
                if(strategy != null){
                    log.info("[][][][]当前策略在测试阶段，返回过滤清除{}",JsonUtils.json(strategy));
                    continue;
                }
                ruleNos.addAll(dto.getHitInfoDTO().stream().map(HitInfoDTO::getHit).collect(Collectors.toList()));
            }
            if(ruleNos.size() > 0) {
                return new RiskResultDTO(1, "风控不通过", StringUtils.join(new TreeSet<>(ruleNos), ","), null);
            }
        }
        return new RiskResultDTO();
    }
}
