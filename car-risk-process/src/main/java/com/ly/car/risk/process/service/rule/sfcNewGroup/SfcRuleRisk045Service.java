package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk045Service extends FilterSfcHandler{

    private static final String ruleNo = "045";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        log.info("[FilterRuleChain][SfcRuleRisk045Service][][]前置判断已通过，进入规则045判断{},命中规则{}", JsonUtils.json(context.getDriverContextList()),context.getRuleNo());
        try {
            if(!context.getSfcRiskRuleConfig().getOnOff045()){
                if(this.nextHandler != null){
                    this.nextHandler.doHandler(context);
                }
                return;
            }
            List<OrderRiskContext> orderRiskContextList = context.getDriverContextList().stream()
                    .filter(data-> DateUtil.string2Date(data.getFinishTime()).after(DateUtil.addHour(new Date(),-context.getSfcRiskRuleConfig().getTime045())))
                    .filter(data-> StringUtils.isNotBlank(data.getPassengerCellphone()))
                    .distinct()
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(orderRiskContextList)){
                //对手机号进行切割
                orderRiskContextList.forEach(data->data.setPassengerCellphone(data.getPassengerCellphone().substring(0,7)));
                Map<String,List<OrderRiskContext>> phoneGroup = orderRiskContextList.stream()
                        .collect(Collectors.groupingBy(OrderRiskContext::getPassengerCellphone));
                for(Map.Entry<String,List<OrderRiskContext>> entry : phoneGroup.entrySet()){
                    if(entry.getValue().size() > context.getSfcRiskRuleConfig().getOrderNum045()){
                        List<OrderRiskContext> collect = entry.getValue().stream()
                                .filter(data -> data.getEstimateKilo().compareTo(new BigDecimal(context.getSfcRiskRuleConfig().getKilo045())) < 0).collect(Collectors.toList());
                        BigDecimal rate = new BigDecimal(collect.size()).divide(new BigDecimal(entry.getValue().size()),2,BigDecimal.ROUND_CEILING);
                        if(rate.compareTo(context.getSfcRiskRuleConfig().getRate045()) >= 0 ){
                            //命中
                            List<String> orderIds = entry.getValue().stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList());
                            log.info("[SfcRuleRisk045Service][doHandler][{}][{}]命中045规则{}", context.getMemberId(), context.getUnionId(), orderIds);
                            distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                            if(StringUtils.isBlank(context.getRuleNo())){
                                context.setRuleNo(ruleNo);
                            } else {
                                context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                            }
                            riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                            if(context.getSfcRiskRuleConfig().getSync045()) {
                                RiskResultDTO dto = new RiskResultDTO(405, "风控不通过045", null, null);
                                context.getUiResult().setData(dto);
                                context.getUiResult().setMsg("风控不通过");
                            } else {
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception e){
            log.error("[SfcRuleRisk045Service][doHandler][{}][{}]报错信息:",context.getMemberId(),context.getUnionId(),e);
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            log.info("[][][][]045最终规则命中信息:{}",context.getRuleNo());
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }


    public static void main(String[] args) {
        String str1 =  "{\"actualDuration\":0,\"actualKilo\":0.00,\"couponAmount\":8.00,\"driverCardNo\":\"苏B6B6S8\",\"endLat\":28.0088450,\"endLng\":120.6996390,\"estimateDuration\":0,\"estimateKilo\":1.80,\"finishTime\":\"2023-05-14 22:06:22\",\"intervalTime\":8,\"memberId\":\"1532988045\",\"orderId\":\"SFC6460U80280061424CF\",\"passengerCellphone\":\"18891153868\",\"startLat\":27.9997340,\"startLng\":120.7028300,\"totalAmount\":4.00,\"unionId\":\"ohmdTtzXHGQab-2Y7hcE3Qyo1ZTI\"}";
        String str2 =  "{\"actualDuration\":0,\"actualKilo\":0.00,\"couponAmount\":3.00,\"driverCardNo\":\"苏B6B6S8\",\"endLat\":28.0096720,\"endLng\":120.6987920,\"estimateDuration\":0,\"estimateKilo\":2.90,\"finishTime\":\"2023-05-14 22:06:41\",\"intervalTime\":7,\"memberId\":\"1532988045\",\"orderId\":\"SFC6460U86A8006142C2A\",\"passengerCellphone\":\"18891153868\",\"startLat\":28.0004340,\"startLng\":120.7025750,\"totalAmount\":9.00,\"unionId\":\"ohmdTtzXHGQab-2Y7hcE3Qyo1ZTI\"}"  ;
        String str3 =  "{\"actualDuration\":0,\"actualKilo\":0.00,\"couponAmount\":3.00,\"driverCardNo\":\"苏B6B6S8\",\"endLat\":28.0075810,\"endLng\":120.6872660,\"estimateDuration\":0,\"estimateKilo\":1.90,\"finishTime\":\"2023-05-14 23:28:21\",\"intervalTime\":9,\"memberId\":\"1532988045\",\"orderId\":\"SFC6460FB7U90061519U2\",\"passengerCellphone\":\"18891153868\",\"startLat\":28.0173610,\"startLng\":120.6904970,\"totalAmount\":9.00,\"unionId\":\"ohmdTtzXHGQab-2Y7hcE3Qyo1ZTI\"}";
        String str4 = "{\"actualDuration\":0,\"actualKilo\":0.00,\"couponAmount\":3.00,\"driverCardNo\":\"苏B6B6S8\",\"endLat\":28.0139480,\"endLng\":120.6866680,\"estimateDuration\":0,\"estimateKilo\":1.10,\"finishTime\":\"2023-05-14 23:27:34\",\"intervalTime\":6,\"memberId\":\"1532988045\",\"orderId\":\"SFC6460FBC88006156835\",\"passengerCellphone\":\"18891153868\",\"startLat\":28.0179820,\"startLng\":120.6905380,\"totalAmount\":9.00,\"unionId\":\"ohmdTtzXHGQab-2Y7hcE3Qyo1ZTI\"}";
        List<OrderRiskContext> orderRiskContextList = new ArrayList<>();
        orderRiskContextList.add(JSONObject.parseObject(str1,OrderRiskContext.class));
        orderRiskContextList.add(JSONObject.parseObject(str2,OrderRiskContext.class));
        orderRiskContextList.add(JSONObject.parseObject(str3,OrderRiskContext.class));
        orderRiskContextList.add(JSONObject.parseObject(str4,OrderRiskContext.class));
        orderRiskContextList.forEach(data->data.setPassengerCellphone(data.getPassengerCellphone().substring(0,7)));
        Map<String,List<OrderRiskContext>> phoneGroup = orderRiskContextList.stream()
                .collect(Collectors.groupingBy(OrderRiskContext::getPassengerCellphone));
        for(Map.Entry<String,List<OrderRiskContext>> entry : phoneGroup.entrySet()){
//            if(entry.getValue().size() > 2){
                List<OrderRiskContext> collect = entry.getValue().stream()
                        .filter(data -> data.getEstimateKilo().compareTo(new BigDecimal(5)) < 0).collect(Collectors.toList());
                BigDecimal rate = new BigDecimal(collect.size()).divide(new BigDecimal(entry.getValue().size()),2,BigDecimal.ROUND_CEILING);
                if(rate.compareTo(new BigDecimal("0.8")) >= 0 ){
                    //命中
                    List<String> orderIds = entry.getValue().stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList());
                    log.info("[SfcRuleRisk045Service][doHandler][{}][{}]命中045规则{}");
                }
            }
        }

}
