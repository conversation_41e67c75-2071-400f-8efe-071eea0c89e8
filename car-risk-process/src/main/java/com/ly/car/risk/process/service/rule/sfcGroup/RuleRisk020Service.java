package com.ly.car.risk.process.service.rule.sfcGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.rule.FilterOrderContext;
import com.ly.car.risk.process.service.rule.FilterRuleChain;
import com.ly.car.risk.process.service.rule.MapIsAllTrue;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 当天司机完单关联相同用户的订单大于2单，且完单距离均小于2公里
 * */
@Service
@Slf4j
public class RuleRisk020Service extends FilterRuleChain {

    private static final String ruleNo = "020";

    @Resource
    private RuleRisk021Service ruleRisk021Service;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Override
    public UiResult doHandler(FilterOrderContext orderContext) {
        log.info("[FilterRuleChain][RuleRisk020Service][][]前置判断已通过，进入规则020判断{}",orderContext.getMemberId(),orderContext.getDriverCardNo());
        this.next(ruleRisk021Service);
        //判断当前是否执行此规则
        if(orderContext.getNeedRuleMap().get(ruleNo) == null && this.nextRule != null){
            return this.nextRule.doHandler(orderContext);
        }

        //没有下个规则就直接返回了
        if(this.nextRule == null && orderContext.getNeedRuleMap().get(ruleNo) == null){
            return orderContext.getUiResult();
        }

        //执行此规则就给个执行标记
        orderContext.getNeedRuleMap().put(ruleNo,true);
        //计算开始时间戳
        List<OrderRiskContext> orderRiskContextList = orderContext.getDriverContextList().stream()
                .filter(context-> DateUtil.string2Date(context.getFinishTime()).after(TimeUtil.currentDay()))
                .collect(Collectors.toList());
        Map<String, List<OrderRiskContext>> collect = orderRiskContextList.stream()
                .filter(context -> StringUtils.isNotBlank(context.getMemberId()) && !context.getMemberId().equals("0"))
                .collect(Collectors.groupingBy(OrderRiskContext::getMemberId));

        for(Map.Entry<String,List<OrderRiskContext>> entry : collect.entrySet()){
            //计算用户下面是否有完单距离都小于2000米的 ,只要有一个大于2000米就让过
            if(entry.getValue().size() > orderContext.getSfcRiskRuleConfig().getOrderNum020()){
                boolean flag = false;
                for(OrderRiskContext context : entry.getValue()){
                    Double distance = CoordUtil.getDistance(context.getStartLng(),context.getStartLat(),context.getEndLng(),context.getEndLat());
                    if(new BigDecimal(distance).compareTo(new BigDecimal(orderContext.getSfcRiskRuleConfig().getDistance020())) > 0){
                        //有一个不满足情况说明是允许的，跳出这组用户，进行下一组用户的判断
                        flag = true;
                        break;
                    }
                }
                if(!flag){
                    String orderIds = StringUtils.join(entry.getValue().stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList()),",");
                    log.info("[RuleRisk018Service][doHandler][{}][{}]命中020规则，司机关联用户为{},关联订单为{}"
                            ,orderContext.getMemberId(),orderContext.getDriverCardNo(),entry.getKey(), JsonUtils.json(orderIds));
                    distributionRiskManageService.addByRuleChain(entry.getValue(),ruleNo,2,1,0,null, RiskLevelEnum.HIGH.getCode());
                    //只要有一组用户满足则直接返回结果
                    UiResult result = UiResult.ok();
                    result.setMsg("风控不通过");
                    RiskResultDTO dto = new RiskResultDTO(405,"风控不通过020",null,null);
                    result.setData(dto);
                    orderContext.setUiResult(result);
                }
//                return result;
            }
        }
        //判断是否已经全部结束，全部结束则返回通过
        if(MapIsAllTrue.isAllTrue(orderContext.getNeedRuleMap()) || this.nextRule == null){
//            UiResult result = UiResult.ok();
//            result.setData("0");
            return orderContext.getUiResult();
        }
        return this.nextRule.doHandler(orderContext);
    }
}
