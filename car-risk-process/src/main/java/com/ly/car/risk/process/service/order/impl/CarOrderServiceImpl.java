package com.ly.car.risk.process.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.api.PublicServiceClient;
import com.ly.car.risk.process.client.CarOrderClient;
import com.ly.car.risk.process.client.IMClient;
import com.ly.car.risk.process.client.OrderClient;
import com.ly.car.risk.process.client.VirtualPhoneApiClient;
import com.ly.car.risk.process.client.model.im.IMMsgRecord;
import com.ly.car.risk.process.model.consts.CarRiskConst;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.model.risk.IMInfo;
import com.ly.car.risk.process.model.risk.UserComplainCache;
import com.ly.car.risk.process.model.risk.UserOrCarFinishCache;
import com.ly.car.risk.process.model.risk.VirtualCallInfo;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderVirtualCallRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderVirtualCallRecord;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.dto.order.OrderTripInfo;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.virtualphoneApi.rsp.CallLogSearchRsp;
import com.ly.sof.utils.common.UUID;
import com.ly.travel.car.ordercore.facade.model.RiskOrder;
import com.ly.travel.car.orderservice.facade.model.opsi.CarOrder;
import com.ly.travel.car.tradecore.facade.response.trade.PenaltyInquiryResponseDTO;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.DriverLocationResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.ly.car.risk.process.utils.OrderUtils.strToInt;

/**
 * Description of CarOrderServiceImpl
 *
 * <AUTHOR>
 * @date 2024/3/11
 * @desc
 */
@Service
public class CarOrderServiceImpl implements CarOrderService {

    private static final FastDateFormat dateTimeFmt = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");


    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private CarOrderClient carOrderClient;

    @Resource
    private OrderClient orderClient;

    @Resource
    private RiskOrderVirtualCallRecordMapper virtualCallRecordMapper;
    @Resource
    private PublicServiceClient publicServiceClient;

    @Resource
    private VirtualPhoneApiClient virtualPhoneApiClient;
    @Resource
    private IMClient imClient;


    @Override
    public CarOrderDetail queryOrderDetail(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return null;
        }
        CarOrder carOrder = carOrderClient.psiDetail(orderId);
        if (null == carOrder) {
            return null;
        }
        return OrderUtils.assembleOrderDetail(carOrder);
    }


    @Override
    public String queryOrderCurrentLocal(String orderId) {
        DriverLocationResponse driverLocationResponse = orderClient.driverLocation(orderId);
        if (null == driverLocationResponse) {
            return null;
        }
        return driverLocationResponse.getLongitude().toString() + ',' + driverLocationResponse.getLatitude().toString();
    }

    @Override
    public List<RiskOrderVirtualCallRecord> queryOrderVirtualRecords(String orderId) {
        List<RiskOrderVirtualCallRecord> records = virtualCallRecordMapper.selectList(new QueryWrapper<RiskOrderVirtualCallRecord>().eq("order_id", orderId));
        return records;
    }

    @Override
    public UserOrCarFinishCache queryUserFinishCache(String memberId) {
        UserOrCarFinishCache cache = new UserOrCarFinishCache(memberId);
        String cacheKey = String.format(CarRiskConst.BIGDATA_FINISH_MEMBERID_KEY, memberId);
        return buildFinishCache(cache, cacheKey);
    }

    @Override
    public UserOrCarFinishCache queryCarFinishCache(String carNum) throws BizException {
        UserOrCarFinishCache cache = new UserOrCarFinishCache(carNum);
        String encryptCarNum = publicServiceClient.encrypt(6, carNum);
        if (StringUtils.isBlank(encryptCarNum)) {
            throw new BizException(-1, "车牌号加密失败");
        }
        String cacheKey = String.format(CarRiskConst.BIGDATA_FINISH_CARNUM_KEY, encryptCarNum);
        return buildFinishCache(cache, cacheKey);
    }

    private UserOrCarFinishCache buildFinishCache(UserOrCarFinishCache cache, String cacheKey) {
        RBucket<String> bucket = redissonClient.getBucket(cacheKey);
        if (!bucket.isExists()) {
            return cache;
        }
        String userRedisCache = bucket.get();
        if (StringUtils.isNotBlank(userRedisCache)) {
            String[] split = userRedisCache.split(",");
            cache.setCancelCount(strToInt(split[1]));
            cache.setCancelBackCnt(strToInt(split[2]));
            cache.setDecisionCount(strToInt(split[3]));
            cache.setFinishCount(strToInt(split[4]));
        }
        return cache;
    }

    @Override
    public UserComplainCache queryUserComplainCache(String unionId) {
        UserComplainCache cache = new UserComplainCache();
        String cacheKey = String.format(CarRiskConst.BIGDATA_COMPLAINT_UNIONID_KEY, unionId);
        RBucket<String> bucket = redissonClient.getBucket(cacheKey);
        if (!bucket.isExists()) {
            return cache;
        }
        String userRedisCache = bucket.get();
        if (StringUtils.isNotBlank(userRedisCache)) {
            String[] split = userRedisCache.split(",");
            cache.setTsCount(strToInt(split[0]));
            cache.setTsCountOther(strToInt(split[1]));
        }
        return cache;
    }

    @Override
    public VirtualCallInfo queryVirtualCallInfo(String orderId, String driverVirtualPhone) {
        VirtualCallInfo virtualCallInfo = new VirtualCallInfo();
        List<CallLogSearchRsp> callRecords = virtualPhoneApiClient.search(orderId, driverVirtualPhone);
        if (CollUtil.isEmpty(callRecords)) {
            return virtualCallInfo;
        }
        CallLogSearchRsp lastCall = callRecords.stream().sorted(Comparator.comparing(CallLogSearchRsp::getReleaseTime).reversed()).findFirst().get();
        // releaseCause为空或者为16(正常的呼叫拆线) 当作成功处理
        int sucCount = (int) callRecords.stream().filter(p -> StringUtils.isBlank(p.getReleaseCause()) || p.getReleaseCause().equals("16")
                || p.getReleaseCause().equals("31")).count();
        virtualCallInfo.setLatestCallTime(lastCall.getReleaseTime());
        virtualCallInfo.setSucCnt(sucCount);
        virtualCallInfo.setFailCnt(callRecords.size() - sucCount);
        long totalCallMillSeconds = 0;
        // 通话总时长
        for (CallLogSearchRsp callRecord : callRecords) {
            if (StringUtils.isBlank(callRecord.getCallTime()) || StringUtils.isBlank(callRecord.getReleaseTime())) {
                continue;
            }
            try {
                Date callTime = dateTimeFmt.parse(callRecord.getCallTime());
                Date releaseTime = dateTimeFmt.parse(callRecord.getReleaseTime());
                if (callTime.before(OrderUtils.limitTime) || releaseTime.before(OrderUtils.limitTime)) {
                    continue;
                }
                totalCallMillSeconds += (releaseTime.getTime() - callTime.getTime());
            } catch (ParseException e) {
                // ignore
            }
        }
        virtualCallInfo.setTotalCallMinutes(totalCallMillSeconds / 60000.0f);
        return virtualCallInfo;
    }

    @Override
    public IMInfo queryImInfo(String orderId, String supplierCode, String unionId, String memberId, String carNum) {
        IMInfo imInfo = new IMInfo();
        List<IMMsgRecord> imRecords = imClient.queryIMMsg(orderId, supplierCode, unionId, memberId, carNum);
        if (CollUtil.isEmpty(imRecords)) {
            return imInfo;
        }
        int userSendCount = 0, userReadCount = 0, driverSendCount = 0, driverReadCount = 0;

        for (IMMsgRecord record : imRecords) {
            if (Objects.equals(record.getMsgSender(), 1)) {
                //乘客发
                if (Objects.equals(record.getMsgSendStatus(), 1)) {
                    userSendCount++;
                }
                if (Objects.equals(record.getMsgStatus(), 1)) {
                    driverReadCount++;
                }
            } else if (Objects.equals(record.getMsgSender(), 2)) {
                //司机发
                if (Objects.equals(record.getMsgSendStatus(), 1)) {
                    driverSendCount++;
                }
                if (Objects.equals(record.getMsgStatus(), 1)) {
                    userReadCount++;
                }
            }
        }

        imInfo.setUserMsgSend(userSendCount);
        imInfo.setUserMsgRead(userReadCount);
        imInfo.setDriverMsgSend(driverSendCount);
        imInfo.setDriverMsgRead(driverReadCount);
        return imInfo;
    }

    @Override
    public String queryPenaltyInquiry(String orderId, String memberId) {
        PenaltyInquiryResponseDTO penaltyInquiryResponseDTO = carOrderClient.penaltyInquiry(memberId, orderId, UUID.generateRandomUUID());
        if (null == penaltyInquiryResponseDTO) {
            return "0";
        }
        return penaltyInquiryResponseDTO.getOriginPenaltyAmount();
    }

    @Override
    public RiskOrder queryOrderDetail(String orderId, String traceId) {
        if (StringUtils.isBlank(orderId)) {
            return null;
        }
        RiskOrder carOrder = carOrderClient.simpleOrderDetail(orderId,traceId);
        if (null == carOrder) {
            return null;
        }
        return carOrder;
    }
}