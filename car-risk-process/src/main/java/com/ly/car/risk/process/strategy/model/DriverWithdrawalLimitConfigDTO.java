package com.ly.car.risk.process.strategy.model;

import lombok.Data;

import java.util.List;

/**
 * @author: fulin.li
 * @create-date: 2025/9/12 17:18
 */
@Data
public class DriverWithdrawalLimitConfigDTO {

    private List<String> sceneList;

    /**
     * 冻结截止时间
     */
    private Integer freezeTime;

    /**
     * 原因
     */
    private String reason = "近期频繁提现";

    private Integer offsetHour;

    private Integer withdrawalNum;

}
