package com.ly.car.risk.process.turboMQ;
/***
 * topic是name
 * */
public enum MqTagEnum {

    car_risk_hitch_notify("notifyRegister","司机认证"),
    risk_order_appeal_approved("appealApproved","司机申诉通知"),
    risk_order_appeal_frozen("appealApproved","司机冻结通知"),
    car_risk_driver_inner_content_notify("car_risk_driver_inner_content_notify","司机端文字提示"),
    car_risk_driver_warn("driverWarn","司机端安全预警"),
    car_risk_work_order("workerOrder","外部违规"),
    car_risk_binlog_sfc_cancel("sfcLong","顺风车创单log"),
    car_risk_safe_warning("safeWarning","安全预警缓存预估路径"),
    car_risk_place_order_phone("place_order","用户下单"),
    car_risk_sfc_user_onCar("oncar","用户确认上车"),
    car_risk_sfc_user_call_record("callRecord","确认用户外呼情况"),

    car_risk_convert_sfc_data("sfcConvertData","顺风车数据转换,除中间态"),
    car_risk_mt_notify("mtNotify","萌艇返回"),
    car_risk_mt_warn("mtWarn","萌艇司机端安全预警"),

    car_risk_ht_notify("htNotify","慧瞳车主认证通知"),

    car_risk_order_event_topic("sfcOrderEvent","顺风车事件整合"),
    car_risk_driver_ex_notify("sfcDriverNotify","车主端异常通知"),
    car_risk_driver_mini_warn_notify("miniSfcWarnNotify","萌艇小程序端风险通知乘客端"),

    //self
    car_risk_self_security_task("selfSecurityTask","接单后监听通话记录并去重"),
    car_self_tencent_get_describe_task("tencentDescribeTask","获取录音文件转文字tag"),
    car_risk_self_auto_call_task("autoCallTask","自动外呼"),
    car_risk_self_auto_call_check_task("autoCallTask","自动外呼检查接口"),
    car_risk_Self_on_car_sms_send("sendOnCarSms","检查外呼失败则发送短信"),
    car_risk_warn_record("riskWarnRecord","安全预警监控"),

    //SFC
    car_risk_sfc_text("sfcText","顺风车虚拟号转文字"),

    car_risk_order_sync_mng("carRiskOrderSync","风险订单同步后台"),
    car_risk_real_car_owner_ht_tag("car_risk_real_car_owner_ht_tag","真车主风控单同步"),
    car_risk_real_car_owner_driver_withdrawal_tag("car_risk_real_car_owner_driver_withdrawal_tag","真车主提现次数限制mq tag")
    ;

    public String tag;
    public String msg;

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    MqTagEnum(String tag, String msg){
        this.tag = tag;
        this.msg = msg;
    }

    public static MqTagEnum getTagName(String productLine){
        if(productLine.equals("MT")){
            return MqTagEnum.car_risk_mt_notify;
        } else if(productLine.equals("HT")){
            return MqTagEnum.car_risk_ht_notify;
        } else  {
            return MqTagEnum.car_risk_hitch_notify;
        }
    }

    public static MqTagEnum getTagWarnNotify(String productLine){
        return productLine.equals("MT")?MqTagEnum.car_risk_mt_warn:MqTagEnum.car_risk_driver_warn;
    }

}
