package com.ly.car.risk.process.constants;

import java.util.*;

public class SceneToCustomerType {

    public static Map<String, List<Integer>> yncCustomerTypeMap;
    public static Map<String, List<Integer>> sfcCustomerTypeMap;
    public static Map<String, List<Integer>> hcCustomerTypeMap;

    static {
        /**
         *  1-黑名单 2-白名单 3-禁止领券名单 4-禁止奖励名单 5-禁止派单名单 6-禁止接单名单 7-1V1
         * */
        Map<String, List<Integer>> sfcMap = new HashMap<>();
        //司机接单
        sfcMap.put("2-1", Arrays.asList(1,2,6,7));
        //用户预定
        sfcMap.put("2-2", Arrays.asList(1,2,5));
        //面对面
        sfcMap.put("2-10", Arrays.asList(1,2,5));
        sfcCustomerTypeMap = sfcMap;

        Map<String,List<Integer>> hcMap = new HashMap<>();
        hcMap.put("7-2",Arrays.asList(8));
        hcMap.put("7-4",Arrays.asList(1));
        hcMap.put("7-5",Arrays.asList(1));
        hcMap.put("7-8",Arrays.asList(1));
        hcCustomerTypeMap = hcMap;


    }
}
