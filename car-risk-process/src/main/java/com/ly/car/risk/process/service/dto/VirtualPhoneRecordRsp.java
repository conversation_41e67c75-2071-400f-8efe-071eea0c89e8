package com.ly.car.risk.process.service.dto;

import lombok.Data;

import java.util.List;

@Data
public class VirtualPhoneRecordRsp {

    private String Code;
    private String Message;
    private List<PhoneData> data;

    @Data
    public static class PhoneData{
        private String callingNo;
        private String calledNo;
        private String releaseTime;
        private String startTime;
        private String callTime;
        private Integer releaseCause;
        private Integer callType;
        private String callId;
        private Long reportId;
    }
}
