package com.ly.car.risk.process.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
@ColumnWidth(33)
public class ChannelRiskBadDebtsDTO {

    @ExcelProperty(value = "城市名称")
    private String cityName;

    @ExcelProperty(value = "供应商")
    private String supplierName;

    @ExcelProperty(value = "上游平台")
    private String platform;

    @ExcelProperty(value = "时间")
    private String time;

    @ExcelProperty(value = "坏账订单数")
    private String badNum;

    @ExcelProperty(value = "坏账金额")
    private String badAmount;

    @ExcelProperty(value = "当前坏账订单占比")
    private String currBadOrderRate;

    @ExcelProperty(value = "当前坏账金额占比")
    private String currBadAmountRate;

    @ExcelProperty(value = "近7天坏账订单占比")
    private String sevenBadOrderRate;

    @ExcelProperty(value = "近7天坏账金额")
    private String sevenBadAmount;

    @ExcelProperty(value = "近7天坏账金额占比")
    private String sevenBadAmountRate;

    @ExcelProperty(value = "近15天坏账订单占比")
    private String fifteenBadOrderRate;

    @ExcelProperty(value = "近15天坏账金额")
    private String fifteenBadAmount;

    @ExcelProperty(value = "近15天坏账金额占比")
    private String fifteenBadAmountRate;

    @ExcelProperty(value = "近30天坏账订单占比")
    private String thirtyBadOrderRate;

    @ExcelProperty(value = "近30天坏账金额")
    private String thirtyBadAmount;

    @ExcelProperty(value = "近30天坏账金额占比")
    private String thirtyBadAmountRate;


}
