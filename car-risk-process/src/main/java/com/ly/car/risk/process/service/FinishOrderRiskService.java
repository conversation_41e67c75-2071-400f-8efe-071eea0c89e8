package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.process.client.SupplierApiClient;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.api.MemberApiClient;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.DistributionRiskManageMapper;
import com.ly.car.risk.process.service.dto.SpecialCarRiskConfig;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.sharding.order.entity.risk.DcdbOrderFinishRisk;
import com.ly.car.sharding.order.mappers.dcdb.DcdbOrderDataMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.spat.dsf.utils.StringUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FinishOrderRiskService {

    @Resource
    private DcdbOrderDataMapper          dcdbOrderDataMapper;
    @Resource
    private SupplierApiClient            supplierApiClient;
    @Resource
    private DistributionRiskManageMapper distributionRiskManageMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private MemberApiClient memberApiClient;
    @Resource
    private RiskOrderManageService riskOrderManageService;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    public String action(){

        Date start = DateUtil.addDay(new Date(),-1);
        Date end = new Date();
        Date zeroDate = DateUtil.string2Date(DateUtil.date2String(new Date(),DateUtil.DATE_PATTERN_YYYY_MM_DD)+" 00:00:00",DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS);
        Date hourDate = DateUtil.string2Date(DateUtil.date2String(DateUtil.addHour(new Date(),-1)),DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS);
        //查询24小时的订单
        List<DcdbOrderFinishRisk> dcdbOrderFinishRisks = dcdbOrderDataMapper.queryFinishOrder(start, end, null);
        log.info("[FinishOrderRiskService][action][][]全部订单:{},第一条订单{}",dcdbOrderFinishRisks.size(), JSONObject.toJSONString(dcdbOrderFinishRisks.get(0)));
        if(dcdbOrderFinishRisks == null || dcdbOrderFinishRisks.size() == 0){
            return "success";
        }
        //查询当天的
        List<DcdbOrderFinishRisk> sfcOrderList = sfcOrderMapper.getSfcFinishOrder(start,end);
        log.info("[FinishOrderRiskService][action][][]全部顺风车订单:{},第一条订单{}",sfcOrderList.size(), JSONObject.toJSONString(sfcOrderList.get(0)));
        //过滤出订单号
        List<String> orderIds = dcdbOrderFinishRisks.stream().map(DcdbOrderFinishRisk::getOrderId).collect(Collectors.toList());
        //过滤出1小时的订单
        List<DcdbOrderFinishRisk> hourFinishRisks = dcdbOrderFinishRisks.stream().filter(e->e.getCreateTime().after(hourDate)).collect(Collectors.toList());
        //过滤当天的订单
//        List<DcdbOrderFinishRisk> zeroFinishRisks = dcdbOrderFinishRisks.stream().filter(e->e.getCreateTime().after(zeroDate)).collect(Collectors.toList());
        //对这些订单进行分组
        //支付账号分组,24小时
        Map<String,List<DcdbOrderFinishRisk>> payAccountMap = convertPayAccountMap(dcdbOrderFinishRisks);
        //用户维度分组
        Map<String,List<DcdbOrderFinishRisk>> byUserMapHour = convertUserMap(hourFinishRisks);
        Map<String,List<DcdbOrderFinishRisk>> byUserMap = convertUserMap(dcdbOrderFinishRisks);
        log.info("[FinishOrderRiskService][action][][]用户维度分组:{}",byUserMap.size());
        //设备维度分组
        Map<String,List<DcdbOrderFinishRisk>> byDeviceMapHour = convertDeviceMap(hourFinishRisks);
        Map<String,List<DcdbOrderFinishRisk>> byDeviceMap = convertDeviceMap(dcdbOrderFinishRisks);

        //顺风车分组
        Map<String,List<DcdbOrderFinishRisk>> byUserSfcMap = convertUserSfcMap(sfcOrderList);
        //有哪些是需要插入的给个map
        Map<String, DistributionRiskManage> insertMap = new HashMap<>();
        //获取统一配置
        SpecialCarRiskConfig riskConfig = getRiskConfig();
        //010策略
        setManage010(dcdbOrderFinishRisks,insertMap);
        //先012策略
        setManage012(payAccountMap,insertMap,riskConfig);
        //013策略
        setManage013(byDeviceMapHour,byUserMapHour,insertMap,riskConfig);
        //014策略
        setManage014(byUserMap,insertMap,riskConfig);
        //015策略
        setManage015(byUserMap,insertMap,byUserSfcMap,riskConfig);
        //016
        setManage016(byUserMap,insertMap,riskConfig);
        //017
        setManage017(byDeviceMap,insertMap,riskConfig);
        List<String> manageOrderIds = insertMap.values().stream().map(DistributionRiskManage::getOrderId).collect(Collectors.toList());
        //查一下24小时的风控订单表看看当前订单有没有被风控，主要是为了重复风控
        List<DistributionRiskManage> distributionRiskManages = distributionRiskManageMapper.selectList(
                new QueryWrapper<DistributionRiskManage>().in("order_id",manageOrderIds)
                        .eq("main_scene",3)
                        .eq("child_scene",4)
        );
        Map<String,DistributionRiskManage> haveManageMap = new HashMap<>();
        if(distributionRiskManages != null && distributionRiskManages.size() > 0){
            haveManageMap = distributionRiskManages.stream().collect(Collectors.toMap(DistributionRiskManage::getOrderId,v->v,(old,cur)->old));
        }
        for(Map.Entry<String,DistributionRiskManage> map : insertMap.entrySet()){
            DistributionRiskManage manage = map.getValue();
            String ruleNoList = manage.getRuleNoList();
            //已有的
            DistributionRiskManage haveManage = haveManageMap.get(manage.getOrderId());
            if(org.apache.commons.lang3.StringUtils.isNotBlank(ruleNoList)){
                //去个重
                String linkOrderIds = manage.getLinkOrder();
                if(StringUtils.isNotBlank(linkOrderIds)){
                    List<String> stringList = Arrays.asList(linkOrderIds.split(",")).stream().distinct().collect(Collectors.toList());
                    String finalStr = org.apache.commons.lang3.StringUtils.join(stringList,",");
                    if(finalStr.length() > 1000){
                        manage.setLinkOrder(finalStr.substring(0,1000));
                    } else {
                        manage.setLinkOrder(finalStr);
                    }
                }
                RiskOrderManage orderManage = new RiskOrderManage();
                orderManage.setOrderId(manage.getOrderId());
                orderManage.setIsRisk(1);
                orderManage.setRiskTypeName("专车场景");
                orderManage.setRuleNo(manage.getRuleNoList());
//                if(haveManage != null && haveManage.getId() > 0){
                    distributionRiskManageService.addByRule(manage.getOrderId(),manage.getRuleNoList(),manage.getMainScene(),
                            manage.getChildScene(),manage.getLinkOrder(),null,manage.getRiskLevel());
//                    haveManage.setRuleNoList(manage.getRuleNoList());
//                    haveManage.setLinkOrder(manage.getLinkOrder());
//                    distributionRiskManageMapper.updateById(haveManage);
//                    riskOrderManageService.addRiskOrder(orderManage);
//                } else {
                    //插入
//                    if(StringUtils.isBlank(manage.getMemberId()) || manage.getMemberId().equals("0")){
//                        continue;
//                    }
//                    MemberQueryResponse infoByMemberId = memberApiClient.getInfoByMemberId(manage.getMemberId());
//                    if(infoByMemberId != null && infoByMemberId.getData() != null){
//                        manage.setUserPhone(infoByMemberId.getData().getMobile());
//                    }
//                    distributionRiskManageMapper.insert(manage);
//                    riskOrderManageService.addRiskOrder(orderManage);
//                    distributionRiskManageService.addByRule(manage.getOrderId(),manage.getRuleNoList(),manage.getMainScene(),
//                            manage.getChildScene(),manage.getLinkOrder(),null);
//                }
            }
        }
        return "success";
    }

    /**
     * 010策略 当前订单关联用户命中风险标签（机器行为、聚集小号）
     * */
    public void setManage010(List<DcdbOrderFinishRisk> dcdbOrderFinishRisks,Map<String,DistributionRiskManage> insertMap){
        dcdbOrderFinishRisks = dcdbOrderFinishRisks.stream().filter(e->e.getOutRiskTag() > 0).collect(Collectors.toList());
        dcdbOrderFinishRisks.forEach(risk->{
            DistributionRiskManage manage;
            if(insertMap.get(risk.getOrderId()) != null){
                manage = insertMap.get(risk.getOrderId());
            } else {
                String supplierCode = StringUtils.isNotBlank(risk.getSupplierCodeFull())?risk.getSupplierCodeFull(): risk.getSupplierCode();
                if(supplierCode.startsWith("MadaSaas")){
                    supplierCode = supplierCode.split("_")[0];
                }
                manage = setManage(supplierCode,risk);
            }
//            if(StringUtils.isNotBlank(manage.getLinkOrder())){
//                manage.setLinkOrder(manage.getLinkOrder() +","+ risk.getOrderId());
//            } else {
//                manage.setLinkOrder(risk.getOrderId());
//            }
            if(StringUtils.isNotBlank(manage.getRuleNoList())){
                if(!manage.getRuleNoList().contains("010")) {
                    manage.setRuleNoList(manage.getRuleNoList() + "," + "010");
                }
            } else {
                manage.setRuleNoList("010");
            }
            insertMap.put(risk.getOrderId(),manage);
        });
    }

    /**
    * 017号策略 近24小时内，当前用户下单设备号关联完单大于3单，且乘车人手机号大于等于3个
    * */
    public void setManage017(Map<String,List<DcdbOrderFinishRisk>> byDeviceMap,Map<String,DistributionRiskManage> insertMap,
                             SpecialCarRiskConfig riskConfig){
        for(Map.Entry<String,List<DcdbOrderFinishRisk>> entry : byDeviceMap.entrySet()){
            List<DcdbOrderFinishRisk> riskList = entry.getValue();
            if(riskList.size() < riskConfig.getOrderNum017()){
                continue;
            }
            //上面是设备分完组，
            List<String> phoneList = riskList.stream().map(DcdbOrderFinishRisk::getPassengerCellphone).distinct().collect(Collectors.toList());
            if(phoneList.size() >= riskConfig.getOrderNum017()){
                riskList.stream().forEach(risk->{
                    DistributionRiskManage manage;
                    if(insertMap.get(risk.getOrderId()) != null){
                        manage = insertMap.get(risk.getOrderId());
                    } else {
                        String supplierCode = StringUtils.isNotBlank(risk.getSupplierCodeFull())?risk.getSupplierCodeFull(): risk.getSupplierCode();
                        if(supplierCode.startsWith("MadaSaas")){
                            supplierCode = supplierCode.split("_")[0];
                        }
                        manage = setManage(supplierCode,risk);
                    }
                    List<String> orderIds = riskList.stream().map(DcdbOrderFinishRisk::getOrderId).collect(Collectors.toList());
                    orderIds.remove(risk.getOrderId());
                    if(StringUtils.isNotBlank(manage.getLinkOrder())){
                        manage.setLinkOrder(manage.getLinkOrder() +","+ org.apache.commons.lang3.StringUtils.join(orderIds,","));
                    } else {
                        manage.setLinkOrder(org.apache.commons.lang3.StringUtils.join(orderIds,","));
                    }
                    if(StringUtils.isNotBlank(manage.getRuleNoList())){
                        if(!manage.getRuleNoList().contains("017")) {
                            manage.setRuleNoList(manage.getRuleNoList() + "," + "017");
                        }
                    } else {
                        manage.setRuleNoList("017");
                    }
                    insertMap.put(risk.getOrderId(),manage);
                });
            }
        }
    }

   /**
    * 016号策略 当前用户的订单里程小于3公里，且近24小时里程小于3公里的订单数>=10单
    * */
   public void setManage016(Map<String,List<DcdbOrderFinishRisk>> byUserMap,Map<String,DistributionRiskManage> insertMap,
                            SpecialCarRiskConfig riskConfig){
        for(Map.Entry<String,List<DcdbOrderFinishRisk>> entry : byUserMap.entrySet()){
            List<DcdbOrderFinishRisk> entryList = entry.getValue();
            if(entryList.size() < riskConfig.getOrderNum016()){
                continue;
            }
            List<DcdbOrderFinishRisk> newList = entryList.stream().filter(e->e!=null).filter(e->e.getDistance() != null)
                    .filter(e->e.getDistance().compareTo(new BigDecimal(riskConfig.getDistance016()))< 0).collect(Collectors.toList());
            if(newList != null && newList.size() >= riskConfig.getOrderNum016()){
                newList.stream().forEach(risk->{
                    DistributionRiskManage manage;
                    if(insertMap.get(risk.getOrderId()) != null){
                        manage = insertMap.get(risk.getOrderId());
                    } else {
                        String supplierCode = StringUtils.isNotBlank(risk.getSupplierCodeFull())?risk.getSupplierCodeFull(): risk.getSupplierCode();
                        if(supplierCode.startsWith("MadaSaas")){
                            supplierCode = supplierCode.split("_")[0];
                        }
                        manage = setManage(supplierCode,risk);
                    }
                    List<String> orderIds = newList.stream().map(DcdbOrderFinishRisk::getOrderId).collect(Collectors.toList());
                    orderIds.remove(risk.getOrderId());
                    if(StringUtils.isNotBlank(manage.getLinkOrder())){
                        manage.setLinkOrder(manage.getLinkOrder() +","+ org.apache.commons.lang3.StringUtils.join(orderIds,","));
                    } else {
                        manage.setLinkOrder(org.apache.commons.lang3.StringUtils.join(orderIds,","));
                    }
                    if(StringUtils.isNotBlank(manage.getRuleNoList())){
                        if(!manage.getRuleNoList().contains("016")) {
                            manage.setRuleNoList(manage.getRuleNoList() + "," + "016");
                        }
                    } else {
                        manage.setRuleNoList("016");
                    }
                    insertMap.put(risk.getOrderId(),manage);
                });
            }
        }
   }

    /**
     *  当前用户订单完单时间与上笔订单完单时间间隔小于6分钟，且该用户近24小时内间隔6分钟内的订单>=3单
     *  代码与014策略类似，可以考虑写在一块儿，但是当前直接隔离开会比较好
     * */
    public void setManage015(Map<String,List<DcdbOrderFinishRisk>> byUserMap,Map<String,DistributionRiskManage> insertMap,
                             Map<String,List<DcdbOrderFinishRisk>> byUserSfcMap,
                             SpecialCarRiskConfig riskConfig){
        for(Map.Entry<String,List<DcdbOrderFinishRisk>> entry : byUserMap.entrySet()){
            //按照时间排序
            List<DcdbOrderFinishRisk> entryList = entry.getValue().stream()
                    .sorted(Comparator.comparing(DcdbOrderFinishRisk::getFinishTime,Comparator.nullsFirst(Comparator.reverseOrder()))).collect(Collectors.toList());
            if(entryList != null && entryList.size() < riskConfig.getOrderNum015()){
                continue;
            }
            Set<DcdbOrderFinishRisk> linkOrder = new HashSet<>();
//            Map<String,DcdbOrderFinishRisk> riskMap = new HashMap<>();
            //倒序的排序，第一个时间是最大的
            for(DcdbOrderFinishRisk riskFirst : entryList){
                for(DcdbOrderFinishRisk riskSec : entryList){
                    //内循环的时间大于外循环的时间且差值大于6分钟
                    if(riskSec.getFinishTime().after(riskFirst.getFinishTime())
                            && DateUtil.addMinute(riskFirst.getFinishTime(),riskConfig.getTimeInterval015()).before(riskSec.getFinishTime())){
                        continue;
                    }
                    //内循环的时间小于外循环的时间且差值大于6分钟
                    if(riskSec.getFinishTime().before(riskFirst.getFinishTime())
                            && DateUtil.addMinute(riskSec.getFinishTime(),riskConfig.getTimeInterval015()).before(riskFirst.getFinishTime())){
                        continue;
                    }
                    //自己和自己不用比较
                    if(riskFirst.getOrderId().equals(riskSec.getOrderId())){
                        continue;
                    }
                    linkOrder.add(riskFirst);
                    linkOrder.add(riskSec);
                    //这边用map就不用
//                    riskMap.put(riskFirst.getOrderId(),riskFirst);
//                    riskMap.put(riskSec.getOrderId(),riskSec);
                }
            }
            //条件就是要大于3，所以这边直接过滤
            if(linkOrder.size() >= riskConfig.getOrderNum015()){
                List<DcdbOrderFinishRisk> risks = new ArrayList<>(linkOrder);
                risks.stream().forEach(risk->{
                    DistributionRiskManage manage;
                    if(insertMap.get(risk.getOrderId()) != null){
                        manage = insertMap.get(risk.getOrderId());
                    } else {
                        String supplierCode = StringUtils.isNotBlank(risk.getSupplierCodeFull())?risk.getSupplierCodeFull(): risk.getSupplierCode();
                        if(supplierCode.startsWith("MadaSaas")){
                            supplierCode = supplierCode.split("_")[0];
                        }
                        manage = setManage(supplierCode,risk);
                    }
                    List<String> orderIds = risks.stream().map(DcdbOrderFinishRisk::getOrderId).collect(Collectors.toList());
                    orderIds.remove(risk.getOrderId());//剩下的就是关联订单
                    if(StringUtils.isNotBlank(manage.getLinkOrder())){
                        manage.setLinkOrder(manage.getLinkOrder() +","+ org.apache.commons.lang3.StringUtils.join(orderIds,","));
                    } else {
                        manage.setLinkOrder(org.apache.commons.lang3.StringUtils.join(orderIds,","));
                    }
                    if(StringUtils.isNotBlank(manage.getRuleNoList())){
                        if(!manage.getRuleNoList().contains("015")) {
                            manage.setRuleNoList(manage.getRuleNoList() + "," + "015");
                        }
                    } else {
                        manage.setRuleNoList("015");
                    }
                    insertMap.put(risk.getOrderId(),manage);
                });
            }
        }

        //再搞下顺风车的018
        for(Map.Entry<String,List<DcdbOrderFinishRisk>> entry : byUserSfcMap.entrySet()){
            //按照时间排序
            List<DcdbOrderFinishRisk> entryList = entry.getValue().stream()
                    .sorted(Comparator.comparing(DcdbOrderFinishRisk::getFinishTime,Comparator.nullsFirst(Comparator.reverseOrder()))).collect(Collectors.toList());
            if(entryList != null && entryList.size() < riskConfig.getOrderNum018()){
                continue;
            }
            Set<DcdbOrderFinishRisk> linkOrder = new HashSet<>();
//            Map<String,DcdbOrderFinishRisk> riskMap = new HashMap<>();
            //倒序的排序，第一个时间是最大的
            for(DcdbOrderFinishRisk riskFirst : entryList){
                for(DcdbOrderFinishRisk riskSec : entryList){
                    //内循环的时间大于外循环的时间且差值大于6分钟
                    if(riskSec.getFinishTime().after(riskFirst.getFinishTime())
                            && DateUtil.addMinute(riskFirst.getFinishTime(),riskConfig.getTimeInterval018()).before(riskSec.getFinishTime())){
                        continue;
                    }
                    //内循环的时间小于外循环的时间且差值大于6分钟
                    if(riskSec.getFinishTime().before(riskFirst.getFinishTime())
                            && DateUtil.addMinute(riskSec.getFinishTime(),riskConfig.getTimeInterval018()).before(riskFirst.getFinishTime())){
                        continue;
                    }
                    //自己和自己不用比较
                    if(riskFirst.getOrderId().equals(riskSec.getOrderId())){
                        continue;
                    }
                    linkOrder.add(riskFirst);
                    linkOrder.add(riskSec);
                }
            }
            if(!linkOrder.isEmpty()){
                log.info("[][][][][]当前用户"+entry.getKey()+"过滤后的订单set为:"+JSONObject.toJSONString(linkOrder));
            }

            //条件就是要大于3，所以这边直接过滤
            if(linkOrder.size() >= riskConfig.getOrderNum018()){
                List<DcdbOrderFinishRisk> risks = new ArrayList<>(linkOrder);
                risks.stream().forEach(risk->{
                    DistributionRiskManage manage;
                    if(insertMap.get(risk.getOrderId()) != null){
                        manage = insertMap.get(risk.getOrderId());
                    } else {
                        String supplierCode = risk.getSupplierCode();
                        if(supplierCode.startsWith("MadaSaas")){
                            supplierCode = supplierCode.split("_")[0];
                        }
                        manage = setManage(supplierCode,risk);
                    }
                    List<String> orderIds = risks.stream().map(DcdbOrderFinishRisk::getOrderId).collect(Collectors.toList());
                    orderIds.remove(risk.getOrderId());//剩下的就是关联订单
                    if(StringUtils.isNotBlank(manage.getLinkOrder())){
                        manage.setLinkOrder(manage.getLinkOrder() +","+ org.apache.commons.lang3.StringUtils.join(orderIds,","));
                    } else {
                        manage.setLinkOrder(org.apache.commons.lang3.StringUtils.join(orderIds,","));
                    }
                    if(StringUtils.isNotBlank(manage.getRuleNoList())){
                        if(!manage.getRuleNoList().contains("018")) {
                            manage.setRuleNoList(manage.getRuleNoList() + "," + "018");
                        }
                    } else {
                        manage.setRuleNoList("018");
                    }
                    insertMap.put(risk.getOrderId(),manage);
                });
            }
        }
    }

    /**
     * 014策略 近24小时内，当前用户订单的起点与上笔订单的终点距离小于500米，且两笔订单的接单司机一致,这个就需要把所有订单都要匹配一次了
     * */
    public void setManage014(Map<String,List<DcdbOrderFinishRisk>> byUserMap,Map<String,DistributionRiskManage> insertMap,SpecialCarRiskConfig riskConfig){
        for(Map.Entry<String,List<DcdbOrderFinishRisk>> entry : byUserMap.entrySet()){
            //按照时间排序
            List<DcdbOrderFinishRisk> entryList = entry.getValue().stream()
                    .sorted(Comparator.comparing(DcdbOrderFinishRisk::getFinishTime,Comparator.nullsFirst(Comparator.reverseOrder()))).collect(Collectors.toList());
            if(entryList.size() < 2){
                continue;
            }
            for(int i=0;i<entryList.size()-1;i++){
                DcdbOrderFinishRisk firstRisk = entryList.get(i);
                DcdbOrderFinishRisk secondRisk = entryList.get(i+1);
                if(!firstRisk.getPlateNumber().equals(secondRisk.getPlateNumber())){
                    continue;
                }
                //计算两个重点距离
                double distance = CoordUtil.getDistance(String.valueOf(firstRisk.getEndLng()),String.valueOf(firstRisk.getEndLat()),
                        String.valueOf(secondRisk.getStartLng()),String.valueOf(secondRisk.getStartLat()));
                if(new BigDecimal(distance).compareTo(new BigDecimal(riskConfig.getDistance014())) < 0){
                    //这个时候满足策略014
                    String supplierCode1 = StringUtils.isNotBlank(firstRisk.getSupplierCodeFull())? firstRisk.getSupplierCodeFull() : firstRisk.getSupplierCode();
                    if(supplierCode1.startsWith("MadaSaas")){
                        supplierCode1 = supplierCode1.split("_")[0];
                    }
                    String supplierCode2 = StringUtils.isNotBlank(secondRisk.getSupplierCodeFull())? secondRisk.getSupplierCodeFull() : secondRisk.getSupplierCode();
                    if(supplierCode2.startsWith("MadaSaas")){
                        supplierCode2 = supplierCode2.split("_")[0];
                    }
                    DistributionRiskManage manage1;
                    if(insertMap.get(firstRisk.getOrderId()) != null){
                        manage1 = insertMap.get(firstRisk.getOrderId());
                    } else {
                        manage1 = setManage(supplierCode1,firstRisk);
                    }

                    if(StringUtils.isNotBlank(manage1.getRuleNoList())){
                        if(!manage1.getRuleNoList().contains("014")) {
                            manage1.setRuleNoList(manage1.getRuleNoList() + "," + "014");
                        }
                    } else {
                        manage1.setRuleNoList("014");
                    }
                    if(StringUtils.isNotBlank(manage1.getLinkOrder())){
                        manage1.setLinkOrder(manage1.getLinkOrder() +","+ secondRisk.getOrderId());
                    } else {
                        manage1.setLinkOrder(secondRisk.getOrderId());
                    }
                    insertMap.put(manage1.getOrderId(),manage1);

                    //关联单也命中
                    DistributionRiskManage manage2;
                    if(insertMap.get(secondRisk.getOrderId()) != null){
                        manage2 = insertMap.get(secondRisk.getOrderId());
                    } else {
                        manage2 = setManage(supplierCode2,secondRisk);
                    }

                    if(StringUtils.isNotBlank(manage2.getRuleNoList())){
                        if(!manage2.getRuleNoList().contains("014")) {
                            manage2.setRuleNoList(manage2.getRuleNoList() + "," + "014");
                        }
                    } else {
                        manage2.setRuleNoList("014");
                    }
                    if(StringUtils.isNotBlank(manage2.getLinkOrder())){
                        manage2.setLinkOrder(manage2.getLinkOrder() +","+ firstRisk.getOrderId());
                    } else {
                        manage2.setLinkOrder(firstRisk.getOrderId());
                    }
                    insertMap.put(manage2.getOrderId(),manage2);
                }
            }
        }
    }


    /**
     * 013策略 当前订单关联设备ID（unionid）近1小时内完单数量大于等于4单，且接单司机一致
     * */
    public void setManage013(Map<String,List<DcdbOrderFinishRisk>> byDeviceMapHour,Map<String,List<DcdbOrderFinishRisk>> byUserMapHour,
                             Map<String,DistributionRiskManage> insertMap,SpecialCarRiskConfig riskConfig){
        for(Map.Entry<String,List<DcdbOrderFinishRisk>> entry : byDeviceMapHour.entrySet()){
            List<DcdbOrderFinishRisk> riskList = entry.getValue();
            if(riskList.size() > riskConfig.getOrderNum013()){
                //针对与这个筛选过的再过滤下司机
                Map<String,List<DcdbOrderFinishRisk>> platNumberMap = riskList.stream().collect(Collectors.groupingBy(risk->risk.getPlateNumber()));
                for(Map.Entry<String,List<DcdbOrderFinishRisk>> filterEntry : platNumberMap.entrySet()){
                    if(filterEntry.getValue().size() > riskConfig.getOrderNum013()){//说明是一个司机
                        filterEntry.getValue().stream().forEach(risk->{
                            String supplierCode = StringUtils.isNotBlank(risk.getSupplierCodeFull())?risk.getSupplierCodeFull():risk.getSupplierCode();
                            if(supplierCode.startsWith("MadaSaas")){
                                supplierCode = supplierCode.split("_")[0];
                            }
                            List<String> orderIds = filterEntry.getValue().stream().map(DcdbOrderFinishRisk::getOrderId).collect(Collectors.toList());
                            DistributionRiskManage manage;
                            if(insertMap.get(risk.getOrderId()) != null){
                                manage = insertMap.get(risk.getOrderId());
                            } else {
                                manage = setManage(supplierCode,risk);
                            }
                            orderIds.remove(risk.getOrderId());//剩下的就是关联订单
                            if(StringUtils.isNotBlank(manage.getLinkOrder())){
                                manage.setLinkOrder(manage.getLinkOrder() +","+ org.apache.commons.lang3.StringUtils.join(orderIds,","));
                            } else {
                                manage.setLinkOrder(org.apache.commons.lang3.StringUtils.join(orderIds,","));
                            }
                            if(StringUtils.isNotBlank(manage.getRuleNoList())){
                                if(!manage.getRuleNoList().contains("013")) {
                                    manage.setRuleNoList(manage.getRuleNoList() +","+"013");
                                }
                            } else {
                                manage.setRuleNoList("013");
                            }
                            insertMap.put(risk.getOrderId(),manage);
                        });
                    }
                }
            }
        }

        for(Map.Entry<String,List<DcdbOrderFinishRisk>> entry : byUserMapHour.entrySet()){
            List<DcdbOrderFinishRisk> entryList = entry.getValue();
            if(entryList.size() > riskConfig.getOrderNum013()){
                //针对与这个筛选过的再过滤下司机
                Map<String,List<DcdbOrderFinishRisk>> platNumberMap = entryList.stream().collect(Collectors.groupingBy(risk->risk.getPlateNumber()));
                for(Map.Entry<String,List<DcdbOrderFinishRisk>> filterEntry : platNumberMap.entrySet()){
                    if(filterEntry.getValue().size() > riskConfig.getOrderNum013()){
                        filterEntry.getValue().stream().forEach(risk->{
                            String supplierCode = StringUtils.isNotBlank(risk.getSupplierCodeFull())?risk.getSupplierCodeFull():risk.getSupplierCode();
                            List<String> orderIds = filterEntry.getValue().stream().map(DcdbOrderFinishRisk::getOrderId).collect(Collectors.toList());
                            DistributionRiskManage manage;
                            if(insertMap.get(risk.getOrderId()) != null){
                                manage = insertMap.get(risk.getOrderId());
                            } else {
                                manage = setManage(supplierCode,risk);
                            }
                            if(StringUtils.isNotBlank(manage.getRuleNoList())){
                                if(manage.getRuleNoList().contains("013")){
                                    //当前订单已经命中了规则说明再上面的已经命中了，就不再对当前订单操作
                                    return;
                                }
                                manage.setRuleNoList(manage.getRuleNoList() +","+"013");
                            } else {
                                manage.setRuleNoList("013");
                            }
                            orderIds.remove(risk.getOrderId());//剩下的就是关联订单
                            if(StringUtils.isNotBlank(manage.getLinkOrder())){
                                manage.setLinkOrder(manage.getLinkOrder() +","+ org.apache.commons.lang3.StringUtils.join(orderIds,","));
                            } else {
                                manage.setLinkOrder(org.apache.commons.lang3.StringUtils.join(orderIds,","));
                            }
                            insertMap.put(risk.getOrderId(),manage);
                        });
                    }
                }
            }
        }
    }


    /**
     * 当前完单用户支付账号近24小时内关联完单用户数大于等于5个
     * */
    public void setManage012(Map<String,List<DcdbOrderFinishRisk>> payAccountMap,Map<String,DistributionRiskManage> insertMap,SpecialCarRiskConfig riskConfig){
        for(Map.Entry<String, List<DcdbOrderFinishRisk>> entry:payAccountMap.entrySet()){
            //该账户对用户进行去重，去重后还大于5个的
            List<DcdbOrderFinishRisk> distinctList = entry.getValue().stream().filter(e-> org.apache.commons.lang3.StringUtils.isNotBlank(e.getMemberId()))
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(DcdbOrderFinishRisk::getMemberId))),ArrayList::new));
            if(distinctList.size() > riskConfig.getUserNum012()){
                entry.getValue().stream().forEach(risk->{
                    String supplierCode = StringUtils.isNotBlank(risk.getSupplierCodeFull())?risk.getSupplierCodeFull():risk.getSupplierCode();
                    if(supplierCode.startsWith("MadaSaas")){
                        supplierCode = supplierCode.split("_")[0];
                    }
                    List<String> orderIds = distinctList.stream().map(DcdbOrderFinishRisk::getOrderId).collect(Collectors.toList());
                    DistributionRiskManage manage;
                    if(insertMap.get(risk.getOrderId()) != null){
                        manage = insertMap.get(risk.getOrderId());
                    } else {
                        manage = setManage(supplierCode,risk);
                    }
                    orderIds.remove(risk.getOrderId());//剩下的就是关联订单
                    if(StringUtils.isNotBlank(manage.getLinkOrder())){
                        manage.setLinkOrder(manage.getLinkOrder() +","+ org.apache.commons.lang3.StringUtils.join(orderIds,","));
                    } else {
                        manage.setLinkOrder(org.apache.commons.lang3.StringUtils.join(orderIds,","));
                    }
                    if(StringUtils.isNotBlank(manage.getRuleNoList())){
                        if(!manage.getRuleNoList().contains("012")) {
                            manage.setRuleNoList(manage.getRuleNoList() + "," + "012");
                        }
                    } else {
                        manage.setRuleNoList("012");
                    }
                    insertMap.put(risk.getOrderId(),manage);
                });
            }
        }
    }

    /**
     * 支付账号维度
     * */
    public Map<String,List<DcdbOrderFinishRisk>> convertPayAccountMap(List<DcdbOrderFinishRisk> dcdbOrderFinishRisks){
        return dcdbOrderFinishRisks.stream()
                .filter(e-> StringUtils.isNotBlank(e.getPayAccount()))
                .collect(Collectors.groupingBy(risk-> risk.getPayAccount()));
    }

    /**
     * 用户维度
     * */
    public Map<String,List<DcdbOrderFinishRisk>> convertUserMap(List<DcdbOrderFinishRisk> dcdbOrderFinishRisks){
        return dcdbOrderFinishRisks.stream()
                .filter(e-> StringUtils.isNotBlank(e.getMemberId()))
                .collect(Collectors.groupingBy(risk-> risk.getMemberId()));
    }

    /**
     * 用户维度-顺风车
     * */
    public Map<String,List<DcdbOrderFinishRisk>> convertUserSfcMap(List<DcdbOrderFinishRisk> sfcOrderList){
        return sfcOrderList.stream()
                .filter(e-> StringUtils.isNotBlank(e.getMemberId()))
                .collect(Collectors.groupingBy(risk-> risk.getMemberId()));
    }

    /**
     * 设备维度
     * */
    public Map<String,List<DcdbOrderFinishRisk>> convertDeviceMap(List<DcdbOrderFinishRisk> dcdbOrderFinishRisks){
        return dcdbOrderFinishRisks.stream()
                .filter(e-> StringUtils.isNotBlank(e.getDeviceId()))
                .collect(Collectors.groupingBy(risk-> risk.getDeviceId()));
    }
    
    public static void main(String[] args) {
        BigDecimal rate2 = new BigDecimal(3).divide(new BigDecimal(97),4,BigDecimal.ROUND_CEILING);
        System.out.println(rate2);
    }

    /**
     * 获取统一配置
     * */
    public SpecialCarRiskConfig getRiskConfig(){
        try {
            String channelRiskConfig = ConfigCenterClient.get("special_car_finish_risk_config");
            log.info("获取专车场景风控:"+channelRiskConfig);
            SpecialCarRiskConfig config = JSONObject.parseObject(channelRiskConfig,SpecialCarRiskConfig.class);
            return config;
        } catch (Exception e) {
            log.error("获取专车场景风控错误:",e);
        }
        return null;
    }

    /**
     * 生成初始插入数据
     * */
    public DistributionRiskManage setManage(String supplierCode,DcdbOrderFinishRisk risk){
        String supplierName = supplierApiClient.getSupplierCodeMap().get(supplierCode);
        if(StringUtils.isBlank(supplierName)){
            log.info("[][][][]完单返券错误{}", JsonUtils.json(risk));
        }
        DistributionRiskManage manage = new DistributionRiskManage();
        manage.setOrderId(risk.getOrderId());
        manage.setCityName(risk.getStartCityName());
        manage.setCreateTime(new Date());
        manage.setUpdateTime(new Date());
        manage.setMainScene(3);
        manage.setChildScene(1);
        manage.setRiskLevel(5);
        manage.setHitTime(new Date());
        manage.setCityId(risk.getStartCityId());
        manage.setDriverCardNo(risk.getPlateNumber());
        manage.setSupplierCode(supplierCode);
        manage.setSupplierName(supplierName);
        manage.setPayAccount(risk.getPayAccount());
        manage.setMemberId(risk.getMemberId());
        manage.setMainScene(3);
        manage.setChildScene(4);
        manage.setRiskMainScenario("离线场景");
        manage.setRiskChildScenario("及时专车");
        manage.setPhone(risk.getPassengerCellphone());
        manage.setUnionId(risk.getUniId());
        return manage;
    }
}
