package com.ly.car.risk.process.strategy.blacklist;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerRiskTipTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.StrategySceneEnum;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyReqExtConst;
import com.ly.car.risk.process.repo.risk.mapper.HcCustomerMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcCustomer;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.dto.CommonCustomerParam;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.utils.StrategyUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description of MTBlackListHander
 *
 * <AUTHOR>
 * @date 2025/3/25
 * @desc
 */
@Service
public class MTBlackListHandler implements BlackListCheck {

    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private HcCustomerMapper hcCustomerMapper;


    @Override
    public RiskSceneResult blackListCheck(UnifyCheckRequest request) {
        HcCustomerRiskTypeEnum mtBlackWhiteCheckResult = mtBlackWhiteCheck(request);
        if (Objects.equals(mtBlackWhiteCheckResult, HcCustomerRiskTypeEnum.WHITE_LIST)) {
            return RiskSceneResult.pass(RiskCustomerRiskTipTypeEnum.white.getMsg());
        } else if (null != mtBlackWhiteCheckResult) {
            RiskSceneResult fail = RiskSceneResult.fail(mtBlackWhiteCheckResult.getTip().getMsg(), mtBlackWhiteCheckResult.getCode());
            return fail;
        }
        return null;
    }

    private HcCustomerRiskTypeEnum mtBlackWhiteCheck(UnifyCheckRequest request) {
        
        // 先判断是不是接单类型
        StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
        boolean isDriverAccept = sceneEnum == StrategySceneEnum.DRIVER_ACCEPT_ORDER || sceneEnum == StrategySceneEnum.DISPATCHER_ACCEPT_ORDER;

        // MT的风控校验
        boolean hasMtWhite = false;
        boolean hasMtBlack = false;
        List<HcCustomer> hcCustomers = queryMtBlackWhiteList(request);
        List<Integer> customerRiskList = hcCustomers.stream().map(HcCustomer::getCustomerType).collect(Collectors.toList());
        List<Integer> whiteTypes = StrategySceneEnum.hcSceneWhiteType(request.getScene(), request.getProductLine());
        hasMtWhite = CollUtil.containsAny(customerRiskList, whiteTypes);
        List<Integer> blackTypes = StrategySceneEnum.hcSceneBlackType(request.getScene(), request.getProductLine());
        List<HcCustomer> matchMtBlackList = hcCustomers.stream().filter(p -> blackTypes.contains(p.getCustomerType())).sorted(Comparator.comparing(p -> p.getCustomerType())).collect(Collectors.toList());
        hasMtBlack = CollUtil.isNotEmpty(matchMtBlackList);
        // 如果有MT白名单，非司机接单则直接结束了
        if (!isDriverAccept) {
            if (hasMtWhite) {
                return HcCustomerRiskTypeEnum.WHITE_LIST;
            }
            // 如果有MT黑名单，直接卡死
            if (hasMtBlack) {
                return HcCustomerRiskTypeEnum.of(matchMtBlackList.get(0).getCustomerType());
            }
            return null;
        } else {
            // 如果有MT黑名单，直接卡死
            if (!hasMtWhite && hasMtBlack) {
                return HcCustomerRiskTypeEnum.of(matchMtBlackList.get(0).getCustomerType());
            }
            // 如果是司机接单，看下聚合名单中是否有黑名单
            boolean hasWhite = false;
            boolean hasBlack = false;
            // 黑白名单校验
            CommonCustomerParam customerParam = new CommonCustomerParam();
            customerParam.setUserPhone(request.getUserPhone());
            customerParam.setPassengerCellphone(request.getPassengerPhone());
            customerParam.setDriverCardNo(request.getCarNum());
            List<RiskCustomerManage> listByValueByGroup = this.riskCustomerService.getListByValueByGroup(customerParam, new Date());
            // 先看看是不是有黑名单
            if (StringUtils.isNotBlank(request.getPassengerPhone()) && StringUtils.isNotBlank(request.getCarNum())) {
                boolean oneToOneMatch = listByValueByGroup.stream().anyMatch(p -> Objects.equals(p.getRiskType(), RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode())
                        && Objects.equals(p.getCustomerValue(), request.getCarNum()) && Objects.equals(p.getBindUser(), request.getPassengerPhone()));
                if (oneToOneMatch) {
                    return HcCustomerRiskTypeEnum.DISABLE_RECEIVE_ORDER_BLACK_LIST;
                }
            }
            // 先判断是不是有白名单
            hasWhite = listByValueByGroup.stream().anyMatch(p -> Objects.equals(RiskCustomerRiskTypeEnum.white_list.getCode(), p.getRiskType()));
            if (hasWhite) {
                return HcCustomerRiskTypeEnum.WHITE_LIST;
            }
            List<Integer> riskTypes = StrategySceneEnum.sceneRiskType(StrategySceneEnum.DRIVER_ACCEPT_ORDER.getScene(), request.getProductLine());
            List<RiskCustomerManage> matchBlackList = listByValueByGroup.stream().filter(p -> riskTypes.contains(p.getRiskType())).sorted(Comparator.comparing(p -> p.getRiskType())).collect(Collectors.toList());
            hasBlack = CollUtil.isNotEmpty(matchBlackList);
            if (hasBlack) {
                return matchBlackList.stream().anyMatch(p -> Objects.equals(p.getRiskType(), RiskCustomerRiskTypeEnum.black_list.getCode())) ?
                        HcCustomerRiskTypeEnum.BLACK_LIST : HcCustomerRiskTypeEnum.DISABLE_RECEIVE_ORDER_WHITE_LIST;
            }
            return hasMtWhite ? HcCustomerRiskTypeEnum.WHITE_LIST : null;
        }
    }

    private List<HcCustomer> queryMtBlackWhiteList(UnifyCheckRequest request) {
        String carNum = request.getCarNum();
        String driverId = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID);
        String toDriverId = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.TO_DRIVER_ID);
        String driverPhone = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_PHONE);
        String certNo = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO);
        if (StringUtils.isAllBlank(carNum, driverId, driverPhone, toDriverId, certNo)) {
            return new ArrayList<>();
        }

        List<HcCustomer> hcCustomers = hcCustomerMapper.selectList(new QueryWrapper<HcCustomer>()
                .eq("line_type", 0).gt("invalid_time", new Date()).and(data -> data
                        .or().eq(StringUtils.isNotBlank(carNum), "driver_card_no", carNum)
                        .or().eq(StringUtils.isNotBlank(driverId), "driver_id", driverId)
                        .or().eq(StringUtils.isNotBlank(driverPhone), "driver_phone", driverPhone)
                        .or().eq(StringUtils.isNotBlank(toDriverId), "driver_id", toDriverId)
                        .or().eq(StringUtils.isNotBlank(certNo), "id_card_no", certNo)));
        return hcCustomers;
    }

}