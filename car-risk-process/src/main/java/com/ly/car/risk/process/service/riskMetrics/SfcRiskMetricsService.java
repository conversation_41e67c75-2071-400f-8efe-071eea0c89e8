package com.ly.car.risk.process.service.riskMetrics;

/**
 * @author: fulin.li
 * @create-date: 2025/9/10 10:58
 */
public interface SfcRiskMetricsService {

    /**
     * 24小时内同一司机多笔（>1）高单价（>100）
     * @return
     */
    boolean hour24DriverOrderAmountGraterThan100CountGraterThan1(String orderId);

    /**
     * 订单司乘无通话记录
     * @param orderId
     * @return
     */
    boolean havePhoneRecording(String orderId);
}
