package com.ly.car.risk.process.service.rule.mtGroup;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MtHandlerMap {

    public static Map<String, List<String>> serviceNameMap;
    public static Map<String,List<Integer>> customerWhiteMap;
    public static Map<String,List<Integer>> customerBlackMap;

    static {
        Map<String,List<String>> map = new HashMap<>();
        Map<String,List<Integer>> whiteMap = new HashMap<>();
        Map<String,List<Integer>> blackMap = new HashMap<>();

        List<String> MT_REGISTER = new ArrayList<>();
//        MT_REGISTER.add("mtCommonCustomerService");
        MT_REGISTER.add("mtDriverRegisterService");
        map.put("11-1",MT_REGISTER);

        List<Integer> MT_REGISTER_WHITE = new ArrayList<>();
        List<Integer> MT_REGISTER_BLACK = new ArrayList<>();
        MT_REGISTER_WHITE.add(10);
        MT_REGISTER_BLACK.add(11);
        whiteMap.put("11-1",MT_REGISTER_WHITE);
        blackMap.put("11-1",MT_REGISTER_BLACK);

        List<String> MT_CERTIFICATION = new ArrayList<>();
        MT_CERTIFICATION.add("mtCommonCustomerService");
        MT_CERTIFICATION.add("mtDriverIdCardService");
        MT_CERTIFICATION.add("mtDriverScoreService");
        MT_CERTIFICATION.add("mtDriverCommonService");
//        MT_CERTIFICATION.add("mtDriverLicenseInfoService");
        MT_CERTIFICATION.add("mtDriverCarOwnerService");
        map.put("11-2",MT_CERTIFICATION);


        List<String> MT_DRIVER_LOGIN = new ArrayList<>();
        MT_DRIVER_LOGIN.add("mtCommonCustomerService");
        MT_DRIVER_LOGIN.add("mtDriverLoginService");
        map.put("11-3",MT_DRIVER_LOGIN);

        List<Integer> MT_LOGIN_WHITE = new ArrayList<>();
        List<Integer> MT_LOGIN_BLACK = new ArrayList<>();
        MT_LOGIN_WHITE.add(8);
        MT_LOGIN_BLACK.add(9);
        whiteMap.put("11-3",MT_LOGIN_WHITE);
        blackMap.put("11-3",MT_LOGIN_BLACK);

        List<String> MT_DRIVER_RECEIVE = new ArrayList<>();
        MT_DRIVER_RECEIVE.add("mtCommonCustomerService");
        MT_DRIVER_RECEIVE.add("mtDriverReceiveService");
        MT_DRIVER_RECEIVE.add("driverAcceptNotifyService");
        map.put("11-4",MT_DRIVER_RECEIVE);

        List<Integer> MT_RECEIVE_WHITE = new ArrayList<>();
        List<Integer> MT_RECEIVE_BLACK = new ArrayList<>();
        MT_RECEIVE_WHITE.add(6);
        MT_RECEIVE_BLACK.add(7);
        whiteMap.put("11-4",MT_RECEIVE_WHITE);
        blackMap.put("11-4",MT_RECEIVE_BLACK);

        List<String> MT_SAFE = new ArrayList<>();
        MT_SAFE.add("mtDriverSafeService");
        map.put("11-5",MT_SAFE);

        List<String> MT_DRIVER_CASH = new ArrayList<>();
        MT_DRIVER_CASH.add("mtCommonCustomerService");
        MT_DRIVER_CASH.add("mtDriverCashService");
        map.put("11-6",MT_DRIVER_CASH);
        List<Integer> MT_CASH_WHITE = new ArrayList<>();
        List<Integer> MT_CASH_BLACK = new ArrayList<>();
        MT_CASH_WHITE.add(2);
        MT_CASH_BLACK.add(3);
        whiteMap.put("11-6",MT_CASH_WHITE);
        blackMap.put("11-6",MT_CASH_BLACK);

        List<String> MT_BANK_CARD = new ArrayList<>();
        MT_BANK_CARD.add("mtCommonCustomerService");
        MT_BANK_CARD.add("mtDriverBankCardService");
        map.put("11-7",MT_BANK_CARD);

        List<String> COMMON_DRIVER_CLIENT = new ArrayList<>();
        COMMON_DRIVER_CLIENT.add("driverCommonWarningService");
        map.put("11-8",COMMON_DRIVER_CLIENT);

        List<String> DRIVER_TO_DRIVER = new ArrayList<>();
        DRIVER_TO_DRIVER.add("mtCommonCustomerService");
        map.put("11-9",DRIVER_TO_DRIVER);

        List<Integer> DRIVER_TO_DRIVER_WHITE = new ArrayList<>();
        List<Integer> DRIVER_TO_DRIVER_BLACK = new ArrayList<>();
        DRIVER_TO_DRIVER_WHITE.add(12);
        DRIVER_TO_DRIVER_BLACK.add(13);
        whiteMap.put("11-9",DRIVER_TO_DRIVER_WHITE);
        blackMap.put("11-9",DRIVER_TO_DRIVER_BLACK);

        List<String> DRIVER_CANCEL = new ArrayList<>();
        DRIVER_CANCEL.add("driverCancelService");
        map.put("11-10",DRIVER_CANCEL);

        List<String> DRIVER_RELEASE = new ArrayList<>();
        DRIVER_RELEASE.add("mtCommonCustomerService");
        DRIVER_RELEASE.add("driverReleaseService");
        map.put("11-11",DRIVER_RELEASE);
        List<Integer> DRIVER_RELEASE_WHITE = new ArrayList<>();
        List<Integer> DRIVER_RELEASE_BLACK = new ArrayList<>();
        DRIVER_RELEASE_WHITE.add(4);
        DRIVER_RELEASE_BLACK.add(5);
        whiteMap.put("11-11",DRIVER_RELEASE_WHITE);
        blackMap.put("11-11",DRIVER_RELEASE_BLACK);

        List<String> DRIVER_INVITE = new ArrayList<>();
        DRIVER_INVITE.add("mtCommonCustomerService");
        DRIVER_INVITE.add("driverInviteService");
        map.put("11-12",DRIVER_INVITE);
        List<Integer> DRIVER_INVITE_WHITE = new ArrayList<>();
        List<Integer> DRIVER_INVITE_BLACK = new ArrayList<>();
        DRIVER_INVITE_WHITE.add(4);
        DRIVER_INVITE_BLACK.add(5);
        whiteMap.put("11-12",DRIVER_INVITE_WHITE);
        blackMap.put("11-12",DRIVER_INVITE_BLACK);

        List<String> DRIVER_RETURN_CASH = new ArrayList<>();
        DRIVER_RETURN_CASH.add("DriverReturnCashService");
        map.put("11-13",DRIVER_RETURN_CASH);

        List<String> DRIVER_B_LOGIN = new ArrayList<>();
        DRIVER_B_LOGIN.add("mtCommonCustomerService");
        map.put("12-1",DRIVER_B_LOGIN);
        List<Integer> DRIVER_B_LOGIN_WHITE = new ArrayList<>();
        List<Integer> DRIVER_B_LOGIN_BLACK = new ArrayList<>();
        DRIVER_INVITE_WHITE.add(8);
        DRIVER_INVITE_BLACK.add(9);
        whiteMap.put("12-1",DRIVER_B_LOGIN_WHITE);
        blackMap.put("12-1",DRIVER_B_LOGIN_BLACK);

        serviceNameMap = map;
        customerWhiteMap = whiteMap;
        customerBlackMap = blackMap;
    }
}
