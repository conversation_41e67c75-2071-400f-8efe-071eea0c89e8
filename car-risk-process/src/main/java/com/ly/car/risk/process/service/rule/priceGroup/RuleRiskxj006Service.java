package com.ly.car.risk.process.service.rule.priceGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.context.FilterSceneContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Component
@Slf4j
@Scope("prototype")
public class RuleRiskxj006Service extends FilterCheckPriceHandler{

    private static final String ruleNo = "xj006";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Override
    public UiResult doHandler(FilterSceneContext context) {
        if(!context.getSpecialCarRuleConfig().getXj006onOff()){
            if(this.nextHandler != null){
                return this.nextHandler.doHandler(context);
            } else {
                return context.getUiResult();
            }
        }
        UiResult uiResult = context.getUiResult();
        //调用大研发标签
        if(context.getEsAmount().compareTo(new BigDecimal("300")) <= 0){

        }
        return context.getUiResult();
    }
}
