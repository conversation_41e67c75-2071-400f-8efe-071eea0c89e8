package com.ly.car.risk.process.strategy.model;

import com.ly.car.risk.process.strategy.model.detail.OfflineRiskFieldDetail;
import com.ly.car.risk.process.strategy.model.detail.RiskRuleDetail;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * Description of RiskStrategy
 *
 * <AUTHOR>
 * @date 2024/5/31
 * @desc
 */
@Data
public class OfflineRiskStrategyDetail {
    
    // 策略id
    private Long strategyId;
    
    //策略编号
    private String strategyNo;
    
    // 业务线
    private List<String> productLine;
    
    //风险类型 0-安全 1-风控 2-全部
    private Integer riskType;
    
    //表达式
    private String expression;
    
    //策略返回文案
    private String strategyWord;
    
    //管控时间 单位天
    private Integer controlTime;
    
    //管控对象 0-司机 1-用户
    private Integer controlType;
    
    //命中字段
    private String hitField;
    
    //命中动作0:加全局黑 1-加1v1
    private Integer hitAction;
    
    //处置动作 0-禁止 1-增强校验 2-通过
    private Integer disposeAction;
    
    // 0-测试 1-上线运行 2-下线
    private int status;
    
    // 运行周期
    private String during;
    
    // 脚本
    private String script;
    
    // 渠道
    private List<String> channels;
    
    // 供应商
    private List<String> supplierCodes;
    
    // 城市
    private Integer cityId;
    
    // 指标
    private List<OfflineRiskFieldDetail> fields = new ArrayList<>();

}