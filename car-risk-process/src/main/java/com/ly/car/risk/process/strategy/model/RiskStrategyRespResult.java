package com.ly.car.risk.process.strategy.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskStrategyRespResult {

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略编号
     */
    private String strategyNo;


    /**
     * 策略名称
     */
    private String strategyName;
}