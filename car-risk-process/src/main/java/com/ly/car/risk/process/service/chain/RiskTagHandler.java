package com.ly.car.risk.process.service.chain;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.kafka.param.MarketingRiskReq;
import com.ly.car.risk.process.kafka.rsp.MarketingRiskRsp;
import com.ly.car.risk.process.service.CommonRiskService;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class RiskTagHandler extends FilterChainHandler{

    @Resource
    private CommonRiskService commonRiskService;

    @Override
    public UiResult doHandler(FilterParams params) {
        log.info("[CustomerHandler][doHandler][{}][{}]进入大研发风控判断{}",params.getMemberId(),params.getUnionId(), JsonUtils.json(params));
        UiResult result;
        MarketingRiskReq req = new MarketingRiskReq();
        req.setDeviceId(params.getDeviceId());
        req.setPhone(params.getUserPhone());
        req.setUnionId(params.getUnionId());
        req.setMemberId(params.getMemberId());
        MarketingRiskRsp marketingRiskRsp = commonRiskService.queryRiskTag(req, params.getChannelType());
        if(marketingRiskRsp.getCode().equals(200) && marketingRiskRsp.getData().get("finalLevel").equals("1")){
            result = UiResult.ok();
            result.setData("0");
        } else {
            result = UiResult.fail();
            result.setData("1");
        }
        return result;
    }
}
