package com.ly.car.risk.process.controller.task;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.repo.risk.mapper.entity.SupplierAppealRecord;
import com.ly.car.risk.process.repo.risk.mapper.entity.TcSupplierWorkOrder;
import com.ly.car.risk.process.repo.risk.mapper.entity.YueJudgeWorkOrder;
import com.ly.car.risk.process.service.base.SupplierAppealRecordService;
import com.ly.car.risk.process.service.base.TcSupplierWorkOrderService;
import com.ly.car.risk.process.service.base.YueJudgeWorkOrderService;
import com.ly.car.risk.process.supplier.SupplierYueYueClientAbstract;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("workOrderTask")
@Slf4j
public class WorkOrderTask {

    @Resource
    private TcSupplierWorkOrderService tcSupplierWorkOrderService;
    @Resource
    private YueJudgeWorkOrderService yueJudgeWorkOrderService;
    @Resource
    private SupplierAppealRecordService supplierAppealRecordService;
    @Resource
    private SupplierYueYueClientAbstract supplierYueYueClientAbstract;

    @RequestMapping("yueYueFinish")
    public void finishYueYueWorkOrder(){
        List<TcSupplierWorkOrder> tcSupplierWorkOrderList = tcSupplierWorkOrderService.queryByCreateTime(DateUtil.addDay(new Date(), -12), DateUtil.addDay(new Date(), -7));
        tcSupplierWorkOrderList = tcSupplierWorkOrderList.stream()
                .filter(data->data.getSupplierCode().equals("YueYue")&&data.getJudgeResult()==0)
                .filter(data->data.getWorkStatus() == 1)
                .collect(Collectors.toList());
        for(TcSupplierWorkOrder supplierWorkOrder : tcSupplierWorkOrderList){
            //查一下最晚申诉时间
            YueJudgeWorkOrder yueJudgeWorkOrder = yueJudgeWorkOrderService.queryByOrderId(supplierWorkOrder.getOrderId());
            if(yueJudgeWorkOrder == null){
                continue;
            }
            Date date = new Date(Long.valueOf(yueJudgeWorkOrder.getAppealEndTime()));
            if(date.after(new Date())){
                continue;
            }
            //查询是否已经过了最晚申诉时间，且没有待审核的申诉单
            supplierWorkOrder.setWorkStatus(4);
            supplierWorkOrder.setJudgeResult(1);
            supplierWorkOrder.setUpdateTime(new Date());
            this.tcSupplierWorkOrderService.updateByEntity(supplierWorkOrder);
        }
    }

    @RequestMapping("autoPassCheck")
    public void autoPassCheck(){
        //查询两天前的待审核的申诉单
        Date startDate = DateUtil.addHour(DateUtil.addDay(new Date(),-2),-1);
        Date endDate =DateUtil.addDay(new Date(),-2);
        log.info("[][][][]约约工单自动通过时间区间{},{}",DateUtil.date2String(startDate),DateUtil.date2String(endDate));
        List<SupplierAppealRecord> supplierAppealRecords = this.supplierAppealRecordService.queryByCreateTime(startDate,endDate);
        for(SupplierAppealRecord record : supplierAppealRecords){
            TcSupplierWorkOrder tcSupplierWorkOrder = this.tcSupplierWorkOrderService.queryByOrderId(record.getOrderId(), null);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderId",record.getOrderId());
            jsonObject.put("appealId",record.getTcAppealId());
            jsonObject.put("auditStatus",1);
            jsonObject.put("auditRemark","自动通过");
            jsonObject.put("eventId",tcSupplierWorkOrder.getYueEventId());
            jsonObject.put("supplierOrderId",tcSupplierWorkOrder.getSupplierOrderId());
            supplierYueYueClientAbstract.syncAppealResult(jsonObject);
        }

    }

    public static void main(String[] args) {
        System.out.println(DateUtil.date2String(new Date(Long.valueOf("1703509320576"))));
    }
}
