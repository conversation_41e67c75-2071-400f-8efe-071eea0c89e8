package com.ly.car.risk.process.service.ability;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.common.enums.ConfigCenterKeyEnum;
import com.ly.dsf.utils.StringUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MetricsConfigCenterService {


    public String getMetricsCarOwnerConfigValue(String name,String  variableName) {
        String variableValue = getMetricsCarOwnerConfig(name,false).getString(variableName);
        if (StringUtils.isBlank(variableValue)) {
            variableValue = getMetricsCarOwnerConfig(name,true).getString(variableName);;
        }
        return variableValue;
    }
    public JSONObject getMetricsCarOwnerConfig(String name,boolean isDefault) {
        if (name == null) {
            return new JSONObject();
        }
        String configValue = getConfigValue(ConfigCenterKeyEnum.Metric_UserDefined_Variable, isDefault);
        if (StringUtils.isBlank(configValue)) {
            return new JSONObject();
        }
        try {
            return JSONArray.parseArray(configValue).stream()
                    .filter(JSONObject.class::isInstance)
                    .map(JSONObject.class::cast)
                    .filter(obj -> name.equals(obj.getString("name")))
                    .findFirst()
                    .orElse(new JSONObject());
        } catch (Exception e) {
            return new JSONObject();
        }
    }
    public String getConfigValue(ConfigCenterKeyEnum keyEnum,boolean isDefault) {
        // 从配置中心获取风险配置
        if (isDefault) {
            return keyEnum.getDefaultValue();
        }
        String value = getConfigValue(keyEnum.getKey());
        if (StringUtils.isBlank(value)) {
            // 配置中心未获取到，使用默认值
            value = keyEnum.getDefaultValue();
        }
        return value;
    }

    public static String getConfigValue(String key) {
        try {
            return ConfigCenterClient.get(key);
        } catch (Exception e) {
            log.error("获取配置中心配置失败，key:{}", key, e);
            return null;
        }
    }
}
