package com.ly.car.risk.process.turboMQ.dto;

import com.ly.car.order.entity.SfcOrder;
import lombok.Data;

@Data
public class SfcOrderStatusChangeEventOrderData {

    /**
     * 订单全量信息
     */
    private SfcOrder orderInfo;
    /**
     * 变更前的支付状态
     */
    private Integer oldPayStatus;
    /**
     * 变更前的拼车状态
     */
    private Integer oldCarPoolStatus;
    /**
     * 变更前的订单状态
     */
    private Integer oldOrderStatus;

    /**
     * 是否是创建订单事件
     */
    private Boolean isCreate = false;
    /**
     * 调用失败重试次数
     */
    private int retryCount = 0;
}
