package com.ly.car.risk.process.service.rule.mtGroup.safe;

import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.api.LabelClient;
import com.ly.car.risk.process.api.rsp.LabelRsp;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.rule.mtGroup.MtFilterContext;
import com.ly.car.risk.process.service.rule.mtGroup.MtFilterHandler;
import com.ly.dal.util.DateUtil;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DriverAcceptNotifyService extends MtFilterHandler {

    @Resource
    private LabelClient labelClient;
    @Resource
    private SfcOrderMapper  sfcOrderMapper;
    @Resource
    private CarOrderService carOrderService;

    @Override
    public void doHandler(MtFilterContext context) {
        String productLine = context.getParam().getString("productLine");
        String orderIdStr = context.getParam().getString("orderId");//是供应商订单号
        if(StringUtils.isBlank(orderIdStr)){
            return;
        }
        //查询同程订单号
        String memberId = "";
        String unionId = "";
        Date useTime = null;
        Integer orderChannel = null;

        String orderId = orderIdStr.split("_")[0];
        // todo 这段代码有问题，但是线上日志，压根没有订单号传参会走到这里
        if (orderId.startsWith("YC")) {
            CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(orderId);

            if (null != carOrderDetail && null != carOrderDetail.getCarInfo()) {

                memberId = carOrderDetail.getMemberId();
                unionId = carOrderDetail.getUnionId();
                orderChannel= carOrderDetail.getOrderChannel();

                if (null != carOrderDetail.getBaseInfo()) {
                    useTime = carOrderDetail.getBaseInfo().getGmtUsage();
                }
            }
        } else {
            SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(orderId);
            if(null != sfcOrder){
                if (sfcOrder.getDistributorFlag() == 1) {
                    return;
                }
                
                memberId = String.valueOf(sfcOrder.getMemberId());
                unionId = sfcOrder.getUnionId();
                useTime = sfcOrder.getUseTime();
                orderChannel = sfcOrder.getPlatId();
            }
        }


        //是否敏感时间
        String currentDay = DateUtil.date2String(useTime,DateUtil.DATE_PATTERN_YYYY_MM_DD);
        if(null == useTime || (useTime.after(DateUtil.string2Date(currentDay+" 05:00:00"))
                && useTime.before(DateUtil.string2Date(currentDay+ " 20:00:00")))){
            log.info("[][][][]当前订单不在敏感时间");
            return;
        }

        LabelRsp.DetailRsp detailRsp = labelClient.queryLabel(orderChannel,String.valueOf(memberId), unionId);
        if(detailRsp != null && (detailRsp.getGender() == 2 || detailRsp.getStudent() == 1)){
            context.getDto().setCode(2);
            context.getDto().setMessage("当前存在敏感人及敏感时间");
        }
    }

    public static void main(String[] args) {
        Date useTime = DateUtil.string2Date("2023-09-11 22:10:00");
        String currentDay =DateUtil.date2String(useTime,DateUtil.DATE_PATTERN_YYYY_MM_DD);
        if(useTime.after(DateUtil.string2Date(currentDay+" 05:00:00"))
                && useTime.before(DateUtil.string2Date(currentDay+ " 20:00:00"))){
            System.out.println("111");
        }
    }
}
