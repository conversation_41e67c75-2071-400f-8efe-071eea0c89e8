package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.service.dto.config.WorkOrderSyncConfig;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ConfigCenterService {

    public static WorkOrderSyncConfig getSyncConfig(){
        WorkOrderSyncConfig workOrderSyncConfig = new WorkOrderSyncConfig();
        workOrderSyncConfig.setSyncChannel(0);
        workOrderSyncConfig.setSyncSupplier("T3");
        try {
            String config = ConfigCenterClient.get("work_order_sync");
            log.info("工单同步规则:"+config);
            if(StringUtils.isNotBlank(config)){
                return JSONObject.parseObject(config,WorkOrderSyncConfig.class);
            }
            return workOrderSyncConfig;
        } catch (Exception e) {
            log.error("获取工单同步规则错误:",e);
        }
        return workOrderSyncConfig;
    }

    public static String getDefault(String key,String defaultVal){
        try {
            String val = ConfigCenterClient.get(key);
            if(StringUtils.isBlank(val)){
                return defaultVal;
            }
            return val;
        } catch (Exception e) {
            return defaultVal;
        }
    }
}
