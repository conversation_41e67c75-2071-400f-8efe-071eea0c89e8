package com.ly.car.risk.process.turboMQ.dto.newCar;

import lombok.Data;

import java.util.List;

/**
 * Description of CarOrderStateChangeMsg
 *
 * <AUTHOR>
 * @date 2024/3/13
 * @desc 用车订单状态变更MQ消息体
 */
@Data
public class CarOrderStateChangeMsg {

    /** 订单流水号 */
    private String orderSerialNo;

    /** 订单标签 */
    private List<String> orderTags;

    /** 订单类型 */
    private String orderType;

    /** 原订单状态 */
    private String fromState;

    /** 当前订单状态 */
    private String toState;

    /** 当前订单用户状态 */
    private String userState;

    /** 当前订单支付状态 */
    private String payState;

    /** 订单来源(公司渠道码) */
    private Integer orderSource;

    /** 会员id */
    private String memberId;

    /** 消息发送时间戳(毫秒) */
    private Long sendTime;
}