package com.ly.car.risk.process.strategy.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description of RiskStrategyResult
 *
 * <AUTHOR>
 * @date 2024/6/4
 * @desc
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskStrategyResult {

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略编号
     */
    private String strategyNo;

    /**
     * 策略结果
     */
    private Boolean strategyMatched;

    /**
     * 命中的规则
     */
    private List<String> matchRules;

}