package com.ly.car.risk.process.strategy;

import static com.ly.car.risk.process.constants.StrategySceneEnum.DRIVER_ACCEPT_ORDER;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.RiskCustomerCustomerTypeEnum;
import com.ly.car.risk.process.constants.StrategySceneEnum;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyReqExtConst;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import com.ly.car.risk.process.repo.riskmetrics.entity.WithdrawalGroupByDriverIdResp;
import com.ly.car.risk.process.repo.riskmetrics.mapper.DriverWithdrawalMapper;
import com.ly.car.risk.process.scene.SpecialSceneHandlerFactory;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.core.DisposeCenterService;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.rule.mtGroup.MqSendConvertService;
import com.ly.car.risk.process.strategy.blacklist.BlackListHandler;
import com.ly.car.risk.process.strategy.model.*;
import com.ly.car.risk.process.strategy.model.detail.RiskFieldDetail;
import com.ly.car.risk.process.strategy.model.detail.RiskRuleDetail;
import com.ly.car.risk.process.utils.ConfigCenterUtils;
import com.ly.car.risk.process.utils.GroovyScriptUtil;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.StrategyUtil;
import com.ly.car.utils.JsonUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.ly.sof.utils.common.DateUtil;
import com.ly.tcbase.config.AppProfile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * Description of RiskStrategyHandler
 *
 * <AUTHOR>
 * @date 2024/5/31
 * @desc
 */
@Service
@Slf4j
public class RiskStrategyHandler {
    
    public static final String PRODUCT_LINE = "productLine";
    public static final String CHANNEL      = "channel";
    public static final String SCENE        = "scene";
    
    public static final String PASSENGER_PHONE = "passengerPhone";
    
    public static final String USER_PHONE = "userPhone";
    
    public static final String CAR_NUM = "carNum";
    
    public static final String MEMBER_ID = "memberId";
    
    public static final String UNION_ID = "unionId";
    
    public static final String ORDER_ID = "orderId";
    
    public static final String SUPPLIER_CODE = "supplierCode";
    
    public static final String SUPPLIER_NAME = "supplierName";
    
    public static final String DEVICE_ID = "deviceId";
    
    public static final String ORDER = "order";
    
    public static final String IP = "ip";

    // 特定场景：需要按freezeTime排序的场景
    private static final Set<String> FREEZE_TIME_PRIORITY_SCENES = Sets.newHashSet(
            StrategySceneEnum.DRIVER_AUTO_FINISH_ORDER.getScene(),
            StrategySceneEnum.CAR_OWNER_REVIEW.getScene(),
            StrategySceneEnum.CAR_OWNER_WITHDRAW.getScene(),
            StrategySceneEnum.CAR_OWNER_UNFREEZE.getScene());

    @Resource
    private RiskStrategyHelper strategyHelper;
    
    @Resource
    private DisposeCenterService disposeCenterService;
    
    @Resource
    private RiskHitService riskHitService;
    
    @Resource
    private SpecialSceneHandlerFactory specialSceneHandlerFactory;
    
    @Resource
    private MqSendConvertService mqSendConvertService;

    @Resource
    private BlackListHandler blackListHandler;
    
    @Resource
    private CarOrderService  carOrderService;

    @Resource
    private DriverWithdrawalMapper driverWithdrawalMapper;
    
    private static final ThreadLocal<HashMap<String, List<CarRiskOrderDetail>>> localCache = ThreadLocal.withInitial(() -> new HashMap<>());
    private static final ThreadLocal<HashMap<String, Object>> localObjectCache = ThreadLocal.withInitial(() -> new HashMap<>());

    public RiskSceneResult unifyCheck(UnifyCheckRequest request) throws BizException {
        
        // 参数校验
        paramCheck(request);


        RiskSceneResult blackListCheckResult = blackListHandler.blackListCheck(request);
        if(null != blackListCheckResult){
            return blackListCheckResult;
        }

        if (request.isDistributionFlag()) {
            return RiskSceneResult.pass("分销单判定风险名单通过");
        }

        // 特殊校验
        RiskSceneResult specialSceneCheck = specialHandlerCheck(request);
        if (null != specialSceneCheck && specialSceneCheck.isRiskFlag()) {
            return specialSceneCheck;
        }

        // 策略校验前的一些动作
        beforeStrategyCheck(request);

        // 策略校验
        RiskSceneResult riskSceneResult = strategyCheck(request);

        // 策略校验后的一些动作
        afterStrategyCheck(request, riskSceneResult);

        try {
            if (Objects.isNull(riskSceneResult) || StringUtils.isBlank(riskSceneResult.getFreezeTime()) && ConfigCenterUtils.getNeedFreezeTimeScene().contains(request.getScene())) {
                log.info("指定场景没有命中策略，补充兜底冻结时长");
                CarOrderDetail orderDetail = carOrderService.queryOrderDetail(request.getOrderId());
                if (Objects.nonNull(orderDetail) && Objects.nonNull(orderDetail.getBaseInfo()) && Objects.nonNull(orderDetail.getBaseInfo().getGmtTripFinished())) {
                    Date orderFinishTime = orderDetail.getBaseInfo().getGmtTripFinished();
                    Date freezeEndTime = DateUtil.addHours(orderFinishTime, 24);
                    String defaultFreezeTime = DateUtil.getNewFormatDateString(freezeEndTime);

                    // 确保兜底冻结时间不早于历史记录
                    String finalFreezeTime = riskHitService.ensureFreezeTimeNotEarlierThanHistory(request, defaultFreezeTime);

                    if(Objects.isNull(riskSceneResult)){
                        RiskSceneResult result =  new RiskSceneResult();
                        result.setFreezeTime(finalFreezeTime);
                        return result;
                    }else{
                        riskSceneResult.setFreezeTime(finalFreezeTime);
                    }
                }
            }
        }catch (Exception e){
            log.error("补充冻结时间异常",e);
        }

        try{
            DriverWithdrawalLimitConfigDTO driverWithdrawalLimitConfigDTO = ConfigCenterUtils.getDriverRiskInfoConfig();
            Long driverId = null;
            if(request.getExt().containsKey("driverId")) {
                //添加非空判断
                if (request.getExt().get("driverId")!=null && StringUtils.isNotBlank((String)request.getExt().get("driverId"))) {
                    driverId = Long.parseLong((String)request.getExt().get("driverId"));
                }
            }
            log.info("[RiskStrategyHandler][unifyCheck][][] 统一配置司机风控:{}",JsonUtils.json(driverWithdrawalLimitConfigDTO));
            //在场景内
            if(driverWithdrawalLimitConfigDTO.getSceneList().contains(request.getScene()) && Objects.nonNull(driverId)){
                List<WithdrawalGroupByDriverIdResp> driverIdRespList = driverWithdrawalMapper.selectDriverWithdrawalGroupByDriverIdAndOffsetTime(driverWithdrawalLimitConfigDTO.getOffsetHour(), AppProfile.getEnvironment());
                log.info("[RiskStrategyHandler][unifyCheck][][] 查询提现信息:{}",JsonUtils.json(driverIdRespList));
                final Long dId = driverId;
                if(CollectionUtils.isNotEmpty(driverIdRespList)){
                    WithdrawalGroupByDriverIdResp withdrawalGroupByDriverIdResp = driverIdRespList.stream().filter(s->Objects.equals(s.getDriverId(),dId)).findFirst().orElse(null);
                    if(Objects.nonNull(withdrawalGroupByDriverIdResp) && withdrawalGroupByDriverIdResp.getCount() >= driverWithdrawalLimitConfigDTO.getWithdrawalNum()){
                        //命中司机风控
                        Date todayMorning = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
                        Date freezeTime = DateUtil.addMinutes(todayMorning, driverWithdrawalLimitConfigDTO.getFreezeTime());
                        String driverFreezeTime = DateUtil.getNewFormatDateString(freezeTime);

//                        // 确保司机风控冻结时间不早于历史记录
//                        String finalDriverFreezeTime = riskHitService.ensureFreezeTimeNotEarlierThanHistory(request, driverFreezeTime);

                        DriverRiskInfo driverRiskInfo = new DriverRiskInfo();
                        driverRiskInfo.setDriverId(dId);
//                        driverRiskInfo.setFreezeTime(finalDriverFreezeTime);
                        driverRiskInfo.setReason(driverWithdrawalLimitConfigDTO.getReason());
                        if(Objects.isNull(riskSceneResult)) {
                            RiskSceneResult result = new RiskSceneResult();
                            result.setDriverRiskInfo(driverRiskInfo);
                            return result;
                        } else {
                            riskSceneResult.setDriverRiskInfo(driverRiskInfo);
                        }
                    }
                }
           }
        }catch (Exception e){
            log.error("司机风控信息异常",e);
        }

//        // 最终保障：确保所有返回的freezeTime都不早于历史记录
//        if (Objects.nonNull(riskSceneResult)) {
//            // 处理主要的freezeTime
//            if (StringUtils.isNotBlank(riskSceneResult.getFreezeTime())) {
//                String finalFreezeTime = riskHitService.ensureFreezeTimeNotEarlierThanHistory(request, riskSceneResult.getFreezeTime());
//                riskSceneResult.setFreezeTime(finalFreezeTime);
//            }
//
//            // 处理司机风控的freezeTime
//            if (Objects.nonNull(riskSceneResult.getDriverRiskInfo()) &&
//                StringUtils.isNotBlank(riskSceneResult.getDriverRiskInfo().getFreezeTime())) {
//                String finalDriverFreezeTime = riskHitService.ensureFreezeTimeNotEarlierThanHistory(
//                        request, riskSceneResult.getDriverRiskInfo().getFreezeTime());
//                riskSceneResult.getDriverRiskInfo().setFreezeTime(finalDriverFreezeTime);
//            }
//        }

        return riskSceneResult;
    }

    public static void main(String[] args) {
        Date todayMorning = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
        Date freezeTime = DateUtil.addMinutes(todayMorning, 1440);
    }
    
    private void afterStrategyCheck(UnifyCheckRequest request, RiskSceneResult riskSceneResult) {
        // MT 且 司机认证
        if (Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())
                && Objects.equals(request.getScene(), StrategySceneEnum.DRIVER_AUTHENTICATION.getScene())) {
            // 非风险则通知MT 司机认证成功
            if (null != riskSceneResult && !riskSceneResult.isRiskFlag()) {
                RiskResultNewDTO dto = new RiskResultNewDTO();
                Map<String, Object> data = new HashMap<>();
                data.put("driverId", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID));
                data.put("idCard", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO));
                dto.setObj(data);
                mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
            }
        }
    }
    
    private RiskSceneResult specialHandlerCheck(UnifyCheckRequest request) {
        RiskSceneResult riskSceneResult = specialSceneHandlerFactory.handlerCheck(request);
        return riskSceneResult;
    }
    
    private void beforeStrategyCheck(UnifyCheckRequest request) {
        
        // 如果是司机接单场景，并且是权益单，则将场景变更为 权益活动
        if (Objects.equals(request.getScene(), DRIVER_ACCEPT_ORDER.getScene()) && request.isRightsOrderFlag()) {
            request.setScene(StrategySceneEnum.RIGHTS_ORDER.getScene());
        }
    }
    
    /**
     * MetricStrategyProductLineEnum
     */
    private Map<String, Object> fillParam(UnifyCheckRequest request) {
        Map<String, Object> params = null == request.getExt() ? new HashMap<>() : request.getExt();
        params.put(SCENE, request.getScene());
        params.put(MEMBER_ID, request.getMemberId());
        params.put(UNION_ID, request.getUnionId());
        params.put(CHANNEL, request.getChannel());
        params.put(PRODUCT_LINE, request.getProductLine());
        params.put(ORDER_ID, request.getOrderId());
        params.put(USER_PHONE, request.getUserPhone());
        params.put(PASSENGER_PHONE, request.getPassengerPhone());
        params.put(CAR_NUM, request.getCarNum());
        params.put(IP, request.getIp());
        params.put(SUPPLIER_CODE, request.getSupplierCode());
        params.put(SUPPLIER_NAME, request.getSupplierName());
        params.put(DEVICE_ID, request.getDeviceId());
        return params;
    }
    
    private void paramCheck(UnifyCheckRequest request) throws BizException {
        if (null == request.getExt()) {
            request.setExt(new HashMap<>());
        }
        CheckUtil.notBlankCheck(request.getScene(), "场景值不可为空");
        CheckUtil.notBlankCheck(request.getProductLine(), "业务线不可为空");
        if (!Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())) {
            CheckUtil.notBlankCheck(request.getChannel(), "渠道不可为空");
        }
        StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
        CheckUtil.notNullCheck(sceneEnum, "场景值异常");
        switch (sceneEnum) {
            case DRIVER_REGISTER:
                CheckUtil.notBlankCheck(request.getDeviceId(), "deviceId不可为空");
                break;
            case USER_CREATE_ORDER:
            case USER_DISPATCHING_ORDER:
                CheckUtil.notBlankCheck(request.getMemberId(), "memberId不可为空");
                break;
            case ACT_COUPON_LQ:
                CheckUtil.notAllNullCheck("memberId和unionId不可同时为空", request.getMemberId(), request.getUnionId());
                break;
            case CANCEL_REMINDER:
                CheckUtil.notBlankCheck(request.getOrderId(),"orderId不可为空");
                break;
            case DRIVER_ACCEPT_ORDER:
            default:
                break;
        }

    }


    public RiskSceneResult strategyCheck(UnifyCheckRequest request) {
        
        Map<String, Object> paramMap = fillParam(request);
        
        try {
            // 1.上下文
            StrategyContext strategyContext = buildContext(paramMap, request);
            // 2.对入参一些字段，进行加密处理，
            encryptParam(strategyContext);
            // 3.找策略
            findStrategy(strategyContext);
            // 4.自检指标
            fillOrderParam(strategyContext);
            // 5.执行
            strategyHandle(strategyContext);
            // 6.针对策略的后置处理
            strategyPostProcessing(strategyContext);

            return strategyContext.getResult();
        } finally {
            localCache.remove();
            localObjectCache.remove();
        }
    }
    
    private void fillOrderParam(StrategyContext strategyContext) {
        Map<String, Object> params = strategyContext.getParams();
        
        // 指标需要对当前订单校验，补充订单详情。
        // 需要限制调用次数：目前仅用于8-1
        if (CollectionUtils.isNotEmpty(strategyContext.getStrategyList())
                && StringUtils.isNotBlank((String) params.get(ORDER_ID))
                && Objects.equals(StrategySceneEnum.FINISH_ORDER.getScene(), strategyContext.getRequest().getScene())) {
            
            boolean basedOnCurrent = strategyContext.getStrategyList().stream().anyMatch(s -> s.getRules().stream().anyMatch(r -> r.getRiskFields().stream().anyMatch(f -> Objects.equals(f.getBasedCurrent(), 1))));
            if (basedOnCurrent) {
                CarOrderDetail orderDetail = carOrderService.queryOrderDetail((String) params.get(ORDER_ID));
                params.put(ORDER, orderDetail);
            }
            
        }
    }
    
    /**
     * 1. 组装上下文
     */
    private StrategyContext buildContext(Map<String, Object> params, UnifyCheckRequest request) {
        StrategyContext strategyContext = new StrategyContext();
        strategyContext.setParams(params);
        strategyContext.setRequest(request);
        return strategyContext;
    }
    
    /**
     * 2. 对某些入参字段进行加密操作，这样才能和库里的数据一致
     *
     * @param strategyContext
     */
        private void encryptParam(StrategyContext strategyContext) {
        // 不需要加解密操作
    }
    
    /**
     * 3.获取对应策略
     */
    public void findStrategy(StrategyContext context) {
        Map<String, Object> params = context.getParams();
        // 1. 找到对应策略，策略里面包含了规则及指标
        String productLine = (String) params.get(PRODUCT_LINE);
        String channel = null == params.get(CHANNEL) ? StringUtils.EMPTY : (String) params.get(CHANNEL);
        String scene = (String) params.get(SCENE);
        String supplierCode = (String) params.get(SUPPLIER_CODE);
        List<RiskStrategyDetail> strategyList = strategyHelper.findStrategy(productLine, channel, scene, supplierCode);
        String strategyNos = strategyList.stream().map(RiskStrategyDetail::getStrategyNo).collect(Collectors.joining(","));
        LoggerUtils.info(log, "获取到对应策略:{} ", strategyNos);
        context.setStrategyList(strategyList);
    }
    
    /**
     * 4.策略执行
     *
     * @param context
     */
    private void strategyHandle(StrategyContext context) {
        List<RiskStrategyDetail> strategyList = context.getStrategyList();
        if (CollUtil.isEmpty(strategyList)) {
            return;
        }
        Map<String, RiskStrategyResult> strategyResultMap = new HashMap<>();
        for (RiskStrategyDetail strategyDetail : strategyList) {
            RiskStrategyResult simpleStrategyResult = checkStrategy(strategyDetail, context.getParams());
            strategyResultMap.put(strategyDetail.getStrategyNo(), simpleStrategyResult);
        }
        LoggerUtils.info(log, "策略执行结果:{}", JSON.toJSONString(strategyResultMap));
        context.setStrategyResultMap(strategyResultMap);
    }
    
    /**
     * 5.策略结果的判定
     *
     * @param strategyContext
     */
    private void strategyPostProcessing(StrategyContext strategyContext) {
        // 响应
        RiskSceneResult result = new RiskSceneResult();
        strategyContext.setResult(result);
        
        Map<String, Object> params = strategyContext.getParams();
        CarOrderDetail orderDetail = (CarOrderDetail) params.get(ORDER);
        // 先释放掉
        params.remove(ORDER);
        Map<String, RiskStrategyResult> strategyResultMap = strategyContext.getStrategyResultMap();
        if (CollUtil.isEmpty(strategyResultMap)) {
            result.setRiskFlag(false);
            result.setRiskMsg("风控策略通过");
            return;
        }
        List<Long> matchStrategyIds = strategyResultMap.values().stream().filter(p -> p.getStrategyMatched()).map(p -> p.getStrategyId()).collect(Collectors.toList());
        List<RiskStrategyDetail> strategyList = strategyContext.getStrategyList().stream()
                .filter(p -> matchStrategyIds.contains(p.getStrategyId())).collect(Collectors.toList());
        
        // 过滤掉用于测试的策略
        strategyList = strategyList.stream().filter(p -> p.getStatus() != 0).collect(Collectors.toList());
        if (CollUtil.isEmpty(strategyList)) {
            result.setRiskFlag(false);
            result.setRiskMsg("命中测试策略，风控策略通过");
            return;
        }
        
        // 根据场景选择策略排序方式
        String scene = (String) params.get(SCENE);
        RiskStrategyDetail disposeStrategy;
        if (FREEZE_TIME_PRIORITY_SCENES.contains(scene)) {
            // 特定场景：按freezeTime从大到小排序，取freezeTime最大的策略
            disposeStrategy = strategyList.stream()
                    .sorted(Comparator.comparing(RiskStrategyDetail::getFreezeTime).reversed())
                    .findFirst().get();
            //获取最大冻结时间
            replenishFreezeTime(strategyList, result, params,orderDetail);
        } else {
            // 其他场景：保持现有逻辑，按level排序
            // 走到这必定有值
            disposeStrategy = strategyList.stream()
                    .sorted(Comparator.comparing(RiskStrategyDetail::getLevel).reversed())
                    .findFirst().get();
        }
        
        List<String> matchedStrategyList = strategyList.stream().map(p -> p.getStrategyNo()).collect(Collectors.toList());
        result.setMatchedStrategy(matchedStrategyList);
        List<RiskStrategyRespResult> strategyRespResultList= strategyList.stream().map(p -> RiskStrategyRespResult.builder().strategyId(p.getStrategyId()).strategyNo(p.getStrategyNo()).strategyName(p.getStrategyName()).build()).collect(Collectors.toList());
        result.setMatchedStrategyList(strategyRespResultList);

        // 如果有命中策略，计入risk_hit中
        RiskStrategyResult disposeStrategyResult = strategyResultMap.get(disposeStrategy.getStrategyNo());
        UnifyCheckRequest request = strategyContext.getRequest();
        if (null != request && null != disposeStrategyResult) {
            Integer controlType = disposeStrategy.getControlType();
            String hitField = disposeStrategy.getHitField();
            Integer disposeAction = disposeStrategy.getDisposeAction();
            
            request.setHitType(0);
            request.setHitRule(String.join(",", disposeStrategyResult.getMatchRules()));
            request.setStrategyNos(disposeStrategyResult.getStrategyNo());
            request.setHitField(StringUtils.defaultString(hitField));
            request.setControlTarget(null == controlType ? "" : String.valueOf(controlType));
            request.setDisposeAction(null == disposeAction ? "" : String.valueOf(disposeAction));
            riskHitService.initHitRisk(request, strategyContext.getResult());
        }
        
        // 过滤掉通过的策略，如果没有策略了，说明命中的策略都是通过的
        strategyList = strategyList.stream().filter(p -> p.getDisposeAction() != 2).collect(Collectors.toList());
        if (CollUtil.isEmpty(strategyList)) {
            result.setRiskFlag(false);
            result.setRiskMsg("命中策略无需拦截");
            return;
        }
        result.setRiskFlag(true);
        result.setRiskMsg(StringUtils.isBlank(disposeStrategy.getStrategyWord()) ? "风控拦截" : disposeStrategy.getStrategyWord());
        replenishFreezeTime(strategyList,result, params,orderDetail);
        // 根据命中策略，进行处置
        for (RiskStrategyDetail strategy : strategyList) {
            doStrategyAction(params, strategy);
        }
        
    }

    //补充冻结时长
    private void replenishFreezeTime(List<RiskStrategyDetail> strategyList, RiskSceneResult result, Map<String, Object> params,CarOrderDetail orderDetail){
        if(CollectionUtils.isEmpty(strategyList)){
            //没有命中的策略,结束
            return;
        }
        RiskStrategyDetail riskStrategyDetail = strategyList.stream().max(Comparator.comparing(RiskStrategyDetail::getFreezeTime)).orElse(null);

        if(Objects.isNull(riskStrategyDetail) || riskStrategyDetail.getFreezeTime() == 0){
            return;
        }
        String orderId = (String) params.get(ORDER_ID);
        LoggerUtils.info(log,"补充冻结时长，取最大冻结时长{}",riskStrategyDetail.getFreezeTime());
        if (riskStrategyDetail.getFreezeTime() > 0 && StringUtils.isNotBlank(orderId)) {
            if(Objects.isNull(orderDetail)){
                orderDetail = carOrderService.queryOrderDetail(orderId);
            }
            if(Objects.nonNull(orderDetail) && Objects.nonNull(orderDetail.getBaseInfo()) && Objects.nonNull(orderDetail.getBaseInfo().getGmtTripFinished())) {
                Date orderFinishTime = orderDetail.getBaseInfo().getGmtTripFinished();
                Date freezeEndTime = DateUtil.addMinutes(orderFinishTime, riskStrategyDetail.getFreezeTime());
                result.setFreezeTime(DateUtil.getNewFormatDateString(freezeEndTime));
            }
        }
    }

    private void doStrategyAction(Map<String, Object> params, RiskStrategyDetail disposeStrategy) {
        if (StringUtils.isBlank(disposeStrategy.getHitField()) || null == disposeStrategy.getControlTime() || disposeStrategy.getControlTime() == 0) {
            return;
        }
        Integer controlTime = disposeStrategy.getControlTime();
        Integer riskType = disposeStrategy.getHitAction();
        String hitField = disposeStrategy.getHitField();
        String customerValue = null;
        
        Integer customerType = 0;
        String passengerPhone = (String) params.getOrDefault(PASSENGER_PHONE, StringUtils.EMPTY);
        String orderId = (String) params.getOrDefault(ORDER_ID, StringUtils.EMPTY);
        String productLine = (String) params.getOrDefault(PRODUCT_LINE, StringUtils.EMPTY);
        String supplierName = (String) params.getOrDefault(SUPPLIER_NAME, StringUtils.EMPTY);
        if (hitField.equals("driverCardNo")) {
            customerValue = (String) params.getOrDefault(CAR_NUM, StringUtils.EMPTY);
            customerType = RiskCustomerCustomerTypeEnum.car_number.getCode();
        } else if (hitField.equals("userId")) {
            customerValue = (String) params.getOrDefault(MEMBER_ID, StringUtils.EMPTY);
            customerType = RiskCustomerCustomerTypeEnum.user_id.getCode();
        } else if (hitField.equals("phone")) {
            customerValue = (String) params.getOrDefault(PASSENGER_PHONE, StringUtils.EMPTY);
            customerType = RiskCustomerCustomerTypeEnum.user_phone.getCode();
        }
        //处理拉黑类型
        Integer resultDisposeType = 7;//默认是1v1
        if (riskType == 0) {
            resultDisposeType = 1;//如果命中动作是0全局拉黑的情况
        }
        
        // 萌艇不落名单
        if (!Objects.equals(StringUtils.upperCase(productLine), "MT")) {
            disposeCenterService.actionCustomer(customerValue, passengerPhone, controlTime
                    , orderId, disposeStrategy.getStrategyNo(), resultDisposeType, customerType, supplierName);
        }
    }
    
    private RiskStrategyResult checkStrategy(RiskStrategyDetail strategyDetail, Map<String, Object> params) {
        String strategyScript = strategyDetail.getScript();
        List<RiskRuleDetail> rules = strategyDetail.getRules();
        if (CollUtil.isEmpty(rules)) {
            return RiskStrategyResult.builder().strategyMatched(false).build();
        }
        Map<String, Object> ruleDataMap = new HashMap<>();
        List<String> matchRules = new ArrayList<>();
        for (RiskRuleDetail rule : rules) {
            // 1.校验规则
            RiskRuleResult simpleRuleResult = checkRule(rule, params);
            LoggerUtils.info(log, "规则:{} 执行结果为:{}", rule.getRuleNo(), simpleRuleResult.getRuleMatched());
            // 2.收集规则结果
            ruleDataMap.put("rule" + rule.getRuleId(), simpleRuleResult.getRuleMatched());
            if (simpleRuleResult.getRuleMatched()) {
                matchRules.add(simpleRuleResult.getRuleNo());
            }
        }
        // 3.校验策略
        boolean strategyMatched = checkStrategyScript(strategyScript, ruleDataMap, strategyDetail.getStrategyNo());
        LoggerUtils.info(log, "策略:{} 执行结果为:{}", strategyDetail.getStrategyNo(), strategyMatched);
        
        return RiskStrategyResult.builder()
                .strategyId(strategyDetail.getStrategyId())
                .strategyNo(strategyDetail.getStrategyNo())
                .strategyMatched(strategyMatched)
                .matchRules(matchRules).build();
        
    }
    
    private RiskRuleResult checkRule(RiskRuleDetail rule, Map<String, Object> params) {
        String ruleScript = rule.getScript();
        List<RiskFieldDetail> riskFields = rule.getRiskFields();
        Map<String, Object> fieldDataMap = new HashMap<>();
        for (RiskFieldDetail riskField : riskFields) {
            String fieldScript = riskField.getScript();
            // 通过groovy脚本，从策略中获取值
            HashMap<String, Object> result = getFieldScriptResult(fieldScript, params, riskField.getFieldNo());
            String num = Optional.ofNullable(result.get("num")).map(String::valueOf).orElse("");
            double value;
            if (StringUtils.isBlank(num)) {
                value = 0.0;
            } else {
                value = new BigDecimal(num).setScale(2, RoundingMode.HALF_UP).doubleValue();
            }
            fieldDataMap.put("field" + riskField.getFieldId(), value);
        }
        boolean ruleResult = checkRuleScript(ruleScript, fieldDataMap, rule.getRuleNo());
        return RiskRuleResult.builder().ruleId(rule.getRuleId()).ruleNo(rule.getRuleNo()).ruleMatched(ruleResult).build();
    }
    
    private Boolean checkStrategyScript(String strategyScript, Map data, String strategyNo) {
        Object[] args = { data };
        Boolean ret = false;
        try {
            ret = (Boolean) GroovyScriptUtil.invokeMethod(strategyScript, "check", args);
        } catch (Exception e) {
            LoggerUtils.warn(log, "执行指标获取，groovy脚本失败,strategyNo:{}", e, strategyNo);
        }
        return ret;
    }
    
    private Boolean checkRuleScript(String ruleScript, Map data, String ruleNo) {
        Object[] args = { data };
        Boolean ret = false;
        try {
            ret = (Boolean) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
        } catch (Exception e) {
            LoggerUtils.warn(log, "执行指标获取，groovy脚本失败,ruleNo:{}", e, ruleNo);
        }
        return ret;
    }
    
    private HashMap<String, Object> getFieldScriptResult(String ruleScript, Map data, String fieldNo) {
        Object[] args = { data };
        HashMap<String, Object> ret = null;
        try {
            ret = (HashMap<String, Object>) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
        } catch (Exception e) {
            LoggerUtils.error(log, "执行规则,groovy脚本失败,fieldNo:{}", e, fieldNo);
            ret = new HashMap<>();
        }
        return ret;
    }
    
    public List<CarRiskOrderDetail> getLocal(String methodName) {
        HashMap<String, List<CarRiskOrderDetail>> localMap = localCache.get();
        return localMap.get(methodName);
    }
    
    public void setLocal(String methodName, List<CarRiskOrderDetail> orderList) {
        HashMap<String, List<CarRiskOrderDetail>> localMap = localCache.get();
        localMap.put(methodName, orderList);
    }

    public Object getObjectLocal(String methodName) {
        HashMap<String, Object> localMap = localObjectCache.get();
        return localMap.get(methodName);
    }
    
    public void setObjectLocal(String methodName, Object orderList) {
        HashMap<String, Object> localMap = localObjectCache.get();
        localMap.put(methodName, orderList);
    }
}
