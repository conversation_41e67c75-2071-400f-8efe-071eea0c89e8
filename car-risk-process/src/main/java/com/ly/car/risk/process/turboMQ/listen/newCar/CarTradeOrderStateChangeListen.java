package com.ly.car.risk.process.turboMQ.listen.newCar;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.turboMQ.consumer.BingLogSfcOrderInfoConsumer;
import com.ly.car.risk.process.turboMQ.consumer.newCar.CarTradeOrderStateChangeConsumer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Description of CarTradeOrderStateChangeListen
 *
 * <AUTHOR>
 * @date 2024/3/13
 * @desc 用车订单状态变更
 */
@Service
@Slf4j
public class CarTradeOrderStateChangeListen implements ApplicationListener<ApplicationStartedEvent>, DisposableBean {

    @Value("${config.turbo-mq.orderStateChangeConsumerGroup}")
    private String GROUP;

    @Resource
    private UrlsProperties urlsProperties;

    @Resource
    private CarTradeOrderStateChangeConsumer orderStateChangeConsumer;

    @Value("${config.turbo-mq.orderStateChangeTopic}")
    private String orderStateChangeTopic;
    
    private DefaultMQPushConsumer consumer;
    
    @Override
    @SneakyThrows
    public void onApplicationEvent(ApplicationStartedEvent event) {

        consumer = new DefaultMQPushConsumer(GROUP);
        // 这里要用公共的MQ地址
        consumer.setNamesrvAddr(urlsProperties.getPublicMqServer());
        //每次拉去N条消息
        consumer.setConsumeMessageBatchMaxSize(1024);
        //设置消费模式=集群
        consumer.setMessageModel(MessageModel.CLUSTERING);
        //订阅PushTopic下tag为push的消息
        consumer.subscribe(orderStateChangeTopic,"*");
        consumer.registerMessageListener(orderStateChangeConsumer);
        consumer.start();
        log.info("[MqStartupRunner][initTurboConsumer][orderStateChangeConsumer][]启动tuborMQ消费者-订单状态变更");
    }
    
    @Override
    public void destroy() {
        if(null != consumer){
            consumer.shutdown();
        }
    }
}
