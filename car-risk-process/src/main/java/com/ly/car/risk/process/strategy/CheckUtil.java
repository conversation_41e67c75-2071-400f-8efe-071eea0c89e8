package com.ly.car.risk.process.strategy;

import com.ly.car.risk.process.model.exception.BizException;
import org.apache.commons.lang3.StringUtils;

/**
 * Description of CheckUtil
 *
 * <AUTHOR>
 * @date 2024/7/25
 * @desc
 */
public class CheckUtil {

    public static void notBlankCheck(String value,String errMsg) throws BizException {
        if(StringUtils.isBlank(value)){
            throw new BizException(-1,errMsg);
        }
    }

    public static void notNullCheck(Object value,String errMsg) throws BizException {
        if(null == value){
            throw new BizException(-1,errMsg);
        }
    }

    public static void notAllNullCheck(String errMsg,String... values) throws BizException {
        if(StringUtils.isAllBlank(values)){
            throw new BizException(-1,errMsg);
        }
    }
}