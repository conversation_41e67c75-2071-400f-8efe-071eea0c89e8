package com.ly.car.risk.process.turboMQ.listen;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.turboMQ.consumer.SfcStatusChangeConsumer;
import com.ly.car.risk.process.turboMQ.consumer.VirtualPhoneConsumer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class VirtualPhoneListen implements ApplicationListener<ApplicationStartedEvent>, DisposableBean {

    private static final String GROUP = "risk_group_virtual_phone_listen";
    private static final String TOPIC = "topic_car_base_service_sound_create_notify";

    @Resource
    private UrlsProperties urlsProperties;
    
    private DefaultMQPushConsumer consumer;
    
    @SneakyThrows
    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        consumer = new DefaultMQPushConsumer(GROUP);
        consumer.setNamesrvAddr(urlsProperties.getPublicMqServer());
        //每次拉去N条消息
        consumer.setConsumeMessageBatchMaxSize(1024);
        //设置消费模式=集群
        consumer.setMessageModel(MessageModel.CLUSTERING);
        //订阅PushTopic下tag为push的消息
        consumer.subscribe(TOPIC,"");
        consumer.registerMessageListener(new VirtualPhoneConsumer());
        consumer.start();
        log.info("[MqStartupRunner][initTurboConsumer][VirtualPhoneListen][]启动tuborMQ消费者-监听虚拟号");
    }
    
    @Override
    public void destroy() {
        if(null != consumer){
            consumer.shutdown();
        }
    }
}
