package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.constants.SceneToCustomerType;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户名单
 * */
@Service
@Slf4j
@Scope("prototype")
public class CustomerUserService extends FilterSfcHandler{

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;

    @Override
    public void doHandler(FilterSfcContext context) {
        //先看下当前场景下判断几种名单类型
        List<Integer> customerTypeList = SceneToCustomerType.sfcCustomerTypeMap.get(context.getMainScene()+"-"+context.getChildScene());
        log.info("[CustomerUserService][doHandler][][]需要过滤名单{}",JsonUtils.json(context.getRiskCustomerManageList()));
        //过滤名单类型
        List<RiskCustomerManage> customerManageList = new ArrayList<>();
        if(CollectionUtils.isEmpty(customerTypeList)){
            customerManageList = context.getRiskCustomerManageList();
        } else {
            customerManageList = context.getRiskCustomerManageList().stream()
                    .filter(e -> customerTypeList.contains(e.getRiskType())).collect(Collectors.toList());
        }
        //1v1的判断拦截
        RiskCustomerManage oneToOneManage = customerManageList.stream().filter(e -> e.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode())
                && StringUtils.isNotBlank(context.getPassengerCellphone()) && context.getPassengerCellphone().equals(e.getBindUser())).findFirst().orElse(null);
        if(oneToOneManage != null){
            log.info("[CustomerUserService][doHandler][][]命中一对一黑名单{}", JsonUtils.json(oneToOneManage));
            RiskResultDTO dto = new RiskResultDTO(405,"命中一对一黑名单",null,oneToOneManage.getCustomerValue());
            context.getUiResult().setData(dto);
            context.getUiResult().setMsg("风控不通过");
            distributionRiskManageService.addByCustomerChain(context.getOrderId(), RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode().toString(),
                    context.getMainScene(), context.getChildScene(), 1,oneToOneManage.getCustomerValue(), RiskLevelEnum.HIGH.getCode());

            riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(RiskCustomerRiskTypeEnum.getMsgByCode(oneToOneManage.getRiskType()),
                    RiskLevelEnum.HIGH.getCode(),1,oneToOneManage.getCustomerValue(),context.getUiResult()));


            return;
        }
        //白名单直接过
        RiskCustomerManage whiteManage = customerManageList.stream().filter(e -> e.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode())).findFirst().orElse(null);
        if(whiteManage != null){
            return;
        }
        //黑名单直接拦截
        RiskCustomerManage blackManage = customerManageList.stream().filter(e -> e.getRiskType().equals(RiskCustomerRiskTypeEnum.black_list.getCode())).findFirst().orElse(null);
        if(blackManage != null){
            log.info("[CustomerUserService][doHandler][][]命中黑名单{}", JsonUtils.json(blackManage));
            RiskResultDTO dto = new RiskResultDTO(405,"命中黑名单",null,blackManage.getCustomerValue());
            context.getUiResult().setData(dto);
            context.getUiResult().setMsg("风控不通过");
            distributionRiskManageService.addByCustomerChain(context.getOrderId(), RiskCustomerRiskTypeEnum.black_list.getCode().toString(),
                    context.getMainScene(), context.getChildScene(), 1,blackManage.getCustomerValue(), RiskLevelEnum.HIGH.getCode());
            riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(RiskCustomerRiskTypeEnum.getMsgByCode(blackManage.getRiskType()),
                    RiskLevelEnum.HIGH.getCode(),1,blackManage.getCustomerValue(),context.getUiResult()));
            return;
        }

        //剩下的有命中直接返回
        List<RiskCustomerManage> hitList = customerManageList.stream().filter(e->!e.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode()) &&
                !e.getRiskType().equals(RiskCustomerRiskTypeEnum.black_list.getCode()) && !e.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode()))
                .collect(Collectors.toList());
        if(hitList != null && hitList.size() > 0){
            log.info("[CustomerUserService][doHandler][][]命中黑名单{}", JsonUtils.json(hitList.get(0)));
            RiskResultDTO dto = new RiskResultDTO(405,"命中名单风控",null,hitList.get(0).getCustomerValue());
            context.getUiResult().setData(dto);
            context.getUiResult().setMsg("风控不通过");
            distributionRiskManageService.addByCustomerChain(context.getOrderId(), hitList.get(0).getRiskType().toString(),
                    context.getMainScene(), context.getChildScene(), 1,hitList.get(0).getCustomerValue(), RiskLevelEnum.HIGH.getCode());

            riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(RiskCustomerRiskTypeEnum.getMsgByCode(hitList.get(0).getRiskType()),
                    RiskLevelEnum.HIGH.getCode(),1,hitList.get(0).getCustomerValue(),context.getUiResult()));

            return;
        }
        //看下是否只判断名单
        if(context.getRuleOnOff().get("customer")){
            return;
        }

        //没有就下一个
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }
    }
}
