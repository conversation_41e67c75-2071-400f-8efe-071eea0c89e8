package com.ly.car.risk.process.service.task;

import com.google.common.collect.Lists;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.ActivityRelation;
import com.ly.car.risk.process.service.ActivityRelationService;
import com.ly.car.risk.process.service.RiskOrderManageService;
import com.ly.car.risk.process.service.dto.task.ActivitySfcOrder;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityRiskStrategyService {

    @Resource
    private ActivityRelationService activityRelationService;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private RiskOrderManageService riskOrderManageService;

    /**
     * 乘推乘是被邀请者完单后  邀请者有现金奖励
     * */
    public void computeRiskOrder(){
        log.info("[][][][]开始执行活动风险策略");
        //获取当前符合的活动id
        Long activityId = queryActivityId();
        if(activityId == null){
            return;
        }
        log.info("[][][][]开始执行活动风险策略id{}",activityId);
        List<ActivityRelation> activityRelationList = this.activityRelationService.queryActivityByUpdateTime(activityId);
        //过滤未完单
        List<ActivityRelation> haveFinishOrderRelationList = activityRelationList.stream()
                .filter(data-> StringUtils.isNotBlank(data.getOrderId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(haveFinishOrderRelationList)){
            log.info("[][][][]开始执行活动风险策略id{}无有效订单",activityId);
            return;
        }
        //查询订单所有信息
        List<String> orderIds = new ArrayList<>();
        for(ActivityRelation relation : haveFinishOrderRelationList){
            orderIds.addAll(new ArrayList<>(Arrays.asList(relation.getOrderId().split(",")[0])));
        }
        List<List<String>> splitOrderIds = Lists.partition(orderIds,500);
        List<ActivitySfcOrder> activitySfcOrderList = new ArrayList<>();
        //分批查询订单信息
        for(List<String> orderList : splitOrderIds){
            activitySfcOrderList.addAll(sfcOrderMapper.queryActivitySfcOrder(orderList));
        }
        //对订单号进行映射
        Map<String,ActivitySfcOrder> activitySfcOrderMap = activitySfcOrderList.stream()
                .collect(Collectors.toMap(ActivitySfcOrder::getOrderId,v->v,(old,cur)->old));

        //ctc_001 先对邀请者进行分组，过滤下邀请少于3个用户的
        //24小时内，司机关联订单，乘客由同一个用户邀请的订单数>3
        Map<String, List<ActivityRelation>> activityRelationGroup = haveFinishOrderRelationList.stream()
                .filter(data->data.getUpdateTime().after(TimeUtil.oneDay()))
                .collect(Collectors.groupingBy(ActivityRelation::getInviterUserId));
        for(Map.Entry<String,List<ActivityRelation>> entry : activityRelationGroup.entrySet()){
            if(entry.getValue().size() < 4){
                continue;
            }
            List<ActivitySfcOrder> compareList = new ArrayList<>();
            for(ActivityRelation relation : entry.getValue()){
                compareList.add(activitySfcOrderMap.get(relation.getOrderId().split(",")[0]));
            }
            //对比较订单再进行司机分组
            Map<String,List<ActivitySfcOrder>> activitySfcOrderGroup = compareList.stream()
                    .collect(Collectors.groupingBy(ActivitySfcOrder::getPlateNumber));

            for(Map.Entry<String,List<ActivitySfcOrder>> compareEntry : activitySfcOrderGroup.entrySet()){
                if(compareEntry.getValue().size() < 4){
                    continue;
                }
                dealCtcRiskOrder(compareEntry.getValue(), "ctc_001");
            }
        }

        //ctc_003
        List<ActivityRelation> activityRelation003List = haveFinishOrderRelationList.stream()
                .filter(data->data.getUpdateTime().after(TimeUtil.oneHour()))
                .collect(Collectors.toList());
        //获取需要处理的订单信息
        List<ActivitySfcOrder> activitySfcOrder003List = new ArrayList<>();
        for(ActivityRelation relation : activityRelation003List){
            activitySfcOrder003List.add(activitySfcOrderMap.get(relation.getOrderId().split(",")[0]));
        }
        //对订单进行过滤，司机分组
        Map<String, List<ActivitySfcOrder>> activitySfcOrder003Group = activitySfcOrder003List.stream()
                .filter(data -> data.getTotalAmount() < 2000)
                .collect(Collectors.groupingBy(ActivitySfcOrder::getPlateNumber));

        for(Map.Entry<String,List<ActivitySfcOrder>> entry : activitySfcOrder003Group.entrySet()){
            if(entry.getValue().size() > 3){
                dealCtcRiskOrder(entry.getValue(), "ctc_003");
            }
        }

        //ctc_004
        List<ActivityRelation> activityRelation004List = haveFinishOrderRelationList.stream()
                .filter(data->data.getUpdateTime().after(TimeUtil.oneDay()))
                .collect(Collectors.toList());
        //获取需要处理的订单信息
        List<ActivitySfcOrder> activitySfcOrder004List = new ArrayList<>();
        for(ActivityRelation relation : activityRelation004List){
            activitySfcOrder004List.add(activitySfcOrderMap.get(relation.getOrderId().split(",")[0]));
        }
        //对订单进行过滤，司机分组
        Map<String, List<ActivitySfcOrder>> activitySfcOrder004Group = activitySfcOrder004List.stream()
                .filter(data -> data.getTotalAmount() < 2000)
                .collect(Collectors.groupingBy(ActivitySfcOrder::getPlateNumber));

        for(Map.Entry<String,List<ActivitySfcOrder>> entry : activitySfcOrder004Group.entrySet()){
            if(entry.getValue().size() > 10){
                dealCtcRiskOrder(entry.getValue(), "ctc_004");
            }
        }

        //ctc_005,对邀请人进行分组
        List<String> haveDealOrderIds = new ArrayList<>();
        Map<String, List<ActivityRelation>> activitySfcOrder005Group = haveFinishOrderRelationList.stream()
                .collect(Collectors.groupingBy(ActivityRelation::getInviterUserId));
        for(Map.Entry<String,List<ActivityRelation>> entry : activitySfcOrder005Group.entrySet()){
            if(entry.getValue().size() < 4){
                continue;
            }
            List<ActivitySfcOrder> activitySfcOrder005List = new ArrayList<>();
            for(ActivityRelation relation : entry.getValue()){
                activitySfcOrder005List.add(activitySfcOrderMap.get(relation.getOrderId().split(",")[0]));
            }
            log.info("[][][][]ctc005当前邀请者关联订单{}",JsonUtils.json(activitySfcOrder005List));
            //查看同 出发地和目的地
            for(int i=0;i<activitySfcOrder005List.size()-1;i++){
                //当前订单的经纬度和后面的比较
                ActivitySfcOrder beforeActivityOrder = activitySfcOrder005List.get(i);
                List<ActivitySfcOrder> shortDistanceActivityList = new ArrayList<>();
                for(int j=0;j<activitySfcOrder005List.size();j++){
                    if(i==j){
                        continue;
                    }
                    ActivitySfcOrder afterActivityOrder = activitySfcOrder005List.get(j);
                    if(new BigDecimal(CoordUtil.getDistance(beforeActivityOrder.getStartLng(),beforeActivityOrder.getStartLat()
                            ,afterActivityOrder.getStartLng(),afterActivityOrder.getStartLat())).compareTo(new BigDecimal(200))<0
                        && new BigDecimal(CoordUtil.getDistance(beforeActivityOrder.getEndLng(),beforeActivityOrder.getEndLat()
                            ,afterActivityOrder.getEndLng(),afterActivityOrder.getEndLat())).compareTo(new BigDecimal(200))<0
                    ){
                        shortDistanceActivityList.add(afterActivityOrder);
                    }
                }
                shortDistanceActivityList.add(beforeActivityOrder);
                if(shortDistanceActivityList.size() > 3){
                    //大于三笔订单的话就要处理相同位置的 需要处理3笔后的
                    List<ActivitySfcOrder> sortedActivitySfcOrderList = shortDistanceActivityList.stream()
                            .sorted(Comparator.comparing(ActivitySfcOrder::getFinishTime)).collect(Collectors.toList());
                    log.info("[][][][]{}排序后集合元素为：{}", "ctc_005",JsonUtils.json(sortedActivitySfcOrderList));
                    sortedActivitySfcOrderList = sortedActivitySfcOrderList.subList(3,sortedActivitySfcOrderList.size());
                    //循环插入风险订单
                    for(ActivitySfcOrder order : sortedActivitySfcOrderList){
                        if(haveDealOrderIds.contains(order.getOrderId())){
                            continue;
                        }
                        haveDealOrderIds.add(order.getOrderId());
                        RiskOrderManage riskOrderManage = new RiskOrderManage();
                        riskOrderManage.setIsRisk(1);
                        riskOrderManage.setRuleNo("ctc_005");
                        riskOrderManage.setOrderId(order.getOrderId());
                        this.riskOrderManageService.addRiskOrder(riskOrderManage);
                    }
                } else {
                    //说明第一笔订单和其他订单无关联，不用关了,这个纯粹记录
                }
            }

        }
        log.info("[][][][]执行活动风险策略结束");
        //ctc_006
    }



    public void dealCtcRiskOrder(List<ActivitySfcOrder> activitySfcOrderList,String ruleNo){
        //对命中订单进行排序,3单以后的全部放入风险订单里面去
        List<ActivitySfcOrder> sortedActivitySfcOrderList = activitySfcOrderList.stream()
                .sorted(Comparator.comparing(ActivitySfcOrder::getFinishTime)).collect(Collectors.toList());
        log.info("[][][][]{}排序后集合元素为：{}", ruleNo,JsonUtils.json(sortedActivitySfcOrderList));
        sortedActivitySfcOrderList = sortedActivitySfcOrderList.subList(3,sortedActivitySfcOrderList.size());
        log.info("[][][][]开始执行活动风险策略{}识别风险订单{}",ruleNo,JsonUtils.json(sortedActivitySfcOrderList));
        //循环插入风险订单
        for(ActivitySfcOrder order : sortedActivitySfcOrderList){
            RiskOrderManage riskOrderManage = new RiskOrderManage();
            riskOrderManage.setIsRisk(1);
            riskOrderManage.setRuleNo(ruleNo);
            riskOrderManage.setOrderId(order.getOrderId());
            this.riskOrderManageService.addRiskOrder(riskOrderManage);
        }
    }

    public Long queryActivityId(){
        try {
            String ctcActivityId = ConfigCenterClient.get("ctc_activity_config");
            return Long.valueOf(ctcActivityId);
        } catch (Exception e) {
            log.error("[][][][]获取乘推乘活动id错误",e);
        }
        return null;
    }

}
