package com.ly.car.risk.process.service.dto;

import lombok.Data;

import java.util.List;

@Data
public class RiskHitLinkDTO {

    private String requestId;
    private String orderId;
    private String ruleNo;
    private List<String> linkOrderIds;

    public RiskHitLinkDTO(String requestId,String orderId,String ruleNo,List<String> linkOrderIds){
        this.requestId = requestId;
        this.orderId = orderId;
        this.ruleNo = ruleNo;
        this.linkOrderIds = linkOrderIds;
    }

}
