package com.ly.car.risk.process.supplier.yueyue;

import com.ly.car.risk.process.utils.GsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 文本混淆工具类
 */
@Slf4j
public final class TextConfoundUtil {


    /**
     * 签名数据混淆
     *
     * @param obj 混淆对象
     * @return
     */
    public static String confoundSignData(Object obj) {

        TreeMap<String, Object> sortMap = new TreeMap<>(new MapKeyComparator());
        try {
            Map<String, Object> map;
            if (obj instanceof Map) {
                map = (Map) obj;
            } else {
                Map<String, Object> convertMap = new HashMap<>();
                Class<?> clazz = obj.getClass();
                Arrays.stream(clazz.getDeclaredFields()).forEach(field -> {
                    field.setAccessible(true);
                    try {
                        Optional.ofNullable(field.get(obj))
                                .ifPresent(value -> {
                                    convertMap.put(field.getName(), value);
                                });
                    } catch (IllegalAccessException e) {
                    }
                });
                map = convertMap;
            }

            sortMap.putAll(map);
        } catch (Exception e) {
            log.error("对象转map异常错误信息：", e);
            return StringUtils.EMPTY;
        }
        StringBuilder sortStr = new StringBuilder();
        sortMap.keySet().stream().filter(key -> !key.equals("sign")).forEach(key -> {
            sortStr.append(key);
            sortStr.append("=");
            if (canToString(sortMap.get(key))) {
                sortStr.append(sortMap.get(key));
            } else {
                sortStr.append(GsonHelper.convertJson(sortMap.get(key)));
            }
            sortStr.append("&");
        });
        return sortStr.substring(0, sortStr.length() - 1);
    }


    public static boolean canToString(Object o) {
        return (o instanceof Integer
                || o instanceof Long
                || o instanceof Double
                || o instanceof String);
    }


}


class MapKeyComparator implements Comparator {

    @Override
    public int compare(Object o1, Object o2) {
        String str1 = (String) o1;
        String str2 = (String) o2;
        return str1.compareTo(str2);
    }
}