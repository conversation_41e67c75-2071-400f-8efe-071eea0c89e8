package com.ly.car.risk.process.service.workOrder;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.api.rsp.SupplierSyncRsp;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.SupplierAppealRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.TcSupplierWorkOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.YueJudgeWorkOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.SupplierAppealRecord;
import com.ly.car.risk.process.repo.risk.mapper.entity.TcSupplierWorkOrder;
import com.ly.car.risk.process.repo.risk.mapper.entity.YueJudgeWorkOrder;
import com.ly.car.risk.process.service.ConfigCenterService;
import com.ly.car.risk.process.service.dto.config.WorkOrderSyncConfig;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.supplier.SupplierYueYueClientAbstract;
import com.ly.car.risk.process.supplier.yueyue.*;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.sharding.order.entity.OrderDispatch;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderDispatchMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

@Service
@Slf4j
public class SupplierYueYueClient extends SupplierYueYueClientAbstract {
    public Map<String,String> violationMap;
    public SupplierYueYueClient(){
        violationMap = new HashMap<>();
//        violationMap.put("1","11007");
        violationMap.put("2-1","11212");
        violationMap.put("2-2","11303");
        violationMap.put("3","11601");
//        violationMap.put("4","11205");
        violationMap.put("5","11215");
//        violationMap.put("6","11206");
//        violationMap.put("7","11621");

    }

    @Resource
    private SupplierYueYueService supplierYueYueService;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderDispatchMapper orderDispatchMapper;
    @Resource
    private YueJudgeWorkOrderMapper yueJudgeWorkOrderMapper;
    @Resource
    private TcSupplierWorkOrderMapper tcSupplierWorkOrderMapper;
    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;
    @Resource
    private SupplierAppealRecordMapper supplierAppealRecordMapper;
    @Resource
    private CarOrderService carOrderService;

    public YueYueDisposeRsp syncDispose(String orderId, Integer beforeViolationId, BigDecimal amount){
        String violationId = String.valueOf(beforeViolationId);
        if(beforeViolationId > 10){
            violationId = violationId.toCharArray()[0]+"-"+violationId.toCharArray()[1];
        }
        String supplierCode;
        String supplierFullCode;
        String supplierOrderId;
        String tenantId = StringUtils.EMPTY;
        String subOrderId;
        if(OrderUtils.isNewOrder(orderId)){
            CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
            supplierCode = orderDetail.getCarInfo().getSupplierCode();
            supplierFullCode = orderDetail.getCarInfo().getSupplierFullCode();
            supplierOrderId = orderDetail.getSupplierOrderId();
        }else {
            OrderInfo orderInfo = orderInfoMapper.findByOrderId(orderId);
            supplierCode = orderInfo.getSupplierCode();
            supplierFullCode = orderInfo.getSupplierCodeFull();
            supplierOrderId = orderInfo.getSupplierOrderId();
        }

        if(!"YueYue".equalsIgnoreCase(supplierCode) && !"YueYueTHFixedPrice".equalsIgnoreCase(supplierCode)){
            return null;
        }
        WorkOrderSyncConfig syncConfig = ConfigCenterService.getSyncConfig();
        if(StringUtils.isNotBlank(syncConfig.getYueyueChildSupplier()) && StringUtils.isNotBlank(supplierFullCode) && !syncConfig.getYueyueChildSupplier().contains(supplierFullCode)){
            return null;
        }

        String violationMapId = violationMap.get(violationId);
        if(StringUtils.isBlank(violationMapId)){
            log.info("[][][][]当前违规id未处理");
            return null;
        }

        LocalDate date = LocalDate.now();
        int year = date.getYear();
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();
        int ymd = year * 10000 + month * 100 + day;

        YueYueRiskSyncParam param = new YueYueRiskSyncParam();
        YueYueRiskSyncParam.EventDTO eventDTO = new YueYueRiskSyncParam.EventDTO();
        YueYueRiskSyncParam.OrderDTO orderDTO = new YueYueRiskSyncParam.OrderDTO();
        YueYueRiskSyncParam.ViolationDTO violationDTO = new YueYueRiskSyncParam.ViolationDTO();

        if(OrderUtils.isNewOrder(orderId)){
            // todo 按正常理解，这地方可能会出现越界异常，因为原dispatch库里，是有那种只存额YueYue的订单,保持原样，错了再说
            tenantId = supplierFullCode.contains("_") ? supplierFullCode.split("_")[1] : StringUtils.EMPTY;
            subOrderId = orderId + (StringUtils.isNotBlank(tenantId) ? "_" + tenantId + "_1" : "");
        }else{
            OrderDispatch orderDispatch = orderDispatchMapper.findBySupplierOrderId(supplierOrderId);
            if(orderDispatch != null){
                tenantId = orderDispatch.getSupplierCode().split("_")[1];
            }
            subOrderId = orderDispatch.getSubOrderId();
        }
        param.setTenant_id(tenantId);//


//        param.setDriverId(null);
        eventDTO.setEvent_id(ymd+""+getRandomNum());
        eventDTO.setEvent_source("RISK_CONTROL");
        eventDTO.setEvent_type("RISK_WORK_ORDER");
        eventDTO.setEvent_type_name("风控工单");
        eventDTO.setEvent_create_time(System.currentTimeMillis());
        eventDTO.setEvent_operator_time(System.currentTimeMillis());
        eventDTO.setEvent_operator("风控系统");
        param.setEvent_info(eventDTO);

        orderDTO.setChannel_main_order_id(orderId);
        orderDTO.setChannel_order_id(supplierOrderId);
//        orderDTO.setOrderId(orderDispatch.getSupplierOrderId());
//        orderDTO.setOrderType("ORDER");
        param.setOrder_info(orderDTO);

        violationDTO.setViolation_id(ymd+""+getRandomNum());
        violationDTO.setViolation_code(violationMap.get(violationId));
        violationDTO.setViolation_create_time(System.currentTimeMillis());

        YueYueRiskSyncParam.ViolationAppeal violationAppeal = new YueYueRiskSyncParam.ViolationAppeal();
        violationAppeal.setCan_appeal("YES");
        violationAppeal.setNow_appeal_num(0);
        violationAppeal.setMax_appeal_num(syncConfig.getAppealMaxNum());//读取配置
        violationAppeal.setAppeal_end_time(DateUtil.addMinute(new Date(),syncConfig.getAppealEndTime()).getTime());
        violationDTO.setViolation_appeal(violationAppeal);
        violationDTO.setViolation_amount(amount.multiply(new BigDecimal("100")).intValue());
        param.setViolation_info(violationDTO);

        List<YueYueRiskSyncParam.DisposeDTOs> disposeDTOsList = new ArrayList<>();
        YueYueRiskSyncParam.DisposeDTOs disposeDTOs = new YueYueRiskSyncParam.DisposeDTOs();
        disposeDTOs.setDispose_id(ymd+""+getRandomNum());
        disposeDTOs.setDispose_type("LY_PUNISH_CP");//对服务商进行罚款
        disposeDTOs.setDispose_status("PUNISH_ING");//处置状态，当前默认处置中
        disposeDTOs.setDispose_reason(null);//
        disposeDTOs.setDispose_reason_code(violationMap.get(violationId));
        disposeDTOs.setDispose_start_time(System.currentTimeMillis());
        disposeDTOs.setDispose_end_time(DateUtil.addMinute(new Date(),syncConfig.getAppealEndTime()).getTime());
        disposeDTOs.setDispose_amount(amount.multiply(new BigDecimal(100)));
        disposeDTOsList.add(disposeDTOs);
        param.setDispose_infos(disposeDTOsList);
        LoggerUtils.info(log,"约约工单请求参数{}", JsonUtils.json(param));
        YueYueDisposeRsp yueYueDisposeRsp = this.supplierYueYueService.judgeDriver(param);
        LoggerUtils.info(log,"约约工单响应{}", JsonUtils.json(param));

        //开始做同程工单生成
        YueJudgeWorkOrder yueJudgeWorkOrder = new YueJudgeWorkOrder();
        yueJudgeWorkOrder.setTcOrderId(orderId);
        yueJudgeWorkOrder.setSupplierOrderId(supplierOrderId);
        yueJudgeWorkOrder.setEventId(eventDTO.getEvent_id());
        yueJudgeWorkOrder.setEventCreateTime(String.valueOf(eventDTO.getEvent_create_time()));
        yueJudgeWorkOrder.setEventOperatorTime(String.valueOf(eventDTO.getEvent_operator_time()));
        yueJudgeWorkOrder.setChannelOrderId(subOrderId);
        yueJudgeWorkOrder.setViolationId(violationDTO.getViolation_id());
        yueJudgeWorkOrder.setViolationCode(violationDTO.getViolation_code());
        yueJudgeWorkOrder.setViolationCreateTime(String.valueOf(violationDTO.getViolation_create_time()));
        yueJudgeWorkOrder.setAppealEndTime(String.valueOf(violationAppeal.getAppeal_end_time()));
        yueJudgeWorkOrder.setDisposeId(disposeDTOs.getDispose_id());
        yueJudgeWorkOrder.setDisposeReasonCode(disposeDTOs.getDispose_reason_code());
        yueJudgeWorkOrder.setDisposeAmount(amount.multiply(new BigDecimal(100)).intValue());
        yueJudgeWorkOrder.setCreateTime(new Date());
        yueJudgeWorkOrder.setDisposeStartTime(String.valueOf(disposeDTOs.getDispose_start_time()));
        yueJudgeWorkOrder.setDisposeEndTime(String.valueOf(disposeDTOs.getDispose_end_time()));
        this.yueJudgeWorkOrderMapper.insert(yueJudgeWorkOrder);

        TcSupplierWorkOrder tcSupplierWorkOrder = this.tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>().eq("order_id", orderId));
        tcSupplierWorkOrder.setYueEventId(eventDTO.getEvent_id());
        this.tcSupplierWorkOrderMapper.updateById(tcSupplierWorkOrder);
        return yueYueDisposeRsp;
    }


    @Override
    public SupplierSyncRsp syncWorkerOrder(TcSupplierWorkOrder tcSupplierWorkOrder) {
        Integer mainCode = tcSupplierWorkOrder.getWoCateCode();
        Integer childCode = tcSupplierWorkOrder.getChildCateCode();
        YueYueDisposeRsp yueYueDisposeRsp = syncDispose(tcSupplierWorkOrder.getOrderId(), childCode>0?Integer.valueOf(mainCode+""+childCode):mainCode, tcSupplierWorkOrder.getChangeFee());
        SupplierSyncRsp rsp = new SupplierSyncRsp();
        if(yueYueDisposeRsp != null && yueYueDisposeRsp.getCode() == 1){
            rsp.setSuccess(true);//这边暂时没啥用
            return null;//因为返回没有工单号
        }
        return null;
    }

    @Override
    public void syncDisposeResult(JSONObject jsonObject){
        log.info("[][][][]约约处罚下发回调参数{}",JsonUtils.json(jsonObject));
        YueYueDisposeCallbackParam yueYueDisposeRsp = JSONObject.parseObject(jsonObject.toJSONString(),YueYueDisposeCallbackParam.class);
        log.info("[][][][]约约处罚下发回调参数解析{}",JsonUtils.json(yueYueDisposeRsp));
        //这边用eventId反推订单号
        YueJudgeWorkOrder yueJudgeWorkOrder = yueJudgeWorkOrderMapper.selectOne(new QueryWrapper<YueJudgeWorkOrder>()
                .eq("event_id", yueYueDisposeRsp.getEventId())
        );
        log.info("[][][][]查询约约工单{}",JsonUtils.json(yueJudgeWorkOrder));
        //更新同程工单的
        TcSupplierWorkOrder tcSupplierWorkOrder = this.tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>()
                .eq("order_id", yueJudgeWorkOrder.getTcOrderId())
        );
        log.info("[][][][]查询同程工单{}",JsonUtils.json(tcSupplierWorkOrder));
        tcSupplierWorkOrder.setPlatformWorkOrderNo(yueYueDisposeRsp.getCpEventId());
        tcSupplierWorkOrder.setIsSync(1);
        tcSupplierWorkOrder.setUpdateTime(new Date());
        tcSupplierWorkOrder.setJudgeResult(0);//约约默认有责
        tcSupplierWorkOrder.setWorkStatus(1);//待供应商反馈
        this.tcSupplierWorkOrderMapper.updateById(tcSupplierWorkOrder);

        //并且要把风险订单也要更新下是否已同步
        RiskOrderManage manage = riskOrderManageMapper.selectOne(new QueryWrapper<RiskOrderManage>()
                .eq("order_id", tcSupplierWorkOrder.getOrderId())
        );
        if(manage != null){
            manage.setStatus(1);//待供应商反馈
            this.riskOrderManageMapper.updateById(manage);
        }
    }

    @Override
    public String syncAppeal(JSONObject jsonObject) {
        log.info("[][][][]约约申诉回调参数{}",JsonUtils.json(jsonObject));
        //供应商层定义了订单号返回
        YueYueRiskWorkerOrderReq req = JSONObject.parseObject(jsonObject.toJSONString(),YueYueRiskWorkerOrderReq.class);
        log.info("[][][][]约约申诉回调参数解析{}",JsonUtils.json(req));
        //这边用eventId反推订单号
        YueJudgeWorkOrder yueJudgeWorkOrder = yueJudgeWorkOrderMapper.selectOne(new QueryWrapper<YueJudgeWorkOrder>()
                .eq("violation_id", req.getViolationId())
        );
        //超时不允许申诉了
        String endTime = yueJudgeWorkOrder.getAppealEndTime();
        Date endTimeDate = new Date(Long.valueOf(endTime));
        if(endTimeDate.before(new Date())){
            log.info("[][][][]已超过申诉截止时间{}",req.getViolationId());
            return null;
        }

        WorkOrderSyncConfig syncConfig = ConfigCenterService.getSyncConfig();
        //通过订单号+申诉id找到具体是哪个申诉单
        List<SupplierAppealRecord> recordList = this.supplierAppealRecordMapper.selectList(new QueryWrapper<SupplierAppealRecord>()
                .eq("order_id", yueJudgeWorkOrder.getTcOrderId())
        );
        if(CollectionUtils.isNotEmpty(recordList) && recordList.size() >= syncConfig.getAppealMaxNum()){
            log.info("[][][][]已超过最大申诉次数{}",req.getViolationId());
            return null;
        }


        LocalDate date = LocalDate.now();
        int year = date.getYear();
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();
        int ymd = year * 10000 + month * 100 + day;
        String appealId = ymd+""+getRandomNum();
        yueJudgeWorkOrder.setAppealId(appealId);
        this.yueJudgeWorkOrderMapper.updateById(yueJudgeWorkOrder);

        TcSupplierWorkOrder tcSupplierWorkOrder = this.tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>()
                .eq("order_id",yueJudgeWorkOrder.getTcOrderId())
        );
        tcSupplierWorkOrder.setOperateType(1);
        tcSupplierWorkOrder.setAttachments(req.getText() +";" + req.getPicUrl() +";" + req.getSoundUrl());
        tcSupplierWorkOrder.setUpdateTime(new Date());
        tcSupplierWorkOrder.setWorkStatus(2);//待审核
        tcSupplierWorkOrder.setYueEventId(yueJudgeWorkOrder.getEventId());
        this.tcSupplierWorkOrderMapper.updateById(tcSupplierWorkOrder);



        SupplierAppealRecord record = new SupplierAppealRecord();
        record.setOrderId(yueJudgeWorkOrder.getTcOrderId());
        record.setTcAppealId(appealId);
        record.setSupplierAppealId(req.getAppealId());
        record.setAppealText(req.getText());
        record.setAppealPic(req.getPicUrl());
        record.setAppealSound(req.getSoundUrl());
//        record.setAuditStatus();
//        record.setAuditRemark();
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        this.supplierAppealRecordMapper.insert(record);
        return appealId;
    }

    @Override
    public void syncAppealResult(JSONObject jsonObject) {
        log.info("[][][][]约约审核通知");
        log.info("[][][][]审核结果透传{}",JsonUtils.json(jsonObject));
        String orderId = jsonObject.getString("orderId");
        String appealId = jsonObject.getString("appealId");
        String supplierOrderId = jsonObject.getString("supplierOrderId");
        Integer auditStatus = jsonObject.getInteger("auditStatus");
        String auditRemark = jsonObject.getString("auditRemark");
        String eventId = jsonObject.getString("eventId");

        //通过传过来的信息找到传给约约的信息
        YueJudgeWorkOrder yueJudgeWorkOrder = this.yueJudgeWorkOrderMapper.selectOne(new QueryWrapper<YueJudgeWorkOrder>()
                .eq("tc_order_id",orderId).eq("event_id",eventId)
        );
        //通过订单号+申诉id找到具体是哪个申诉单
        List<SupplierAppealRecord> recordList = this.supplierAppealRecordMapper.selectList(new QueryWrapper<SupplierAppealRecord>()
                .eq("order_id", orderId)
        );
        SupplierAppealRecord record = recordList.stream().filter(data->data.getTcAppealId().equals(appealId)).findFirst().orElse(null);

        record.setAuditStatus(auditStatus==1?1:2);
        record.setAuditRemark(auditRemark);
        record.setUpdateTime(new Date());
        this.supplierAppealRecordMapper.updateById(record);

        //驳回的如果是第一笔申诉单，则工单改为待供应商反馈
        TcSupplierWorkOrder tcSupplierWorkOrder = this.tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>()
                .eq("order_id",orderId)
        );
        if(recordList.size() == 1 && auditStatus != 1){
            tcSupplierWorkOrder.setWorkStatus(1);
            tcSupplierWorkOrder.setUpdateTime(new Date());
            this.tcSupplierWorkOrderMapper.updateById(tcSupplierWorkOrder);
        }
        if(jsonObject.getString("auditRemark").equals("自动通过")){
            tcSupplierWorkOrder.setWorkStatus(4);
            tcSupplierWorkOrder.setJudgeResult(2);
            this.tcSupplierWorkOrderMapper.updateById(tcSupplierWorkOrder);
        }

        YueYueAppealParam param = new YueYueAppealParam();
        String tenantId = null;
        if(OrderUtils.isNewOrder(orderId)){
            CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
            String supplierFullCode = orderDetail.getCarInfo().getSupplierFullCode();
            tenantId = supplierFullCode.contains("_") ? supplierFullCode.split("_")[1] : StringUtils.EMPTY;
        }else{
            OrderDispatch orderDispatch = orderDispatchMapper.findBySupplierOrderId(supplierOrderId);
            if(orderDispatch != null){
                tenantId = orderDispatch.getSupplierCode().split("_")[1];
            }
        }
        param.setTenant_id(tenantId);//

        YueYueAppealParam.OrderDTO orderDTO = new YueYueAppealParam.OrderDTO();
        orderDTO.setChannel_main_order_id(orderId);
        orderDTO.setChannel_order_id(supplierOrderId);
        param.setOrder_info(orderDTO);

        /**
         * 违规申诉信息
         * */
        WorkOrderSyncConfig syncConfig = ConfigCenterService.getSyncConfig();
        YueYueAppealParam.ViolationDTO violationDTO = new YueYueAppealParam.ViolationDTO();
        violationDTO.setViolation_id(yueJudgeWorkOrder.getViolationId());
        YueYueAppealParam.ViolationAppeal appeal = new YueYueAppealParam.ViolationAppeal();
        appeal.setCan_appeal("YES");
        appeal.setNow_appeal_num(recordList.size());
        appeal.setMax_appeal_num(syncConfig.getAppealMaxNum());
        appeal.setAppeal_end_time(Long.valueOf(yueJudgeWorkOrder.getAppealEndTime()));
        violationDTO.setViolation_appeal(appeal);
        param.setViolation_info(violationDTO);

        YueYueAppealParam.AppealDTO appealDTO = new YueYueAppealParam.AppealDTO();
        appealDTO.setAppeal_id(record.getTcAppealId());
//        appealDTO.setAppeal_id("2023112933369");
        appealDTO.setAppeal_status(auditStatus==1?"PASS":"REJECT");
        appealDTO.setAppeal_result(auditRemark);
        appealDTO.setAppeal_result_photos(null);
        appealDTO.setAppeal_amount(new BigDecimal(yueJudgeWorkOrder.getDisposeAmount()));
        param.setAppeal_info(appealDTO);
        YueYueDisposeRsp yueYueDisposeRsp = supplierYueYueService.appealDriver(param);

    }

    public int getRandomNum(){
        Random random = new Random();
        int randomNumber = random.nextInt(900_0000) + 10000;
        return randomNumber;
    }

    public static void main(String[] args) {
        BigDecimal a = new BigDecimal("12.1");
        System.out.println(a.multiply(new BigDecimal(100)).intValue());
    }
}
