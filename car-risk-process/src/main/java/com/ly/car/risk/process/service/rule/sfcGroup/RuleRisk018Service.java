package com.ly.car.risk.process.service.rule.sfcGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.rule.FilterOrderContext;
import com.ly.car.risk.process.service.rule.FilterRuleChain;
import com.ly.car.risk.process.service.rule.MapIsAllTrue;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RuleRisk018Service extends FilterRuleChain {
    private static final String ruleNo = "018";
    @Resource
    private RuleRisk019Service ruleRisk019Service;
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;

    /**
     * 018规则
     * 时间区间：当天
     * 完单时间：6分钟
     * 订单量：大于3单
     * */
    @Override
    public UiResult doHandler(FilterOrderContext orderContext) {
        log.info("[FilterRuleChain][RuleRisk018Service][][]前置判断已通过，进入规则018判断{}");
        this.next(ruleRisk019Service);
        //判断当前是否执行此规则
        if(orderContext.getNeedRuleMap().get(ruleNo) == null && this.nextRule != null){
            log.info("[RuleRisk018Service][doHandler][{}][{}]018规则数据step1",orderContext.getMemberId(),orderContext.getDriverCardNo());
            return this.nextRule.doHandler(orderContext);
        }

        //没有下个规则就直接返回了
        if(this.nextRule == null && orderContext.getNeedRuleMap().get(ruleNo) == null){
            return orderContext.getUiResult();
        }

        //执行此规则就给个执行标记
        orderContext.getNeedRuleMap().put(ruleNo,true);
        //计算开始时间戳
        List<OrderRiskContext> orderRiskContextList = orderContext.getDriverContextList().stream().filter(context->context.getIntervalTime() != null)
                .filter(context->context.getIntervalTime() < orderContext.getSfcRiskRuleConfig().getTime018())
                .filter(context->DateUtil.string2Date(context.getFinishTime()).after(TimeUtil.currentDay()))
                .collect(Collectors.toList());
        log.info("[RuleRisk018Service][doHandler][{}][{}]018规则数据{}",orderContext.getMemberId(),orderContext.getDriverCardNo(),orderRiskContextList.size());
        boolean flag = false;
        if((!CollectionUtils.isEmpty(orderRiskContextList)) && orderRiskContextList.size() >= orderContext.getSfcRiskRuleConfig().getOrderNum018()){
            distributionRiskManageService.addByRuleChain(orderRiskContextList,ruleNo,2,1,0,null, RiskLevelEnum.HIGH.getCode());
            log.info("[RuleRisk018Service][doHandler][{}][{}]命中018规则",orderContext.getMemberId(),orderContext.getDriverCardNo());
            UiResult result = UiResult.ok();
            result.setMsg("风控不通过");
            RiskResultDTO dto = new RiskResultDTO(405,"风控不通过018",null,null);
            result.setData(dto);
            orderContext.setUiResult(result);
            flag = true;
//            return result;
        }
        //判断是否已经全部结束，全部结束则返回通过
        if(MapIsAllTrue.isAllTrue(orderContext.getNeedRuleMap()) || this.nextRule == null){
            if(flag){
                riskHitService.initHitRisk(orderContext.getFilterParams(),new HitInfoDTO(ruleNo,
                        RiskLevelEnum.HIGH.getCode(),0,null,orderContext.getUiResult()));
            }
            return orderContext.getUiResult();
        }
        return this.nextRule.doHandler(orderContext);
    }

}
