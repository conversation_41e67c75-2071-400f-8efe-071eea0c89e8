package com.ly.car.risk.process.service.rule.imGrooup;

import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.ChildSceneEnum;
import com.ly.car.risk.process.constants.CustomerConstants;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.service.rule.hcGroup.HcRuleServiceContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class ImConvertHandlerService {

    public List<ImFilterHandler> getImHandlerList(Integer mainScene,Integer childScene){
        List<ImFilterHandler> serviceNameList = new ArrayList<>();
        List<String> childSceneStr = new ArrayList<>();
        if(childScene == null){
            //先取出主场景下所有子场景
            childSceneStr.addAll(ChildSceneEnum.getAllChildStr(mainScene));
        } else {
            childSceneStr.add(mainScene+"-"+childScene);
        }
        //名单类肯定放第一个
        for(String str : childSceneStr){
            List<String> mapService = ImServiceContextUtil.serviceNameMap.get(str);
            for(String serviceStr : mapService){
                if(CustomerConstants.customerMap.get(serviceStr) != null){
                    serviceNameList.add(0, SpringContextUtil.getBean(serviceStr));
                } else {
                    serviceNameList.add(SpringContextUtil.getBean(serviceStr));
                }
            }
        }
        //串联链路
        if(!CollectionUtils.isEmpty(serviceNameList)){
            for(int i = 1;i< serviceNameList.size();i++){
                serviceNameList.get(i-1).next(serviceNameList.get(i));
            }
        }
        return serviceNameList;
    }

    public ImFilterContext convertParam(FilterParams params){
        ImFilterContext context = new ImFilterContext();
        context.setDriverCardNo(params.getDriverCardNo());
        context.setMemberId(params.getMemberId());
        context.setOrderId(params.getOrderId());
        context.setUnionId(params.getUnionId());
        context.setText(params.getText());
        return context;
    }
}
