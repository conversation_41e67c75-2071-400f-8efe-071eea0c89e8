package com.ly.car.risk.process.turboMQ.consumer.newCar;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.OrderComplete;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.handler.HandlerChooseFactory;
import com.ly.car.risk.process.handler.orderstate.AbstractOrderStateHandler;
import com.ly.car.risk.process.repo.risk.mapper.OrderCompleteMapper;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.redis.UpdateScoreSortedSetService;
import com.ly.car.risk.process.turboMQ.dto.BingLogData;
import com.ly.car.risk.process.turboMQ.dto.newCar.CarOrderStateChangeMsg;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.BingLogUtil;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.sharding.order.entity.OrderDriver;
import com.ly.car.sharding.order.entity.OrderExpand;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.entity.OrderSupplierBill;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderExpandMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.car.tradecore.model.enums.OrderType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.ly.car.risk.process.utils.OrderUtils.DISTRIBUTION_TAG;

@Component
@Slf4j
public class CarTradeOrderStateChangeConsumer implements MessageListenerConcurrently {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @Resource
    private CarOrderService carOrderService;
    @Resource
    private HandlerChooseFactory handlerChooseFactory;

    private static String ORDER_STATE_LOCK = "order:%s:from:%s:to:%s";

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msgExt : list) {
            LoggerUtils.initLogMap("tradeOrderState",msgExt.getTags(),msgExt.getMsgId(),"");
            try {
                String body = new String(msgExt.getBody(), "utf-8");
                LoggerUtils.info(log,"订单状态变更，收到消息,body={}",body);
                CarOrderStateChangeMsg changeData = JSON.parseObject(body, new TypeReference<CarOrderStateChangeMsg>() {
                });
                LoggerUtils.getLogMap().put("filter2",changeData.getOrderSerialNo());

                String orderSerialNo = changeData.getOrderSerialNo();
                OrderState fromState = null;
                OrderState toState = null;
                try {
                    fromState = OrderState.valueOf(changeData.getFromState());
                    toState = OrderState.valueOf(changeData.getToState());
                } catch (IllegalArgumentException e) {
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }

                String lockKey = String.format(ORDER_STATE_LOCK, orderSerialNo, fromState.getCode(), toState.getCode());
                RLock lock = redissonClient.getLock(lockKey);
                if (!lock.tryLock(0, 30, TimeUnit.SECONDS)) {
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
                // 是否分销订单
                boolean isDistribution = OrderUtils.isDistributionOrder(changeData.getOrderTags());

                CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderSerialNo);
                if(null == orderDetail){
                    LoggerUtils.info(log,"未查询到对应订单，orderId:{}",orderSerialNo);
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                orderDetail.setDistributionOrder(isDistribution);
                String orderType = OrderUtils.getProductLineByOrderPrefix(orderSerialNo);
                AbstractOrderStateHandler stateHandler = handlerChooseFactory.chooseOrderStateHandler(orderType);
                switch (toState) {
                    case DRAFT:
                        break;
                    case DISPATCHING:
                        // 这里只会走顺风车，另外顺风车下单后的起始状态就是 派单中
                        stateHandler.dealDispatchingState(orderDetail, fromState, toState);
                        break;
                    case CANCELED:  //取消
                        stateHandler.dealCancelState(orderDetail, fromState, toState);
                        break;
                    case RECEIVING_ORDER:   //司机接单
                        stateHandler.dealReceiveOrderState(orderDetail, fromState, toState);
                        break;
                    case RE_DISPATCHING:  //订单改派
                        break;
                    case AWAITING_TRAVEL:   //司机已到达
                        break;
                    case IN_TRIP:   //行程中
                        stateHandler.dealInTripState(orderDetail, fromState, toState);
                        break;
                    case TRIP_FINISHED: //行程结束
                        stateHandler.dealTripFinishState(orderDetail, fromState, toState);
                        break;
                    case ORDER_CLOSED:  //订单完成
                        break;
                    default:
                        break;
                }

            } catch (Exception e) {
                LoggerUtils.error(log,"订单状态变更,发生异常",e);
                log.error("[][][][]专车消费报错:{}", e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            } finally {
                LoggerUtils.removeAll();
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
