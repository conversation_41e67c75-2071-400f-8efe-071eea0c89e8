package com.ly.car.risk.process.service.core;

import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.dal.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/*
*  后面可能有多种处置方式
* */
@Service
public class DisposeCenterService {

    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;

   /**
    * 目前只有名单处置，拉黑各种信息
    * @param customerValue 拉黑的值
    * @param passengerCellphone 乘车人手机号
    * @param ttl 拉黑多少天
    * @param orderId 订单号
    * @param strategyNo 策略编号
    * */
   public void actionCustomer(String customerValue,String passengerCellphone,Integer ttl,String orderId,String strategyNo,Integer riskType,Integer customerType,String supplierName){
       if (StringUtils.isBlank(customerValue)) {
           return;
       }
       RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
       riskCustomerManage.setRiskType(riskType);//直接用1v1和全局拉黑的模式，1-黑名单 7-1v1黑名单
       riskCustomerManage.setCustomerType(customerType);//司机车牌号等等信息
       riskCustomerManage.setCustomerValue(customerValue);
       riskCustomerManage.setStatus(1);
       riskCustomerManage.setTtl(ttl);
       riskCustomerManage.setOptionType(1);
       riskCustomerManage.setCreateUser(strategyNo+"策略拉黑");
       riskCustomerManage.setOptionName(strategyNo+"策略拉黑");
       riskCustomerManage.setRiskRemark("");
       riskCustomerManage.setCreateTime(new Date());
       riskCustomerManage.setUpdateTime(new Date());
       riskCustomerManage.setBindUser(passengerCellphone);
       riskCustomerManage.setBindOrder(orderId);
       riskCustomerManage.setSupplierName(StringUtils.defaultString(supplierName));
       Date invalidTime = new Date();
       if (ttl == -1) {
           invalidTime = DateUtil.string2Date("2099-12-31 23:59:59");
       } else {
           invalidTime = DateUtil.addDay(new Date(), ttl);
       }
       riskCustomerManage.setInvalidTime(invalidTime);
       riskCustomerManageMapper.insert(riskCustomerManage);
   }

    /**
     * 一对一拉黑
     * @param customerValue 拉黑的值
     * @param passengerCellphone 乘车人手机号
     * @param ttl 拉黑多少天
     * @param orderId 订单号
     * @param strategyNo 策略编号
     * */
    public void actionAllBlack(String customerValue,String passengerCellphone,Integer ttl,String orderId,String strategyNo){
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        riskCustomerManage.setRiskType(7);
        riskCustomerManage.setCustomerType(6);//司机车牌号
        riskCustomerManage.setCustomerValue(customerValue);
        riskCustomerManage.setStatus(1);
        riskCustomerManage.setTtl(ttl);
        riskCustomerManage.setOptionType(1);
        riskCustomerManage.setCreateUser(strategyNo+"策略拉黑");
        riskCustomerManage.setOptionName(strategyNo+"策略拉黑");
        riskCustomerManage.setRiskRemark("");
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        riskCustomerManage.setBindUser(passengerCellphone);
        riskCustomerManage.setBindOrder(orderId);
        Date invalidTime = new Date();
        if (ttl == -1) {
            invalidTime = DateUtil.string2Date("2099-12-31 23:59:59");
        } else {
            invalidTime = DateUtil.addDay(new Date(), ttl);
        }
        riskCustomerManage.setInvalidTime(invalidTime);
        riskCustomerManageMapper.insert(riskCustomerManage);
    }

}
