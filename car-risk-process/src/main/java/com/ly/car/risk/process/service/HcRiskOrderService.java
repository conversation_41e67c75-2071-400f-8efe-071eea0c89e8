package com.ly.car.risk.process.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ly.car.order.entity.DistributionInfo;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.api.rsp.MemberQueryResponse;
import com.ly.car.risk.process.repo.hitchorder.entity.DriverCarInfo;
import com.ly.car.risk.process.repo.hitchorder.entity.DriverInfo;
import com.ly.car.risk.process.repo.hitchorder.entity.PassengerOrderAddress;
import com.ly.car.risk.process.repo.hitchorder.entity.PassengerOrderInfo;
import com.ly.car.risk.process.repo.hitchorder.mapper.DriverCarInfoMapper;
import com.ly.car.risk.process.repo.hitchorder.mapper.DriverInfoMapper;
import com.ly.car.risk.process.repo.risk.mapper.HcRiskDriverMapper;
import com.ly.car.risk.process.repo.risk.mapper.HcOrderCheckMapper;
import com.ly.car.risk.process.repo.risk.mapper.HcRiskUserMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcOrderCheck;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcRiskDriver;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcRiskUser;
import com.ly.car.sharding.order.entity.OrderExpand;
import com.ly.car.sharding.order.mapper.OrderExpandMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class HcRiskOrderService {

    @Resource
    private MemberService memberService;
    @Resource
    private OrderExpandMapper orderExpandMapper;
    @Resource
    private HcOrderCheckMapper hcOrderCheckMapper;
    @Resource
    private HcRiskUserMapper hcRiskUserMapper;
    @Resource
    private DriverInfoMapper driverInfoMapper;
    @Resource
    private DriverCarInfoMapper driverCarInfoMapper;
    @Resource
    private HcRiskDriverMapper hcRiskDriverMapper;

    public void syncDataBase(SfcOrder sfcOrder, PassengerOrderInfo orderInfo, DistributionInfo distributionInfo,
                             PassengerOrderAddress orderAddress){
        HcOrderCheck orderCheck = new HcOrderCheck();
        HcOrderCheck historyOrder = hcOrderCheckMapper.selectOne(new QueryWrapper<HcOrderCheck>().eq("order_id",orderInfo.getPassengerOrderId()));
        if(historyOrder != null){
            //如果已有的话就不更新了
            return;
        }
        orderCheck.setOrderId(orderInfo.getPassengerOrderId());
        orderCheck.setTcOrderId(orderInfo.getDistributionOrderId());
        if(sfcOrder != null){
            orderCheck.setRefId(String.valueOf(sfcOrder.getRefId()));
            orderCheck.setRefName(distributionInfo.getDistributionName());
        }
        orderCheck.setProductId(orderInfo.getPoolStatus() == 4?0:1);//拼车成功才是拼车，其他都是独享
        orderCheck.setCityId(orderAddress.getStartCityId());
        orderCheck.setCityName(orderAddress.getStartCityName());
        orderCheck.setDistributorOrderId(orderInfo.getDistributionOrderId());
        orderCheck.setIsRisk(1);
        orderCheck.setRiskRule("7-6");
        orderCheck.setRiskType(0);
        orderCheck.setRemark("");
        orderCheck.setCreateTime(new Date());
        orderCheck.setUpdateTime(new Date());
        orderCheck.setStatus(0);
        this.hcOrderCheckMapper.insert(orderCheck);
        HcRiskUser hcRiskUser = new HcRiskUser();
        hcRiskUser.setOrderId(orderInfo.getPassengerOrderId());
        //请求会员部门获取用户信息
        if(sfcOrder.getMemberId() != null && sfcOrder.getMemberId() != 0L){
            MemberQueryResponse infoByMemberId = memberService.getInfoByMemberId(String.valueOf(sfcOrder.getMemberId()));
            if(infoByMemberId != null && infoByMemberId.getData() != null){
                hcRiskUser.setUserName(infoByMemberId.getData().getRealname());
                hcRiskUser.setSex(infoByMemberId.getData().getSex()==0?1:(infoByMemberId.getData().getSex()==1?2:0));
                hcRiskUser.setAge(0);
                hcRiskUser.setEmergencyContact("");
                hcRiskUser.setUserPhone(infoByMemberId.getData().getMobile());
                hcRiskUser.setUserPhonePlace("");
            }
        }
        hcRiskUser.setPassengerPhone(orderInfo.getPassengerMobile());
//        hcRiskUser.setActivityAddress();
//        hcRiskUser.setLastActivityAddress();
//        hcRiskUser.setDeviceId();
        OrderExpand orderExpand = this.orderExpandMapper.findByOrderId(orderInfo.getDistributionOrderId());
        if(orderExpand != null){
            hcRiskUser.setOrderDeviceId(orderExpand.getDeviceId());
        }

//        hcRiskUser.setAccountPhoneRecord("");
//        hcRiskUser.setMemberLevel();
        hcRiskUser.setStartAddress(orderInfo.getStartAddress());
        hcRiskUser.setEndAddress(orderInfo.getEndAddress());
        hcRiskUser.setStartLngLat(orderAddress.getStartLng()+","+orderAddress.getStartLat());
        hcRiskUser.setEndLngLat(orderAddress.getEndLng()+","+orderAddress.getEndLat());
        hcRiskUser.setOrderCreateTime(orderInfo.getCreateTime());
        hcRiskUser.setOrderServiceTime(orderInfo.getOnboardTime());
        hcRiskUser.setFinishTime(sfcOrder.getFinishTime());
        hcRiskUser.setPayTime(sfcOrder.getPayTime());
        hcRiskUser.setFormChangeRecord("");
        hcRiskUser.setCostAddRecord("");
        hcRiskUser.setDriverEvaluation(0);
        hcRiskUser.setCreateTime(new Date());
        hcRiskUser.setUpdateTime(new Date());
        hcRiskUserMapper.insert(hcRiskUser);

        //插入司机的信息
        HcRiskDriver hcRiskDriver = new HcRiskDriver();
        DriverInfo driverInfo = driverInfoMapper.selectOne(new QueryWrapper<DriverInfo>().eq("member_id", orderInfo.getDriverMemberId()));
        DriverCarInfo driverCarInfo = driverCarInfoMapper.selectOne(
                new QueryWrapper<DriverCarInfo>().eq("member_id",orderInfo.getDriverMemberId()).last("limit 1")
        );
        hcRiskDriver.setOrderId(orderInfo.getPassengerOrderId());
        if(driverInfo != null){
            hcRiskDriver.setDriverName(driverInfo.getIdCardName());
        }

        hcRiskDriver.setSex(driverInfo.getRegisterSex()==0?1:2);
//        hcRiskDriver.setAge();
//        hcRiskDriver.setDomicilePlace();
//        hcRiskDriver.setDriverAge();
        hcRiskDriver.setRegisterPhone(hcRiskDriver.getRegisterPhone());
//        hcRiskDriver.setPlaceAttribution();
//        hcRiskDriver.setEmergencyContact();
//        hcRiskDriver.setHistoryFinish();
//        hcRiskDriver.setHistoryCancel();
//        hcRiskDriver.setDriverScore();
//        hcRiskDriver.setGoodTag();
//        hcRiskDriver.setBadTag();
        if(driverCarInfo != null){
            hcRiskDriver.setCarType(driverCarInfo.getCarType());
            hcRiskDriver.setCarNo(driverCarInfo.getCarNumber());
            hcRiskDriver.setCarBrand(driverCarInfo.getCarBrand()+"-"+driverCarInfo.getCarModel());
            hcRiskDriver.setCarColor(driverCarInfo.getCarColor());
        }

//        hcRiskDriver.setOperationScope();
//        hcRiskDriver.setActivityAddress();
//        hcRiskDriver.setDistance();
//        hcRiskDriver.setServiceStartTime();
//        hcRiskDriver.setServiceEndTime();
//        hcRiskDriver.setFormChangeRecord();
//        hcRiskDriver.setRouteDeviationDistance();
        this.hcRiskDriverMapper.insert(hcRiskDriver);

    }


    public void delHc(String orderId){
        this.hcOrderCheckMapper.delete(new UpdateWrapper<HcOrderCheck>().eq("order_id",orderId));
        this.hcRiskUserMapper.delete(new UpdateWrapper<HcRiskUser>().eq("order_id",orderId));
        this.hcRiskDriverMapper.delete(new UpdateWrapper<HcRiskDriver>().eq("order_id",orderId));
    }
}
