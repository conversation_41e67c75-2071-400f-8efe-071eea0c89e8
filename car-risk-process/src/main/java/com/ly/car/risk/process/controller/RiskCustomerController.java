package com.ly.car.risk.process.controller;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.params.DriverSyncParams;
import com.ly.car.risk.process.service.RiskCustomerService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/risk/customer")
public class RiskCustomerController {

    @Resource
    private RiskCustomerService riskCustomerService;

    /**
     * 风控名单定时失效job
     * @param
     * @return
     */
    @PostMapping("/invalid")
    public String invalid(){
        return riskCustomerService.invalid();
    }

    /**
     * 名单初始化失效时间
     * */
    @RequestMapping("/initCustomer")
    public String initCustomer(){
        return this.riskCustomerService.initCustomer();
    }

    /**
     * 司机黑名单,给陈牧那边同步
     * */
    @RequestMapping("/syncBlackDriver")
    public UiResult syncBlackDriver(@RequestBody DriverSyncParams params){
        this.riskCustomerService.syncDriver(params);
        return UiResult.ok();
    }

    @RequestMapping("/syncBlackDriverByErp")
    public UiResult syncBlackDriverByErp(@RequestBody DriverSyncParams params){
        this.riskCustomerService.syncDriver(params);
        return UiResult.ok();
    }

}
