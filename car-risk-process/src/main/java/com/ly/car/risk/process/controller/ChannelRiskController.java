package com.ly.car.risk.process.controller;

import com.ly.car.risk.process.controller.params.ChannelRiskReq;
import com.ly.car.risk.process.service.ChannelRiskService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/channel/risk")
public class ChannelRiskController {


    @Resource
    private ChannelRiskService channelRiskService;


    @PostMapping("/badDebts")
    public String findBadDebts(@RequestBody ChannelRiskReq req){
        return channelRiskService.findBadDebts(req.getRefIdList());
    }

    @PostMapping("/notify")
    public String badDebtsNotify(){
        return channelRiskService.badDebtsNotify();
    }
}
