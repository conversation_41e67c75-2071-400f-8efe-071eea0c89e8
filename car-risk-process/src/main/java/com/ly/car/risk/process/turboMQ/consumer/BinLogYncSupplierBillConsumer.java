package com.ly.car.risk.process.turboMQ.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.OrderComplete;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.repo.risk.mapper.OrderCompleteMapper;
import com.ly.car.risk.process.service.redis.UpdateScoreSortedSetService;
import com.ly.car.risk.process.turboMQ.dto.BingLogData;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.BingLogUtil;
import com.ly.car.sharding.order.entity.OrderDriver;
import com.ly.car.sharding.order.entity.OrderExpand;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.entity.OrderSupplierBill;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderExpandMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class BinLogYncSupplierBillConsumer implements MessageListenerConcurrently {

    private OrderCompleteMapper orderCompleteMapper;
    private UpdateScoreSortedSetService updateScoreSortedSetService;
    private OrderExpandMapper orderExpandMapper;
    private OrderInfoMapper orderInfoMapper;
    private OrderDriverMapper orderDriverMapper;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for(MessageExt messageExt : list){
            try {
                String body = new String(messageExt.getBody(),"utf-8");
                log.info("[binlog][BinLogYncSupplierBillConsumer][][][]收到turbo消息,消息id={}, body={}", messageExt.getMsgId(), body);
                BingLogData bingLogData = JSON.parseObject(body, BingLogData.class);
                String eventType = bingLogData.getEventType();
                if(!eventType.equals("INSERT")){
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                OrderSupplierBill orderSupplierBill = BingLogUtil.buildSource(bingLogData,OrderSupplierBill.class);
                //更新完单表
                orderCompleteMapper = SpringContextUtil.getBean("orderCompleteMapper");
                OrderComplete orderComplete = orderCompleteMapper.selectOne(
                        new QueryWrapper<OrderComplete>().eq("order_id",orderSupplierBill.getOrderId())
                );
                if(orderComplete != null){
                    orderComplete.setActualKilo(orderSupplierBill.getDistance());
                    orderComplete.setActualTime(orderSupplierBill.getDuration());
                    orderCompleteMapper.updateById(orderComplete);
                }
                orderExpandMapper = SpringContextUtil.getBean("orderExpandMapper");
                OrderExpand orderExpand = null;
                if(orderExpandMapper != null){
                    orderExpand = orderExpandMapper.findByOrderId(orderSupplierBill.getOrderId());
                    log.info("[binlog][BinLogYncSupplierBillConsumer][][][]orderExpandMapper:{}", JsonUtils.json(orderExpand));
                }
                orderInfoMapper = SpringContextUtil.getBean("orderInfoMapper");
                orderDriverMapper = SpringContextUtil.getBean("orderDriverMapper");
                OrderInfo orderInfo = orderInfoMapper.findByOrderId(orderSupplierBill.getOrderId());
                OrderDriver orderDriver = orderDriverMapper.findByOrderId(orderSupplierBill.getOrderId());
                //更新缓存
                updateScoreSortedSetService = SpringContextUtil.getBean("updateScoreSortedSetService");
                if(orderExpand != null && StringUtils.isNotBlank(orderExpand.getPayAccount())){
                    updateScoreSortedSetService.updateSupplierBill(RedisKeyConstants.PAY_ACCOUNT_WINDOW+orderExpand.getPayAccount(),orderSupplierBill);
                }
                if(orderExpand != null && StringUtils.isNotBlank(orderExpand.getDeviceId())){
                    updateScoreSortedSetService.updateSupplierBill(RedisKeyConstants.DEVICE_ID_WINDOW+orderExpand.getDeviceId(),orderSupplierBill);
                }
                if(StringUtils.isNotBlank(orderInfo.getMemberId()) && !orderInfo.getMemberId().equals("0")){
                    updateScoreSortedSetService.updateSupplierBill(RedisKeyConstants.USER_MEMBER_WINDOW+orderInfo.getMemberId(),orderSupplierBill);
                }
                if(StringUtils.isNotBlank(orderInfo.getUniId())){
                    updateScoreSortedSetService.updateSupplierBill(RedisKeyConstants.USER_UNION_ID_WINDOW+orderInfo.getUniId(),orderSupplierBill);
                }
                if(StringUtils.isNotBlank(orderDriver.getPlateNumber())){
                    updateScoreSortedSetService.updateSupplierBill(RedisKeyConstants.DRIVER_CARD_WINDOW+orderDriver.getPlateNumber(),orderSupplierBill);
                }
            } catch (Exception e){
                log.error("[][][][]专车账单报错:{}",e);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
