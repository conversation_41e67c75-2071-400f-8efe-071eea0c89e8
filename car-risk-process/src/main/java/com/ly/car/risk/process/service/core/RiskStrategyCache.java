package com.ly.car.risk.process.service.core;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskStrategyMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.CategoryRelation;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskField;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskStrategy;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RKeys;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RiskStrategyCache {

    private static final String KEY = "risk_strategy_";

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private RiskStrategyMapper riskStrategyMapper;
    @Resource
    private CategoryRelationCache categoryRelationCache;

    @Scheduled(fixedRate = 1000L * 60L,initialDelay = 0)
    public void init(){
        List<RiskStrategy> strategyList = this.riskStrategyMapper.selectList(new QueryWrapper<RiskStrategy>()
                .eq("is_deleted", 0)
                .in("type",0,1)
        );
        for(RiskStrategy strategy : strategyList){
            redissonClient.getBucket(KEY+strategy.getStrategyNo()).set(JSONObject.toJSONString(strategy),5,TimeUnit.MINUTES);
        }
    }

    public RiskStrategy loadStrategy(String key){
        RBucket<String> riskStrategyRBucket = redissonClient.getBucket(KEY + key);
        if(riskStrategyRBucket == null){
            return null;
        }
        return JSONObject.parseObject(riskStrategyRBucket.get(), RiskStrategy.class);
    }

    public List<RiskStrategy> loadStrategyList(String guid,String productLine){
        List<CategoryRelation> strategyCategoryRelations = this.categoryRelationCache.loadRelationByRuleSceneNo(guid);
        List<RiskStrategy> riskStrategyList = new ArrayList<>();
        for(CategoryRelation relation : strategyCategoryRelations){
            RiskStrategy strategy = this.loadStrategy(relation.getStrategyNo());
            log.info("[][][][]loadStrategyList加载策略{}", JsonUtils.json(strategy));
            if(strategy == null){
                continue;
            }
            if(StringUtils.isNotBlank(strategy.getProductLine()) && strategy.getProductLine().equals(productLine)){
                riskStrategyList.add(strategy);
            }

        }
        return riskStrategyList;
    }


}
