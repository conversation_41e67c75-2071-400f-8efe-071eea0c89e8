package com.ly.car.risk.process.controller.task;

import com.ly.car.risk.process.service.ability.RiskCustomerInitService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/task/customer")
public class RiskCustomerTask {

    @Resource
    private RiskCustomerInitService riskCustomerInitService;

    @RequestMapping("init")
    public void initCustomer(){
        riskCustomerInitService.initCustomer();
    }

    @RequestMapping("increment")
    public void incrementUpdate(){
        riskCustomerInitService.incrementUpdate();
    }
}
