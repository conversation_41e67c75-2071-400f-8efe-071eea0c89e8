package com.ly.car.risk.process.service.core;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.CategoryRelationMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.CategoryRelation;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CategoryRelationCache {
    private static final String KEY = "category_relation_";

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private CategoryRelationMapper categoryRelationMapper;

    @Scheduled(fixedRate = 1000L * 60L,initialDelay = 0)
    public void init(){
        List<CategoryRelation> categoryRelationList = this.categoryRelationMapper.selectList(new QueryWrapper<CategoryRelation>()
                .gt("id", 0)
        );

        Map<String,List<CategoryRelation>> relationMap = categoryRelationList.stream()
                .collect(Collectors.groupingBy(CategoryRelation::getStrategyNo));
        //以策略编号为key
        for(Map.Entry<String,List<CategoryRelation>> entry : relationMap.entrySet()){
            redissonClient.getBucket(KEY+entry.getKey()).set(JsonUtils.json(entry.getValue()),1,TimeUnit.DAYS);
        }

        //以规则或场景编号为key
        Map<String,List<CategoryRelation>> ruleSceneNoMap = categoryRelationList.stream()
                .collect(Collectors.groupingBy(CategoryRelation::getRuleSceneNo));
        for(Map.Entry<String,List<CategoryRelation>> entry : ruleSceneNoMap.entrySet()){
            redissonClient.getBucket(KEY+entry.getKey()).set(JsonUtils.json(entry.getValue()),1,TimeUnit.DAYS);
        }
    }

    public List<CategoryRelation> loadRelation(String key,Integer type){
        RBucket<String> bucket = redissonClient.getBucket(KEY + key);
        if(bucket == null || !bucket.isExists()){
            return null;
        }
        List<CategoryRelation> categoryRelationList = JSONArray.parseArray(bucket.get(),CategoryRelation.class).stream()
                .filter(data->data.getType().equals(type)).collect(Collectors.toList());
        return categoryRelationList;
    }

    public List<CategoryRelation> loadRelationByRuleSceneNo(String key){
        RBucket<String> bucket = redissonClient.getBucket(KEY + key);
        if(bucket == null || !bucket.isExists()){
            return null;
        }
        List<CategoryRelation> categoryRelationList = JSONArray.parseArray(bucket.get(),CategoryRelation.class);
        return categoryRelationList;
    }
}
