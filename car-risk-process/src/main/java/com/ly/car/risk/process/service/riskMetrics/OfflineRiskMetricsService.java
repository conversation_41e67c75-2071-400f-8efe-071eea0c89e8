package com.ly.car.risk.process.service.riskMetrics;

import com.ly.car.risk.process.repo.riskmetrics.entity.CarOfflineRiskOrderDetail;
import java.util.List;

public interface OfflineRiskMetricsService {
    
    List<CarOfflineRiskOrderDetail> driverRealMoneyExceedRate(List<String> productLineList, Double rate, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverRealMoneyExceedAmount(List<String> productLineList, Double difference, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverSurchargeExceedRate(List<String> productLineList, Double rate, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverRealDistanceExceedKm(List<String> productLineList, Double distance, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverRealTimeExceedMinute(List<String> productLineList, Double minute, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverSupplementaryExceedAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverSurchargeRealExceedAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverDistanceLessKm(List<String> productLineList, Double distance, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverTimeLessMinute(List<String> productLineList, Double minute, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverTimeExceedMinute(List<String> productLineList, Double minute, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverStateExp(List<String> productLineList, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverScorePayLessAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverScorePaySupplementaryAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverRangeAmount(List<String> productLineList, Double leftAmount, Double rightAmount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverBookLessAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverMoreAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverODSame(List<String> productLineList, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverZeroEstimateDistance(List<String> productLineList, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverRealAmountMoreRate(List<String> productLineList, Double rate, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverRealAmountMoreAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverTimeMoreRate(List<String> productLineList, Double rate, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverSecondPayMoreAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverLessAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverShouldPayMoreAmount(List<String> productLineList, Double amount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverRealRangeAmount(List<String> productLineList, Double leftAmount, Double rightAmount, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);
    
    List<CarOfflineRiskOrderDetail> driverTimeHasLessMinute(List<String> productLineList, Double minute, String startTime, String endTime, List<String> channels, List<String> supplierCodeList);

}