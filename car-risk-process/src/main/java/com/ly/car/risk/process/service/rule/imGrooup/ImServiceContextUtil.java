package com.ly.car.risk.process.service.rule.imGrooup;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ImServiceContextUtil {

    public static Map<String, List<String>> serviceNameMap;

    static {
        Map<String,List<String>> map = new HashMap<>();
        List<String> IM_TEXT = new ArrayList<>();
        IM_TEXT.add("sensitiveTextService");
        map.put("8-1",IM_TEXT);
        serviceNameMap = map;

    }
}
