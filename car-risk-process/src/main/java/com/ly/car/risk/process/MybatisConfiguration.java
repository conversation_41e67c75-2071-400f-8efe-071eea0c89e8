package com.ly.car.risk.process;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.ly.car.datasource.EnableDataSource;
import com.ly.car.risk.process.utils.RiskMybatisConfigUtils;
import com.ly.car.sharding.order.DcdbOrderMybatisConfiguration;
import com.ly.car.sharding.order.ShardingOrderMybatisConfiguration;
import com.ly.car.utils.MybatisConfigUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.*;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@EnableDataSource
@Configuration
@EnableTransactionManagement(mode = AdviceMode.ASPECTJ)
@Import({ShardingOrderMybatisConfiguration.class, DcdbOrderMybatisConfiguration.class})
@ComponentScan("com.ly.car.sharding.order.mapper")
@MapperScan(basePackages = "com.ly.car.risk.process.repo.risk.mapper", sqlSessionFactoryRef = "riskSqlSessionFactory")
@MapperScan(basePackages = "com.ly.car.risk.process.repo.order.mapper", sqlSessionFactoryRef = "orderSqlSessionFactory")
@MapperScan(basePackages = "com.ly.car.risk.process.repo.dcdborder.mapper", sqlSessionFactoryRef = "dcdbOrderSqlSessionFactory")
@MapperScan(basePackages = "com.ly.car.risk.process.repo.resource.mapper", sqlSessionFactoryRef = "resourceSqlSessionFactory")
@MapperScan(basePackages = "com.ly.car.risk.process.repo.traffic.mapper", sqlSessionFactoryRef = "trafficSqlSessionFactory")
@MapperScan(basePackages = "com.ly.car.risk.process.repo.hitchorder.mapper", sqlSessionFactoryRef = "hitchOrderSqlSessionFactory")
@MapperScan(basePackages = "com.ly.car.risk.process.repo.mtticket.mapper", sqlSessionFactoryRef = "mtTicketSqlSessionFactory")
@MapperScan(basePackages = "com.ly.car.risk.process.repo.dcdbresource.mapper", sqlSessionFactoryRef = "dcdbResourceSqlSessionFactory")
@MapperScan(basePackages = "com.ly.car.risk.process.repo.riskmetrics.mapper", sqlSessionFactoryRef = "riskMetricsSqlSessionFactory")
@MapperScan(basePackages = "com.ly.car.risk.process.repo.dcdbcarrisk.mapper", sqlSessionFactoryRef = "dcdbCarRiskSqlSessionFactory")
public class MybatisConfiguration {

    @Bean
    public MybatisSqlSessionFactoryBean riskMetricsSqlSessionFactory(@Qualifier("riskMetricsDataSource") DataSource dataSource){
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setGlobalConfig(MybatisConfigUtils.getGlobalConfig());
        sqlSessionFactory.setConfiguration(MybatisConfigUtils.getMybatisConfiguration());
        return sqlSessionFactory;
    }

    @Bean
    public MybatisSqlSessionFactoryBean riskSqlSessionFactory(@Qualifier("riskDataSource") DataSource dataSource) {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setGlobalConfig(RiskMybatisConfigUtils.getGlobalConfig());
        sqlSessionFactory.setConfiguration(RiskMybatisConfigUtils.getMybatisConfiguration());
        return sqlSessionFactory;
    }

    @Bean
    public MybatisSqlSessionFactoryBean orderSqlSessionFactory(@Qualifier("orderDataSource") DataSource dataSource) {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setGlobalConfig(MybatisConfigUtils.getGlobalConfig());
        sqlSessionFactory.setConfiguration(MybatisConfigUtils.getMybatisConfiguration());
        return sqlSessionFactory;
    }

    @Bean
    public MapperScannerConfigurer oldOrderMapperScannerConfigurer() {
        MapperScannerConfigurer mapperScannerConfigurer = new MapperScannerConfigurer();
        mapperScannerConfigurer.setBasePackage("com.ly.car.sharding.order.mappers.old");
        mapperScannerConfigurer.setSqlSessionFactoryBeanName("orderSqlSessionFactory");
        return mapperScannerConfigurer;
    }

    /*@Bean
    public MybatisSqlSessionFactoryBean dcdbOrderSqlSessionFactory(@Qualifier("dcdbOrderDataSource") DataSource dataSource) {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setGlobalConfig(MybatisConfigUtils.getGlobalConfig());
        sqlSessionFactory.setConfiguration(MybatisConfigUtils.getMybatisConfiguration());
        return sqlSessionFactory;
    }

    @Bean
    public MapperScannerConfigurer dcdbOrderMapperScannerConfigurer() {
        MapperScannerConfigurer mapperScannerConfigurer = new MapperScannerConfigurer();
        mapperScannerConfigurer.setBasePackage("com.ly.car.sharding.order.mappers.dcdb");
        mapperScannerConfigurer.setSqlSessionFactoryBeanName("dcdbOrderSqlSessionFactory");
        return mapperScannerConfigurer;
    }*/

    @Bean
    public MybatisSqlSessionFactoryBean resourceSqlSessionFactory(@Qualifier("resourceDataSource") DataSource dataSource) {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setGlobalConfig(MybatisConfigUtils.getGlobalConfig());
        sqlSessionFactory.setConfiguration(MybatisConfigUtils.getMybatisConfiguration());
        return sqlSessionFactory;
    }

    @Bean
    public MybatisSqlSessionFactoryBean trafficSqlSessionFactory(@Qualifier("trafficDataSource") DataSource dataSource) {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setGlobalConfig(MybatisConfigUtils.getGlobalConfig());
        sqlSessionFactory.setConfiguration(MybatisConfigUtils.getMybatisConfiguration());
        return sqlSessionFactory;
    }

    @Bean
    public MybatisSqlSessionFactoryBean hitchOrderSqlSessionFactory(@Qualifier("hitchOrderDataSource") DataSource dataSource) {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setGlobalConfig(MybatisConfigUtils.getGlobalConfig());
        sqlSessionFactory.setConfiguration(MybatisConfigUtils.getMybatisConfiguration());
        return sqlSessionFactory;
    }

    @Bean
    public MybatisSqlSessionFactoryBean mtTicketSqlSessionFactory(@Qualifier("mtTicketDataSource") DataSource dataSource) {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setGlobalConfig(MybatisConfigUtils.getGlobalConfig());
        sqlSessionFactory.setConfiguration(MybatisConfigUtils.getMybatisConfiguration());
        return sqlSessionFactory;
    }

    @Bean
    public MybatisSqlSessionFactoryBean dcdbResourceSqlSessionFactory(@Qualifier("dcdbResourceDataSource") DataSource dataSource){
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setGlobalConfig(MybatisConfigUtils.getGlobalConfig());
        sqlSessionFactory.setConfiguration(MybatisConfigUtils.getMybatisConfiguration());
        return sqlSessionFactory;
    }

    @Bean
    public MybatisSqlSessionFactoryBean dcdbCarRiskSqlSessionFactory(@Qualifier("dcdbCarRiskDataSource") DataSource dataSource){
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setGlobalConfig(MybatisConfigUtils.getGlobalConfig());
        sqlSessionFactory.setConfiguration(MybatisConfigUtils.getMybatisConfiguration());
        return sqlSessionFactory;
    }

//    @Bean
//    public MapperScannerConfigurer hitchOrderMapperScannerConfigurer() {
//        MapperScannerConfigurer mapperScannerConfigurer = new MapperScannerConfigurer();
//        mapperScannerConfigurer.setBasePackage("com.ly.car.sharding.order.mappers.old");
//        mapperScannerConfigurer.setSqlSessionFactoryBeanName("orderSqlSessionFactory");
//        return mapperScannerConfigurer;
//    }

    // 欺骗IDEA使其不报错，假装存在名称为xxxDataSource的Bean
    @Configuration
    @Profile("cheatIdea")
    public static class CheatIdeaConfiguration {
        @Bean
        public DataSource orderDataSource() {
            return null;
        }

        @Bean
        public DataSource riskDataSource() {
            return null;
        }

        @Bean
        public DataSource dcdbOrderDataSource() {
            return null;
        }

        @Bean
        public DataSource resourceDataSource() {
            return null;
        }

        @Bean
        public DataSource trafficDataSource() {
            return null;
        }
        @Bean
        public DataSource hitchOrderDataSource() {
            return null;
        }
        @Bean
        public DataSource mtTicketDataSource(){return null;}
        @Bean
        public DataSource dcdbResourceDataSource(){return null;}
        @Bean
        public DataSource riskMetricsDataSource(){return null;}
        @Bean
        public DataSource dcdbCarRiskDataSource(){return null;}
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}
