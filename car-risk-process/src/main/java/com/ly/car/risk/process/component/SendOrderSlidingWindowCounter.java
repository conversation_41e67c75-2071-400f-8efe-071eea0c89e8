package com.ly.car.risk.process.component;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@Component
@Slf4j
public class SendOrderSlidingWindowCounter {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    public List<SendOrderContext> getCommonWindow(String key,long startMs){
        //当前时间
        long currentTime = System.currentTimeMillis();
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        //优化下节省内存
        long initMs = TimeUtil.initMs();
        scoredSortedSet.removeRangeByScore(initMs,true,startMs,false);
        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, currentTime, true);
        List<SendOrderContext> objList = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            SendOrderContext context = JSONObject.parseObject(obj.getValue().toString(),SendOrderContext.class);
            objList.add(context);
        }
        return objList;
    }
}
