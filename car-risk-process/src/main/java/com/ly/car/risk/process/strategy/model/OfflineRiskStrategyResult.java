package com.ly.car.risk.process.strategy.model;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineRiskStrategyResult {
    
    /**
     * 策略id
     */
    private Long strategyId;
    
    /**
     * 策略编号
     */
    private String strategyNo;
    
    /**
     * 策略结果
     */
    private Boolean strategyMatched;
    
    /**
     * 命中的指标
     */
    private List<String> matchFields;
    
}