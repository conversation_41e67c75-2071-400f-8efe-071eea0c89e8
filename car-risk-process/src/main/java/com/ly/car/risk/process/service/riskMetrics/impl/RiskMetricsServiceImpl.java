package com.ly.car.risk.process.service.riskMetrics.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.common.constants.CommonConstants;
import com.ly.car.risk.common.enums.ConfigCenterKeyEnum;
import com.ly.car.risk.common.enums.MetricStrategyChannelEnum;
import com.ly.car.risk.process.api.EsQueryClient;
import com.ly.car.risk.process.api.dto.CarOwnerTrackInfo;
import com.ly.car.risk.process.client.lbs.LbsClient;
import com.ly.car.risk.process.client.lbs.NavigateCondition;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.SensitiveRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.SensitiveRecord;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtDriverAccountRecordMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtDriverBillMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtDriverInfoMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarMtRideOrderInfoMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderCouponMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderDetailMapper;
import com.ly.car.risk.process.service.ability.MetricsConfigCenterService;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.dto.order.CarPassenger;
import com.ly.car.risk.process.service.dto.order.OrderTripInfo;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService;
import com.ly.car.risk.process.strategy.RiskStrategyHandler;
import com.ly.car.risk.process.utils.CoordUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import com.ly.car.risk.process.utils.GeoDistanceUtil;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import org.apache.catalina.util.ServerInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description of RiskMetricsServiceImpl
 *
 * <AUTHOR>
 * @date 2024/6/18
 * @desc
 */
@Service(value = "riskMetricsService")
@Slf4j
public class RiskMetricsServiceImpl implements RiskMetricsService {

    @Resource
    private RiskStrategyHandler riskStrategyHandler;

    @Resource
    private CarRiskOrderDetailMapper orderDetailMapper;

    @Resource
    private CarRiskOrderCouponMapper couponMapper;
    
    @Resource
    private CarMtDriverInfoMapper carMtDriverInfoMapper;
    
    @Resource
    private CarMtRideOrderInfoMapper carMtRideOrderInfoMapper;
    
    @Resource
    private CarMtDriverAccountRecordMapper carMtDriverAccountRecordMapper;
    
    @Resource
    private CarMtDriverBillMapper carMtDriverBillMapper;

    @Resource
    private SensitiveRecordMapper sensitiveRecordMapper;

    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;

    @Resource
    private EsQueryClient esQueryClient;

    @Resource
    private LbsClient lbsClient;
    @Autowired
    private CarOrderService carOrderService;
    @Autowired
    private MetricsConfigCenterService metricsConfigCenterService;

    @Override
    public void cleanRiskMetricOldData() {
        orderDetailMapper.cleanRiskMetricOldData();
    }

    @Override
    public void cleanRiskMetricOldCouponData() {
        couponMapper.cleanRiskMetricOldCouponData();
    }

    @Override
    public List<Map<String, Object>> executeSql(String sql) {
        return orderDetailMapper.executeSql(sql);
    }

    @Override
    public List<CarRiskOrderDetail> user1HourFinishOrder(String memberId,String productLine) {
        String methodName = "user1HourFinishOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser1HourFinishOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user24HourFinishOrder(String memberId,String productLine) {
        String methodName = "user24HourFinishOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser24HourFinishOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> car1HourFinishOrder(String carNum,String productLine) {
        String methodName = "car1HourFinishOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryCar1HourFinishOrder(carNum,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> car24HourFinishOrder(String carNum,String productLine) {
        String methodName = "car24HourFinishOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryCar24HourFinishOrder(carNum,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user24HourTotalOrder(String memberId,String productLine) {
        String methodName = "user24HourTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser24HourAllOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> car24HourTotalOrder(String carNum, String productLine) {
        String methodName = "car24HourTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryCar24HourTotalOrder(carNum,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> car1HourTotalOrder(String carNum, String productLine) {
        String methodName = "car1HourTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryCar1HourTotalOrder(carNum,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user1HourTotalOrder(String memberId, String productLine) {
        String methodName = "user1HourTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser1HourAllOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user30MinTotalOrder(String memberId, String productLine) {
        String methodName = "user30MinTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser30MinTotalOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> phone24HourFinishOrder(String phone, String productLine) {
        String methodName = "phone24HourFinishOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryPhone24HourFinishOrder(phone,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> phone24HourTotalOrder(String phone, String productLine) {
        String methodName = "phone24HourTotalOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryPhone24HourTotalOrder(phone,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }


    @Override
    public List<CarRiskOrderDetail> user30MinCancelOrder(String memberId, String productLine) {
        String methodName = "user30MinCancelOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser30MinCancelOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user1HourCancelOrder(String memberId, String productLine) {
        String methodName = "user1HourCancelOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser1HourCancelOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> user24HourCancelOrder(String memberId, String productLine) {
        String methodName = "user24HourCancelOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryUser24HourCancelOrder(memberId,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }


    @Override
    public List<CarRiskOrderDetail> phone24HourCancelOrder(String phone, String productLine) {
        String methodName = "phone24HourCancelOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.queryPhone24HourCancelOrder(phone,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }

    @Override
    public List<CarRiskOrderDetail> car24HourFinishRightsOrder(String carNum, String productLine) {
        String methodName = "car24HourFinishRightsOrder_"+productLine;
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = orderDetailMapper.car24HourFinishRightsOrder(carNum,productLine);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 萌艇-当前设备近24小时注册司机账户数量
     */
    @Override
    public List<CarRiskOrderDetail> driver24HourRegisterCountOnSameDevice(String deviceId) {
        String methodName = "mtDriver24HourRegisterCountOnSameDevice";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtDriverInfoMapper.driver24HourRegisterCountOnSameDevice(deviceId);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 萌艇-当前设备近24小时登录司机账户数量
     */
    @Override
    public List<CarRiskOrderDetail> driver24HourLoginCountOnSameDevice(String deviceId) {
        String methodName = "mtDriver24HourLoginCountOnSameDevice";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtDriverAccountRecordMapper.driver24HourLoginCountOnSameDevice(deviceId);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 萌艇-当前设备近24小时关联提现司机账户数量
     */
//    @Override
//    public List<CarRiskOrderDetail> driver24HourWithdrawCountSameDevice(String deviceId) {
//        String methodName = "driver24HourWithdrawCountSameDevice";
//        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
//        if (null != orderList) {
//            return orderList;
//        }
//        orderList = driverWithdrawalRecordMapper.driver24HourWithdrawCountSameDevice(deviceId);
//        riskStrategyHandler.setLocal(methodName,orderList);
//        return orderList;
//    }
    
    /**
     * 萌艇-当前设备近24小时关联提现金额
     */
//    @Override
//    public List<CarRiskOrderDetail> driver24HourWithdrawAmountSameDevice(String deviceId) {
//        String methodName = "driver24HourWithdrawAmountSameDevice";
//        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
//        if (null != orderList) {
//            return orderList;
//        }
//        orderList = driverWithdrawalRecordMapper.driver24HourWithdrawAmountSameDevice(deviceId);
//        riskStrategyHandler.setLocal(methodName,orderList);
//        return orderList;
//    }
    
    /**
     * 萌艇-当前设备敏感时间注册司机账户数量（手机号）0-5
     */
    @Override
    public List<CarRiskOrderDetail> driver05RegisterSameDevice(String deviceId) {
        String methodName = "mtDriver05RegisterSameDevice";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtDriverInfoMapper.driver05RegisterSameDevice(deviceId);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 萌艇-当前司机近24小时获取补贴订单数
     */
    @Override
    public List<CarRiskOrderDetail> driver24HourBillOrderCount(String carNum) {
        String methodName = "mtDriver24HourBillOrderCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtDriverBillMapper.driver24HourBillOrderCount(carNum);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 萌艇-当前近24小时司机完单小于6分钟的订单数
     */
    @Override
    public List<CarRiskOrderDetail> driver24hourFinishOrderLess6min(String carNum) {
        String methodName = "mtDriver24hourFinishOrderLess6min";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtRideOrderInfoMapper.driver24hourFinishOrderLess6min(carNum);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 当前近24小时司机获取补贴、奖励金额
     */
    @Override
    public List<CarRiskOrderDetail> driver24hourBillOrderAmount(String carNum) {
        String methodName = "driver24hourBillOrderAmount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        orderList = carMtDriverBillMapper.driver24hourBillOrderAmount(carNum);
        riskStrategyHandler.setLocal(methodName,orderList);
        return orderList;
    }
    
    /**
     * 当前用户近24小时取消单个供应商名称的订单数
     */
    @Override
    public List<CarRiskOrderDetail> user24hourCancelSupplierOrderCount(String memberId, String productLine) {
        String methodName = "user24hourCancelSupplierOrderCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        Map<String, List<CarRiskOrderDetail>> map = orderDetailMapper.queryUser24hourCancelSupplierOrderCount(memberId, productLine)
                .stream()
                .collect(Collectors.groupingBy(CarRiskOrderDetail::getSupplierCode));
        
        orderList = map.values().stream().max(Comparator.comparing(List::size)).orElse(Collections.emptyList());
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当天用户订单完单时间与上笔订单完单时间间隔小于6分钟的订单数
     */
    @Override
    public List<CarRiskOrderDetail> user24hourFinishNearOrderLess6MinCount(String memberId, String productLine) {
        String methodName = "user24hourFinishNearOrderLess6MinCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        orderList = new ArrayList<>();
        
        List<CarRiskOrderDetail> list = orderDetailMapper.queryUser24hourFinishNearOrderLess6MinCount(memberId, productLine);
        
        for (int i = 1; i < list.size(); i++) {
            CarRiskOrderDetail before = list.get(i - 1);
            CarRiskOrderDetail cur = list.get(i);
            if (DateUtil.between(before.getGmtTripFinished(), cur.getGmtTripFinished(), DateUnit.MINUTE) < 6) {
                orderList.add(cur);
            }
        }
        
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当前用户近24小时同设备号完单数
     */
    @Override
    public List<CarRiskOrderDetail> user24hourFinishOrderSameDeviceCount(String memberId, String productLine) {
        String methodName = "user24hourFinishOrderSameDeviceCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        Map<String, List<CarRiskOrderDetail>> map = orderDetailMapper.queryUser24hourFinishOrderSameDeviceCount(memberId, productLine)
                .stream()
                .collect(Collectors.groupingBy(CarRiskOrderDetail::getDeviceId));
        
        orderList = map.values().stream().max(Comparator.comparing(List::size)).orElse(Collections.emptyList());
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当前用户近24小时订单的起点与上笔订单的终点距离小于500米的订单数
     */
    @Override
    public List<CarRiskOrderDetail> user24hourFinishNearOrderLess500mCount(String memberId, String productLine) {
        String methodName = "user24hourFinishNearOrderLess500mCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        orderList = new ArrayList<>();
        
        List<CarRiskOrderDetail> list = orderDetailMapper.queryUser24hourFinishNearOrderLess500mCount(memberId, productLine);
        
        for (int i = 1; i < list.size(); i++) {
            CarRiskOrderDetail before = list.get(i - 1);
            CarRiskOrderDetail cur = list.get(i);
            if (CoordUtil.getDistance(before.getArrLon(), before.getArrLat(), cur.getDepLon(), cur.getDepLat()) < 500) {
                orderList.add(cur);
            }
        }
        
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当前用户近24小时完单小于2公里的订单数
     */
    @Override
    public List<CarRiskOrderDetail> user24hourFinishOrderLess2kmCount(String memberId, String productLine) {
        String methodName = "user24hourFinishOrderLess2kmCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        orderList = orderDetailMapper.queryUser24hourFinishOrderLess2kmCount(memberId, productLine);
        
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当前司机近24小时完单时间小于6分钟的订单
     */
    @Override
    public List<CarRiskOrderDetail> driver24hourFinishOrderLess6minCount(String carNum, String productLine) {
        String methodName = "driver24hourFinishOrderLess6minCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        orderList = orderDetailMapper.queryDriver24hourFinishOrderLess6minCount(carNum, productLine);

        
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }
    
    /**
     * 当前司机近24小时完单时间小于6分钟的订单
     */
    @Override
    public List<CarRiskOrderDetail> driver24hourFinishOrderCount(String carNum, String productLine) {
        String methodName = "driver24hourFinishOrderCount";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        orderList = orderDetailMapper.queryDriver24hourFinishOrderCount(carNum, productLine);
        

        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }

    /**
     * 订单24小时
     */
    @Override
    public List<SensitiveRecord> order24hourSensitiveMatchRecord(String orderId) {
        List<SensitiveRecord> recordList  = sensitiveRecordMapper.queryOrder24hourSensitiveMatchRecord(orderId);
        return recordList;
    }

    /**
     * 当前订单手机号指定时间内待补款订单
     *
     * @param passengerPhone 订单手机号
     * @param startTime    行程结束时间范围-开始
     * @param endTime      行程结束时间范围-结束
     * @return 订单
     */
    @Override
    public List<CarRiskOrderDetail> phonePendingOrderNum(String passengerPhone, List<String> productLineList, Date startTime, Date endTime) {
        String methodName = "phonePendingOrderNum";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }

        orderList = orderDetailMapper.queryPhoneSupplementaryAmountOrderCount(passengerPhone,productLineList, startTime, endTime);

        riskStrategyHandler.setLocal(methodName, orderList);

        return orderList;
    }

    @Override
    public BigDecimal phonePendingOrderAmount(String passengerPhone, List<String> productLineList, Date startTime, Date endTime) {
        String methodName = "phonePendingOrderAmount";

        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return new BigDecimal(object.toString());
        }

        BigDecimal  amount = orderDetailMapper.queryPhoneOrderSupplementaryAmount(passengerPhone,productLineList, startTime, endTime);

        riskStrategyHandler.setObjectLocal(methodName, amount);

        return amount;
    }

    @Override
    public long phonePendingDriverOrderCount(String passengerPhone, List<String> productLineList, Date startTime, Date endTime) {
        String methodName = "phonePendingDriverOrderCount";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Long.parseLong(object.toString());
        }

        long count = orderDetailMapper.queryPhonePendingDriverOrderCount(passengerPhone,productLineList, startTime, endTime);

        riskStrategyHandler.setObjectLocal(methodName, count);

        return count;
    }

    /**
     * 司机指定时间内命中规则次数
     *
     * @param ruleNo    规则编号
     * @param startTime 行程结束时间范围-开始
     * @param endTime   行程结束时间范围-结束
     * @return 命中规则次数
     */
    @Override
    public long driverFitRuleNum(String carNum,String ruleNo, Date startTime, Date endTime) {
        String methodName = "driverFitRuleNum";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Long.parseLong(object.toString());
        }

        long count = riskOrderManageMapper.queryDriverFitRuleCount(carNum, ruleNo, startTime, endTime);

        riskStrategyHandler.setObjectLocal(methodName, count);
        return count;
    }

    /**
     * 司机单月添加附加费订单数
     */
    @Override
    public List<CarRiskOrderDetail> driverSurchargeMonthNum(String carNum, Integer orderNum, List<String> productLineList) {
        String methodName = "driverSurchargeMonthNum";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        Date lastMonth = DateUtil.lastMonth();
        Date startTime = DateUtil.beginOfMonth(lastMonth);
        Date endTime = DateUtil.endOfMonth(lastMonth);
        orderList = orderDetailMapper.driverSurchargeMonthNum(carNum, productLineList, startTime, endTime);
        orderList = orderList.stream().skip(orderNum).collect(Collectors.toList());
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }

    /**
     *  司机每月（上个自然月）有责风险单数量
     *
     * @param startTime 行程结束时间范围-开始
     * @param endTime   行程结束时间范围-结束
     * @return 命中规则次数
     */
    @Override
    public long driverResponsibleOrderCount(String carNum, Date startTime, Date endTime) {
        String methodName = "driverResponsibleOrderCount";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Long.parseLong(object.toString());
        }

        long count = riskOrderManageMapper.queryDriverResponsibleOrderCount(carNum, startTime, endTime);

        riskStrategyHandler.setObjectLocal(methodName, count);
        return count;
    }
    
    /**
     * 当前订单附加费金额＞预估金额X%
     */
    @Override
    public List<CarRiskOrderDetail> driverSurchargeGtBookRate(CarOrderDetail orderDetail) {
        String methodName = "driverSurchargeGtBookRate";
        List<CarRiskOrderDetail> orderList = riskStrategyHandler.getLocal(methodName);
        if (null != orderList) {
            return orderList;
        }
        
        if (null == orderDetail || null == orderDetail.getBaseInfo()) {
            return Collections.emptyList();
        }
        
        CarRiskOrderDetail order = orderDetailMapper.getByOrderSerialNo(orderDetail.getOrderId());
        if (null == order) {
            return Collections.emptyList();
        }
        
        order.setSurcharge(orderDetail.getBaseInfo().getSurcharge());
                
                orderList = Collections.singletonList(order);
        riskStrategyHandler.setLocal(methodName, orderList);
        return orderList;
    }


    @Override
    public BigDecimal orderCompleteActualDuration(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            log.warn("订单时长过段指标，订单编号不能为空");
            return BigDecimal.ZERO;
        }
        String methodName = "orderCompleteActualDuration";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            log.info("[RiskMetricsServiceImpl][orderCompleteActualDuration][{}][{}] 订单完成实际时长内存:{}",orderSerialNo,methodName,object);
            return new BigDecimal(object.toString());
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail) {
            log.warn("[RiskMetricsServiceImpl][orderCompleteActualDuration][{}][{}] 根据订单编号查询订单不存在",orderSerialNo,methodName);
            return BigDecimal.ZERO;
        }

        if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            log.warn("[RiskMetricsServiceImpl][orderCompleteActualDuration][{}][{}] 订单状态不是行程结束或订单关闭:{}",orderSerialNo,methodName);
            return BigDecimal.ZERO;
        }
        int realRunTime = riskOrderDetail.getRealTime();
        if (realRunTime <= 0) {
            log.warn("[RiskMetricsServiceImpl][orderCompleteActualDuration][{}][{}] 订单实际时长为0",orderSerialNo,methodName);
            return BigDecimal.ZERO;
        }
        BigDecimal minuteDifference = BigDecimal.valueOf(realRunTime).divide(new BigDecimal("60"), 10, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
        riskStrategyHandler.setObjectLocal(methodName, minuteDifference);
        log.info("[RiskMetricsServiceImpl][orderCompleteActualDuration][{}][{}] 订单完成实际时长分钟:{}",orderSerialNo,methodName,minuteDifference);
        return minuteDifference;
    }

    @Override
    public int orderCompleteActualDistance(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            log.warn("订单编号不能为空");
            return 0;
        }
        String methodName = "orderCompleteActualDistance";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            log.info("[RiskMetricsServiceImpl][orderCompleteActualDistance][{}][{}] 订单完成实际距离:{}",orderSerialNo,methodName,object);
            return 0;
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail) {
            log.warn("[RiskMetricsServiceImpl][orderCompleteActualDistance][{}][{}] 根据订单编号查询订单不存在",orderSerialNo,methodName);
            return 0;
        }

        if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            log.warn("[RiskMetricsServiceImpl][orderCompleteActualDistance][{}][{}] 订单状态不是行程结束或订单关闭",orderSerialNo,methodName);
            return 0;
        }
        int distance = riskOrderDetail.getRealDistance();
        riskStrategyHandler.setObjectLocal(methodName, distance);
        return distance;
    }

    @Override
    public boolean driverOrderCompleteAmountGreater500EstimatedAmountLess100(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            log.warn("订单编号不能为空");
            return false;
        }
        String methodName = "driverOrderCompleteAmountGreater500EstimatedAmountLess100";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            log.info("[RiskMetricsServiceImpl][driverOrderCompleteAmountGreater500EstimatedAmountLess100][{}][{}] 订单完成金额大于500且预估金额小于100:{}",orderSerialNo,methodName,object);
            return Boolean.parseBoolean(object.toString());
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail) {
            log.warn("根据订单编号查询订单不存在:{}",orderSerialNo);
            return false;
        }
        if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            log.warn("订单状态不是行程结束或订单关闭:{}",orderSerialNo);
            return false;
        }
        return riskOrderDetail.getAmount().compareTo(new BigDecimal("500")) > 0 && riskOrderDetail.getBookUserAmount().compareTo(new BigDecimal("100")) < 0 ? true : false;
    }

    @Override
    public boolean orderCompleteActualBotimeEstimateDurationGreater30(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            log.error("订单时长异常指标，订单号不能为空");
            return false;
        }
        String methodName = "orderCompleteActualBotimeEstimateDurationGreater30";

        try {
            Object object = riskStrategyHandler.getObjectLocal(methodName);
            if (null != object) {
                log.info("[RiskMetricsServiceImpl][orderCompleteActualBotimeEstimateDurationGreater30][{}][{}] 订单完成实际时长大于预估时长30分钟内存值:{}",orderSerialNo,methodName,object);
                return Boolean.parseBoolean(object.toString());
            }
            CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
            if (null == riskOrderDetail) {
                log.warn("[RiskMetricsServiceImpl][orderCompleteActualBotimeEstimateDurationGreater30][{}][{}] 根据订单编号查询订单不存在",orderSerialNo,methodName);
                return false;
            }

            if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
                log.warn("[RiskMetricsServiceImpl][orderCompleteActualBotimeEstimateDurationGreater30][{}][{}] 订单状态不是行程结束或订单关闭",orderSerialNo,methodName);
                return false;
            }
            // 实际运行时长（秒）
            int realRunTime = riskOrderDetail.getRealTime();
            // 预估运行时长（秒）
            int estimateRunTime = riskOrderDetail.getEstimateTime();
            if (estimateRunTime <= 0 || realRunTime <= 0) {
                log.warn("[RiskMetricsServiceImpl][orderCompleteActualBotimeEstimateDurationGreater30][{}][{}] 订单实际运行时长或预估运行时长为0", orderSerialNo,methodName);
                return false;
            }
            // 预估时长(分)
            int oldEstimateMinute = (estimateRunTime % 60 == 0) ? (estimateRunTime / 60) : (estimateRunTime / 60) + 1;

            int diffTimes = realRunTime / estimateRunTime;

            log.info("[RiskMetricsServiceImpl][orderCompleteActualBotimeEstimateDurationGreater30][{}][{}] 订单实际运行时长或预估运行时长,estimateRunTime:{},realRunTime:{},oldEstimateMinute:{},diffTimes:{}", orderSerialNo,methodName,estimateRunTime,realRunTime,oldEstimateMinute,diffTimes);

            String oldEstimateMinuteConfig = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "oldEstimateMinute");
            String diffTimesConfig = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "diffTimes");
            log.info("[RiskMetricsServiceImpl][orderCompleteActualBotimeEstimateDurationGreater30][{}][{}] 订单完成实际时长大于预估时长统一配置,oldEstimateMinute:{},diffTimes:{}",orderSerialNo,methodName,oldEstimateMinuteConfig,diffTimesConfig);

            if (oldEstimateMinute > Integer.parseInt(oldEstimateMinuteConfig) && diffTimes >= Integer.parseInt(diffTimesConfig)) {
                riskStrategyHandler.setObjectLocal(methodName, true);
                log.info("[RiskMetricsServiceImpl][orderCompleteActualBotimeEstimateDurationGreater30][{}][{}] 订单完成实际时长大于预估时长30分钟true",orderSerialNo,methodName);
                return true;
            }
        } catch (Exception e) {
            LoggerUtils.error(log,"订单完成实际时长大于预估时长30分钟异常",e);
        }
        return false;
    }

    @Override
    public boolean orderCompleteActualBotimeEstimateDistanceGreater5km(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return false;
        }
        String methodName = "orderCompleteActualBotimeEstimateDistanceGreater5km";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Boolean.parseBoolean(object.toString());
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail) {
            return false;
        }

        if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            return false;
        }
        //实际运行里程（米)
        int realRunDistance = riskOrderDetail.getRealDistance();
        //预估运行里程（米）
        int estimateRunDistance = riskOrderDetail.getEstimateDistance();
        if(estimateRunDistance <= 0 || realRunDistance <= 0) {
            return false;
        }
        // 预估公里
        int oldRealKilo = estimateRunDistance / 1000;
        if (new BigDecimal(String.valueOf(oldRealKilo)).compareTo(new BigDecimal(5)) >= 0 && new BigDecimal(String.valueOf(realRunDistance)).divide(new BigDecimal(String.valueOf(estimateRunDistance)), 10, RoundingMode.HALF_UP).compareTo(new BigDecimal("2") ) >= 0) {
            riskStrategyHandler.setObjectLocal(methodName, true);
            return true;
        }
        return false;
    }

    @Override
    public int carOwnerAutoFinishOrderTag(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            log.error("订单号不能为空");
            return 0;
        }
        String methodName = "carOwnerAutoFinishOrderTag";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            log.info("[RiskMetricsServiceImpl][carOwnerAutoFinishOrderTag][{}][{}] 订单自动完成:{}", orderId, methodName, object);
            return Integer.parseInt(object.toString());
        }
        CarRiskOrderDetail order = orderDetailMapper.getByOrderSerialNo(orderId);
        if (Objects.isNull(order)) {
            return 0;
        }

        if (order.getOrderState() != OrderState.TRIP_FINISHED.getCode() && order.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            log.info("[RiskMetricsServiceImpl][carOwnerAutoFinishOrderTag][{}][{}] 订单状态不是行程结束或订单关闭", orderId, order.getOrderState());
            return 0;
        }

        String orderTags = Strings.EMPTY;
        if (StringUtils.isNotBlank(order.getCarOwnerTags())) {
            orderTags = order.getCarOwnerTags();
        }

        int hitAutoFinishOrderTag = orderTags.equals(CommonConstants.CAR_OWNER_AUTO_FINISH_ORDER_TAG) ? 1 : 0;
        riskStrategyHandler.setObjectLocal(methodName, hitAutoFinishOrderTag);
        log.info("[RiskMetricsServiceImpl][driverAutoFinishOrderTag] hitAutoFinishOrderTag = {}, orderId = {}, tags = {}",
                hitAutoFinishOrderTag, order.getOrderSerialNo(), order.getCarOwnerTags());
        return hitAutoFinishOrderTag;
    }

    @Override
    public boolean orderEstimateGreater30FinishBelow50Percent(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            log.error("订单号不能为空");
            return false;
        }
        String methodName = "orderEstimateGreater30FinishBelow50Percent";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            log.info("[RiskMetricsServiceImpl][orderEstimateGreater30FinishBelow50Percent][{}][{}] " +
                    "订单完单预估时长大于30min，且实际时长是预估时长的50%:{}", orderSerialNo, methodName, object);
            return Boolean.parseBoolean(object.toString());
        }
        CarRiskOrderDetail order = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (Objects.isNull(order)) {
            return false;
        }
        if (order.getOrderState() != OrderState.TRIP_FINISHED.getCode()
                && order.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            log.info("[RiskMetricsServiceImpl][orderEstimateGreater30FinishBelow50Percent][{}][{}] " +
                    "订单状态不是行程结束或订单关闭", orderSerialNo, order.getOrderState());
            return false;
        }
        // 实际运行时长（秒）
        int realRunTime = order.getRealTime();
        // 预估运行时长（秒）
        int estimateRunTime = order.getEstimateTime();

        String estimateRunTimeConfig = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "estimateRunTime");
        String realRunTimePercentConfig = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "realRunTimePercent");

        log.info("[RiskMetricsServiceImpl][orderEstimateGreater30FinishBelow50Percent] estimateRunTime = {}, realRunTime = {}, " +"统一配置estimateRunTimeConfig = {}, 统一配置realRunTimePercentConfig = {}", estimateRunTime, realRunTime, estimateRunTimeConfig, realRunTimePercentConfig);
        //判断订单完单预估时长大于等于30min，且实际时长是预估时长的50%以内
        if (estimateRunTime >= Integer.parseInt(estimateRunTimeConfig) * 60 && realRunTime > 0) {
            //计算时间与预估占比
            double realRunTimePercent = new BigDecimal(String.valueOf(realRunTime)).divide(new BigDecimal(String.valueOf(estimateRunTime)),
                    10, RoundingMode.HALF_UP).doubleValue();
            if (realRunTimePercent <= Double.parseDouble(realRunTimePercentConfig)) {
                riskStrategyHandler.setObjectLocal(methodName, true);
                log.info("[RiskMetricsServiceImpl][orderEstimateGreater30FinishBelow50Percent][{}][{}] " +
                        "订单完单预估时长大于30min，且实际时长是预估时长的50%以内:{}", orderSerialNo, methodName, realRunTimePercent);
                return true;
            }
        }
        return false;
    }

    @Override
    public BigDecimal singleOrderAmountGreater300(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            log.warn("订单编号不能为空");
            return BigDecimal.ZERO;
        }
        String methodName = "singleOrderAmountGreater300";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            log.info("[RiskMetricsServiceImpl][singleOrderAmountGreater300][{}][{}] 订单完成金额大于300:{}", orderSerialNo, methodName, object);
            return new BigDecimal(object.toString());
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail) {
            log.warn("根据订单编号查询订单不存在:{}", orderSerialNo);
            return BigDecimal.ZERO;
        }
        if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            log.warn("订单状态不是行程结束或订单关闭:{}", orderSerialNo);
            return BigDecimal.ZERO;
        }
        //订单金额
        riskStrategyHandler.setObjectLocal(methodName, riskOrderDetail.getAmount().toString());
        log.info("[RiskMetricsServiceImpl][singleOrderAmountGreater300][{}][{}] 订单完成金额:{}",
                orderSerialNo, methodName, riskOrderDetail.getAmount().toString());

        return riskOrderDetail.getAmount();
    }


    @Override
    public boolean samePassengerPaymentSameDriverThriceWithin24Hour(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return false;
        }
        String methodName = "samePassengerPaymentSameDriverWithin24Hour";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            log.info("[RiskMetricsServiceImpl][samePassengerPaymentSameDriverWithin24Hour][{}][{}] 同乘客同司机24小时内支付内存值:{}",orderSerialNo,methodName,object);
            return Boolean.parseBoolean(object.toString());
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail || StringUtils.isBlank(riskOrderDetail.getContactPhone())) {
            log.info("[RiskMetricsServiceImpl][samePassengerPaymentSameDriverWithin24Hour][{}][{}] 订单不存在",orderSerialNo,methodName);
            return false;
        }
        String hour = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "hour");
        String limit = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "limit");
        log.info("[RiskMetricsServiceImpl][samePassengerPaymentSameDriverWithin24Hour][{}][{}] 同乘客同司机24小时内统一配置支付小时数:{},次数:{}",orderSerialNo,methodName,hour,limit);
        List<CarRiskOrderDetail> carRiskOrderDetails = orderDetailMapper.samePassengerPaymentSameDriverWithin24Hours(riskOrderDetail.getContactPhone(),Integer.valueOf(hour));
        if (CollectionUtils.isEmpty(carRiskOrderDetails)) {
            log.info("[RiskMetricsServiceImpl][samePassengerPaymentSameDriverWithin24Hour][{}][{}] 同乘客同司机24小时内支付无数据",orderSerialNo,methodName);
            return false;
        }
        if (carRiskOrderDetails.size() < Integer.parseInt(limit)) {
            log.info("[RiskMetricsServiceImpl][samePassengerPaymentSameDriverWithin24Hour][{}][{}] 同乘客同司机24小时内支付次数小于3",orderSerialNo,methodName);
           return false;
        }

        List<String> carNumList = carRiskOrderDetails.stream().map(CarRiskOrderDetail::getCarNum).collect(Collectors.toList());

        if (hasConsecutiveStrings(carNumList,Integer.parseInt(limit))) {
            riskStrategyHandler.setObjectLocal(methodName, true);
            log.info("[RiskMetricsServiceImpl][samePassengerPaymentSameDriverWithin24Hour][{}][{}] 同乘客同司机24小时内支付true",orderSerialNo,methodName);
            return true;
        }
        return false;
    }

    @Override
    public boolean onlyDriverCompletesOrder(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return false;
        }
        String methodName = "onlyDriverCompletesOrder";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            log.info("[RiskMetricsServiceImpl][onlyDriverCompletesOrder][{}][{}] 订单仅司机完成订单内存值:{}",orderSerialNo,methodName,object);
            return Boolean.parseBoolean(object.toString());
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail) {
            log.info("[RiskMetricsServiceImpl][onlyDriverCompletesOrder][{}][{}] 订单不存在",orderSerialNo,methodName);
            return false;
        }
        if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            log.info("[RiskMetricsServiceImpl][onlyDriverCompletesOrder][{}][{}] 订单状态不是完成或关闭",orderSerialNo,methodName);
            return false;
        }
        if (riskOrderDetail.getOnlyDriverCompletes() == -1) {
            log.info("[RiskMetricsServiceImpl][onlyDriverCompletesOrder][{}][{}] 数据库默认值，未清洗",orderSerialNo,methodName);
            return false;
        }
        if (riskOrderDetail.getOnlyDriverCompletes() == 0) {
            log.info("[RiskMetricsServiceImpl][onlyDriverCompletesOrder][{}][{}] 仅司机操作完成订单",orderSerialNo,methodName);
            riskStrategyHandler.setObjectLocal(methodName, true);
            return true;
        }
        return false;
    }

    @Override
    public boolean orderHasNoTracks(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return false;
        }
        String methodName = "orderHasNoTracks";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Boolean.parseBoolean(object.toString());
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail || StringUtils.isBlank(riskOrderDetail.getThirdOrderNo())) {
            log.warn("[RiskMetricsServiceImpl][deptDistanceDeviationGreater3Km][{}][{}] 订单或第三方订单号不存在", orderSerialNo, methodName);
            return false;
        }

        if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            return false;
        }
        CarOwnerTrackInfo trackInfo = esQueryClient.getTrackInfoByOrderNo(riskOrderDetail.getThirdOrderNo());
        if (Objects.isNull(trackInfo)) {
            riskStrategyHandler.setObjectLocal(methodName, true);
            return true;
        }
        return false;
    }

    @Override
    public boolean deptDistanceDeviationGreater3Km(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return false;
        }
        String methodName = "deptDistanceDeviationGreater3Km";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Boolean.parseBoolean(object.toString());
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail || StringUtils.isBlank(riskOrderDetail.getThirdOrderNo())) {
            log.warn("[RiskMetricsServiceImpl][deptDistanceDeviationGreater3Km][{}][{}] 订单或第三方订单号不存在",orderSerialNo,methodName);
            return false;
        }

        if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            return false;
        }
        CarOwnerTrackInfo trackInfo = esQueryClient.getTrackInfoByOrderNo(riskOrderDetail.getThirdOrderNo());
        CarOwnerTrackInfo.LatitudeLongitudeInfo deptLatitudeLongitudeInfo = Optional.ofNullable(trackInfo)
                .map(CarOwnerTrackInfo::getStart)
                .orElse(null);

        String thresholdKm = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "thresholdKm");
        log.info("[RiskMetricsServiceImpl][deptDistanceDeviationGreater3Km][{}][{}] 出发地与上车点误差超3km统一配置阈值:{}",orderSerialNo,methodName,thresholdKm);
        if (Objects.nonNull(deptLatitudeLongitudeInfo)) {
            boolean withinDistance = GeoDistanceUtil.isWithinDistance(
                    deptLatitudeLongitudeInfo.getLatitude(),
                    deptLatitudeLongitudeInfo.getLongitude(),
                    riskOrderDetail.getDepLat(),
                    riskOrderDetail.getDepLon(),
                    Integer.parseInt(thresholdKm));
            riskStrategyHandler.setObjectLocal(methodName, withinDistance);
            return !withinDistance;
        }
        return false;
    }

    @Override
    public boolean arrDistanceDeviationGreater3Km(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return false;
        }
        String methodName = "arrDistanceDeviationGreater3Km";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Boolean.parseBoolean(object.toString());
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail || StringUtils.isBlank(riskOrderDetail.getThirdOrderNo())) {
            log.warn("[RiskMetricsServiceImpl][arrDistanceDeviationGreater3Km][{}][{}] 订单或第三方订单号不存在",orderSerialNo,methodName);
            return false;
        }

        if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            return false;
        }
        CarOwnerTrackInfo trackInfo = esQueryClient.getTrackInfoByOrderNo(riskOrderDetail.getThirdOrderNo());
        CarOwnerTrackInfo.LatitudeLongitudeInfo arrLatitudeLongitudeInfo = Optional.ofNullable(trackInfo)
                .map(CarOwnerTrackInfo::getEnd)
                .orElse(null);

        String thresholdKm = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "thresholdKm");
        log.info("[RiskMetricsServiceImpl][arrDistanceDeviationGreater3Km][{}][{}] 目的地与下车点误差超3km统一配置阈值:{}",orderSerialNo,methodName,thresholdKm);
        if (Objects.nonNull(arrLatitudeLongitudeInfo)) {
            boolean withinDistance = GeoDistanceUtil.isWithinDistance(
                    arrLatitudeLongitudeInfo.getLatitude(),
                    arrLatitudeLongitudeInfo.getLongitude(),
                    riskOrderDetail.getArrLat(),
                    riskOrderDetail.getArrLon(),
                    Integer.parseInt(thresholdKm));
            riskStrategyHandler.setObjectLocal(methodName, withinDistance);
            return !withinDistance;
        }
        return false;
    }

    @Override
    public boolean orderDistanceDeviationAbove50Percent(String orderSerialNo) {

        if (StringUtils.isBlank(orderSerialNo)) {
            return false;
        }
        String methodName = "orderDistanceDeviationAbove50Percent";
        Object object = riskStrategyHandler.getObjectLocal(methodName);
        if (null != object) {
            return Boolean.parseBoolean(object.toString());
        }
        CarRiskOrderDetail riskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
        if (null == riskOrderDetail || StringUtils.isBlank(riskOrderDetail.getThirdOrderNo())) {
            log.warn("[RiskMetricsServiceImpl][orderDistanceDeviationAbove50Percent][{}][{}] 订单不存在或第三方订单号不存在", orderSerialNo, methodName);
            return false;
        }
        if (riskOrderDetail.getOrderState() != OrderState.TRIP_FINISHED.getCode() && riskOrderDetail.getOrderState() != OrderState.ORDER_CLOSED.getCode()) {
            return false;
        }

        try {
            CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(orderSerialNo);

            //获取轨迹信息
            CarOwnerTrackInfo trackInfo = esQueryClient.getTrackInfoByOrderNo(riskOrderDetail.getThirdOrderNo());

            if (trackInfo == null || trackInfo.getStart() == null || trackInfo.getEnd() == null) {
                log.warn("[RiskMetricsServiceImpl][orderDistanceDeviationAbove50Percent][{}][{}] 估计信息不全，无法计算距离", orderSerialNo, methodName);
                return false;
            }

            //构建Lbs查询参数
            NavigateCondition navigateCondition = buildNavigateCondition(riskOrderDetail, carOrderDetail, trackInfo);

            //获取lbs距离
            Long maxLbsDistance = lbsClient.getMaxLbsDistance(navigateCondition);
            if (maxLbsDistance==null) {
                log.warn("[RiskMetricsServiceImpl][orderDistanceDeviationAbove50Percent][{}][{}] 未获取到lbs距离信息",
                        orderSerialNo, methodName);
                return false;
            }

            String estimateDistanceConfig = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "estimateDistance");
            String distanceDeviationPercentStart = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "distanceDeviationPercentStart");
            String distanceDeviationPercentEnd = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "distanceDeviationPercentEnd");
            log.info("[RiskMetricsServiceImpl][orderDistanceDeviationAbove50Percent][{}][{}] 统一配置预估距离:{},偏差百分比:{},{}", orderSerialNo, methodName, estimateDistanceConfig, distanceDeviationPercentStart,distanceDeviationPercentEnd);
            //获取订单预估距离
            int estimateDistance = riskOrderDetail.getEstimateDistance();
            if (estimateDistance>0 && estimateDistance>Integer.parseInt(estimateDistanceConfig)) {
                //计算订单里程偏差百分比，以预估距离做底数
                BigDecimal distanceDeviationPercent = new BigDecimal(maxLbsDistance).divide(new BigDecimal(estimateDistance), 2, RoundingMode.HALF_UP);
                log.info("[RiskMetricsServiceImpl][orderDistanceDeviationAbove50Percent][{}][{}] 订单距离偏差百分比:{}", orderSerialNo, methodName, distanceDeviationPercent.toPlainString());
                if (distanceDeviationPercent.compareTo(new BigDecimal(distanceDeviationPercentStart)) < 0 || distanceDeviationPercent.compareTo(new BigDecimal(distanceDeviationPercentEnd)) > 0) {
                    riskStrategyHandler.setObjectLocal(methodName, true);
                    return true;
                }
            }

        } catch (Exception e) {
            log.error("[RiskMetricsServiceImpl][orderDistanceDeviationAbove50Percent][{}][{}] 里程对比异常",
                    orderSerialNo, methodName, e);
        }
        return false;
    }

    @Override
    public boolean carOwnerDriverExtraSettlementTag(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            log.error("订单号不能为空");
            return false;
        }
        String methodName = "carOwnerDriverExtraSettlementTag";
        try {
            Object object = riskStrategyHandler.getObjectLocal(methodName);
            if (null != object) {
                log.info("[RiskMetricsServiceImpl][carOwnerDriverExtraSettlementTag][{}][{}] 免佣订单:{}", orderId, methodName, object);
                return Boolean.parseBoolean(object.toString());
            }
            CarRiskOrderDetail order = orderDetailMapper.getByOrderSerialNo(orderId);
            if (Objects.isNull(order) || StringUtils.isBlank(order.getContactPhone())) {
                log.warn("[RiskMetricsServiceImpl][carOwnerDriverExtraSettlementTag][{}][{}] 订单不存在", orderId, methodName);
                return false;
            }

            //前置时间(默认查最近12小时的免佣订单)
            String hour = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "hour");
            String limit = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "limit");
            //查询免佣订单数量
            List<CarRiskOrderDetail> list = orderDetailMapper.querySfcSamePassenger12HourDriverExtraOrder(order.getContactPhone(), Integer.parseInt(hour));
            if (CollectionUtils.isEmpty(list) || list.size() < Integer.parseInt(limit)) {
                log.info("[RiskMetricsServiceImpl][carOwnerDriverExtraSettlementTag][{}][{}] 同乘客同司机免佣订单小于阈值{}", orderId, methodName, limit);
                return false;
            }

            List<String> carNumList = list.stream().map(CarRiskOrderDetail::getCarNum).collect(Collectors.toList());

            if (hasConsecutiveStrings(carNumList, Integer.parseInt(limit))) {
                riskStrategyHandler.setObjectLocal(methodName, true);
                log.info("[RiskMetricsServiceImpl][carOwnerDriverExtraSettlementTag][{}][{}] 同乘客同司机连续免佣订单大于阈值:{}", orderId, methodName, limit);
                return true;
            }
        } catch (Exception e) {
            log.error("[RiskMetricsServiceImpl][carOwnerDriverExtraSettlementTag][{}][{}] 获取免佣订单异常", orderId, methodName, e);
            return false;
        }
        return false;
    }

    @Override
    public boolean carOwnerTotalDriverExtraSettlementTag(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            log.error("订单号不能为空");
            return false;
        }
        String methodName = "carOwnerTotalDriverExtraSettlementTag";
        try {
            Object object = riskStrategyHandler.getObjectLocal(methodName);
            if (null != object) {
                log.info("[RiskMetricsServiceImpl][carOwnerTotalDriverExtraSettlementTag][{}][{}] 免佣订单免佣卡使用次数超配:{}", orderId, methodName, object);
                return Boolean.parseBoolean(object.toString());
            }
            CarRiskOrderDetail order = orderDetailMapper.getByOrderSerialNo(orderId);
            if (Objects.isNull(order) || StringUtils.isBlank(order.getCarNum())) {
                log.warn("[RiskMetricsServiceImpl][carOwnerTotalDriverExtraSettlementTag][{}][{}] 订单不存在或查出车牌为空", orderId, methodName);
                return false;
            }

            //前置时间(默认查最近24小时的免佣订单)
            String hour = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "hour");
            String limit = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "limit");
            //查询免佣订单数量
            List<CarRiskOrderDetail> list = orderDetailMapper.queryCarOwnerFinishOrderByCarNumAndTime(order.getCarNum(), Integer.parseInt(hour));
            if (CollectionUtils.isEmpty(list) || list.size() > Integer.parseInt(limit)) {
                log.info("[RiskMetricsServiceImpl][carOwnerTotalDriverExtraSettlementTag][{}][{}] 当前司机免佣卡使用次数超配，阈值：{}", orderId, methodName, limit);
                riskStrategyHandler.setObjectLocal(methodName, true);
                return true;
            }
        } catch (Exception e) {
            log.error("[RiskMetricsServiceImpl][carOwnerTotalDriverExtraSettlementTag][{}][{}] 获取免佣订单异常", orderId, methodName, e);
            return false;
        }
        return false;
    }

    private NavigateCondition buildNavigateCondition(CarRiskOrderDetail riskOrderDetail, CarOrderDetail carOrderDetail, CarOwnerTrackInfo trackInfo) {
        return NavigateCondition.builder()
                .startLat(String.valueOf(trackInfo.getStart().getLatitude()))
                .startLng(String.valueOf(trackInfo.getStart().getLongitude()))
                .startPoi(Optional.ofNullable(carOrderDetail)
                        .map(CarOrderDetail::getOrderTrip)
                        .map(OrderTripInfo::getDepPoi)
                        .orElse(null))
                .endLat(String.valueOf(trackInfo.getEnd().getLatitude()))
                .endLng(String.valueOf(trackInfo.getEnd().getLongitude()))
                .endPoi(Optional.ofNullable(carOrderDetail)
                        .map(CarOrderDetail::getOrderTrip)
                        .map(OrderTripInfo::getDepPoi)
                        .orElse(null))
                .traceId(riskOrderDetail.getOrderSerialNo())
                .orderChannel(Optional.ofNullable(carOrderDetail)
                        .map(CarOrderDetail::getOrderChannel)
                        .orElse(Integer.parseInt(MetricStrategyChannelEnum.MADA_CAR_OWNER.getCode())))
                .passengerId(Optional.ofNullable(carOrderDetail)
                        .map(CarOrderDetail::getPassengerInfo)
                        .map(CarPassenger::getPassengerId)
                        .orElse(null))
                .build();
    }

    public static void main(String[] args) {
        System.out.println("Tomcat Version: " + ServerInfo.getServerInfo());
    }

    private boolean hasConsecutiveStrings(List<String> list,int limit) {
        if (list == null || list.size() < limit) return false;

        int count = 1;
        for (int i = 1; i < list.size(); i++) {
            count = list.get(i).equals(list.get(i-1)) ? count + 1 : 1;
            if (count >= limit) return true;
        }
        return false;
    }
}