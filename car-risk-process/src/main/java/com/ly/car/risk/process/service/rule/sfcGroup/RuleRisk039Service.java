package com.ly.car.risk.process.service.rule.sfcGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.repo.data.SfcRiskLimitData;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.SfcOrderNumDTO;
import com.ly.car.risk.process.service.rule.FilterOrderContext;
import com.ly.car.risk.process.service.rule.FilterRuleChain;
import com.ly.car.risk.process.service.rule.MapIsAllTrue;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RuleRisk039Service extends FilterRuleChain {

    private static final String ruleNo = "039";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Override
    public UiResult doHandler(FilterOrderContext orderContext) {

        log.info("[FilterRuleChain][RuleRisk038Service][{}][{}]前置判断已通过，进入规则039判断{}",orderContext.getMemberId(),orderContext.getDriverCardNo(), JsonUtils.json(orderContext.getCancelOrderNumList()));
        //判断当前是否执行此规则
        if(orderContext.getNeedRuleMap().get(ruleNo) == null && this.nextRule != null){
            return this.nextRule.doHandler(orderContext);
        }
        //没有下个规则就直接返回了
        if(this.nextRule == null && orderContext.getNeedRuleMap().get(ruleNo) == null){
            return orderContext.getUiResult();
        }
        orderContext.getNeedRuleMap().put(ruleNo,true);

        List<SfcOrderNumDTO> orderNumDTOList = orderContext.getCancelOrderNumList().stream()
                .filter(order->order.getCancelTime().after(TimeUtil.thirtyMin(orderContext.getSfcRiskRuleConfig().getTime039())))
                .filter(order-> StringUtils.isNotBlank(order.getOrderId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(orderNumDTOList) && orderNumDTOList.size() > orderContext.getSfcRiskRuleConfig().getOrderNum039()){
            List<String> phoneList = orderNumDTOList.stream().map(SfcOrderNumDTO::getPassengerCellPhone).distinct().collect(Collectors.toList());
            if(phoneList.size() >= orderContext.getSfcRiskRuleConfig().getPhoneNum039()){
                List<String> orderIds = orderNumDTOList.stream().map(SfcOrderNumDTO::getOrderId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(orderIds)){
                    distributionRiskManageService.addManageCommon(orderIds,ruleNo,2,1,0,null, RiskLevelEnum.HIGH.getCode());
                    log.info("[RuleRisk038Service][doHandler][{}][{}]命中039规则",orderContext.getMemberId(),orderContext.getUnionId());
                    UiResult result = UiResult.ok();
                    result.setMsg("风控不通过");
                    RiskResultDTO dto = new RiskResultDTO(405,"风控不通过039",null,null);
                    result.setData(dto);
                    orderContext.setUiResult(result);
                }
            }
        }

        //判断是否已经全部结束，全部结束则返回通过
        if(MapIsAllTrue.isAllTrue(orderContext.getNeedRuleMap()) || this.nextRule == null){
            return orderContext.getUiResult();
        }
        return this.nextRule.doHandler(orderContext);
    }
}
