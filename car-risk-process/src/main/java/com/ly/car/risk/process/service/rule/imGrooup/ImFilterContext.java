package com.ly.car.risk.process.service.rule.imGrooup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import lombok.Data;

@Data
public class ImFilterContext {

    private String driverCardNo;
    private String unionId;
    private String memberId;
    private String text;
    private String orderId;
    private Integer source=0;
    private UiResult uiResult = UiResult.ok();

    public ImFilterContext(){
        UiResult result = this.uiResult;
        result.setData(new RiskResultNewDTO());
    }

}
