package com.ly.car.risk.process.constants;

public enum DriverWarnNotifyEnum {

    sjaq006_mt("sjaq006","您的订单行驶方向偏离的安全提示","司机师傅您好，系统检测到您的行程可能存在方向偏离，请您确认行驶路线和方向是否正常，如在行程中出现任何问题请及时联系我们，萌艇车主安全中心全程为您提供安全保障服务."),
    sjaq004_mt("sjaq004","您的订单停留时间过长的安全行车提示","司机师傅您好，系统检测到你的行程出现短暂停留，如行程中出现任何安全问题，请及时与我们联系，萌艇车主安全中心全程为您提供安全保障服务。"),
    aq002_mt("aq002","您的订单行驶速度异常的安全行车提示","司机师傅您好，请您注意行驶速度，保持安全驾驶，避免因超速导致出现安全问题，萌艇车主安全中心全程为您提供安全保障服务。"),
    sjaq001_mt("sjaq001","您的夜间订单行车安全提示","司机师傅您好，订单行程已开始，请您提示乘客后排落座并系好安全带，夜间行车注意交通安全，萌艇车主安全中心全程保护您的行程。"),
    sjaq002_mt("sjaq002","您的远程订单行车安全提示","司机师傅您好，订单行程较远，请勿疲劳驾驶，夜间行车注意交通安全，萌艇车主安全中心全程保护您的行程。"),
    sjaq003_mt("sjaq003","您的夜间订单安全行车提示","司机师傅您好，订单行程较远，请勿疲劳驾驶，不要索要微信等联系方式，夜间行车注意交通安全，萌艇车主安全中心全程保护您的行程。"),
    sjaq005_mt("sjaq005","您的订单乘客是否安全送达的安全提示","司机师傅您好，乘客疑似提前下车，如已安全送达，请及时结束订单，萌艇车主安全中心全程保护您的行程。"),
    sjaq007_mt("sjaq007","您的订单行驶时间过长的安全提示","司机师傅您好，您的行程长时间未结束，如已安全送达，请及时结束订单，萌艇车主安全中心全程提供安全保障。"),
    sjaq008_mt("sjaq008","您的订单到达目的地的安全提示","行程已结束，请提醒乘客带好随身物品从右侧下车并注意后方来车。有问题请与我们联系，萌艇车主安全中心24小时为您服务。"),
    sjaq011_mt("sjaq011","乘客已上车提示","行程即将开始，请提醒乘客确认上车，行程全程录音，我们全程为您保驾护航。"),

    sjtx_im_mt("sjtx_im_mt","命中敏感词","沟通异常提醒：请注意沟通文明友好，不要进行线下交易等扰乱平台秩序行为，一经确认将进行管控和限制接单。"),

    sjaq006_hc("sjaq006_hc","您的订单行驶方向偏离的安全提示"," 司机师傅您好，系统检测到您的行程可能存在方向偏离，请您确认行驶路线和方向是否正常，如在过程中出现任何问题请及时联系我们，安全中心全程为您提供安全保障服务。"),
    sjaq004_hc("sjaq004_hc","您的订单停留时间过长的安全行车提示","司机师傅您好，系统检测到你的行程出现短暂停留，如行程中出现任何安全问题，请及时与我们联系，安全中心全程为您提供安全保障服务。"),
    aq002_hc("aq002_hc","您的订单行驶速度异常的安全行车提示","司机师傅您好，请您注意行驶速度，保持安全驾驶，避免因超速导致出现安全问题，安全中心全程为您提供安全保障服务。"),
    sjaq001_hc("sjaq001_hc","您的夜间订单行车安全提示","司机师傅您好，订单行程已开始，请您提示乘客后排落座并系好安全带，为了您和乘客的出行安全，尽量避免与乘客进行过度闲谈，夜间行车请您随时注意交通安全，行程中我们将对您的行驶轨迹进行实时监控，期间发生任何问题请及时联系我们。"),
    sjaq002_hc("sjaq002_hc","您的远程订单行车安全提示","司机师傅您好，订单行程已开始，此次订单行程较远，请您提示乘客后排落座并系好安全带，夜间行车请您随时注意交通安全，行程中我们将对您的行驶轨迹进行实时监控，期间发生任何问题请及时联系我们。"),
    sjaq003_hc("sjaq003_hc","您的夜间订单安全行车提示","司机师傅您好，订单行程已开始，此次订单行程较远，请您提示乘客后排落座并系好安全带，尽量避免与乘客进行过度闲谈，索要手机号、微信等联系方式，夜间行车请您随时注意交通安全，行程中我们将对您的行驶轨迹进行实时监控，期间发生任何问题请及时联系我们。"),
    sjaq005_hc("sjaq005_hc","您的订单乘客是否安全送达的安全提示","司机师傅您好，系统检测到你的行程疑似出现乘客提前下车，请确认是否安全将乘客送达目的地，如已送达，请及时结束订单，如行程中出现任何安全问题，请及时与我们联系，安全中心全程为您提供安全保障服务。"),
    sjaq007_hc("sjaq007_hc","您的订单行驶时间过长的安全提示","司机师傅您好，系统检测到您的行程长时间未结束，请您确认订单是否正常进行，如已安全送达乘客到达目的地，请及时结束订单，在过程中出现任何问题请及时联系我们，安全中心全程为您提供安全保障服务。"),
    sjaq008_hc("sjaq008_hc","您的订单到达目的地的安全提示","订单行程即将结束，请提示乘客带好随时物品准备下车，建议乘客从右后方下车，注意避让行人和后方来车，如在本次行程出现任何问题可随时联系我们，安全中心二十四小时为您服务。"),
    sjaq009_hc("sjaq009_hc","疲劳驾驶","司机师傅您好，今天您已经驾驶8个小时了，请注意休息，谨防疲劳驾驶，保持警惕，注意安全驾驶"),
    sjaq010_hc("sjaq010_hc","上下班高峰期","订单行程为上下班高峰期，安全中心提醒您，道路车多，请司机师傅注意行车安全，保持警惕，安全驾驶"),

    sjtx_im_hc("sjtx_im_hc","安全提醒","司机师傅您好，系统发现您与乘客沟通信息存在异常，提示您不要进行线下交易等扰乱平台秩序行为，一经确认我们将对您进行管控并限制接单"),

    BAD_PLAY_MOBILE("1_mt","打电话玩手机","同程安全中心提醒，带上耳机接打电话、聊语音，不分心、不分神，不用频繁低头看手机，安全又省心"),
    BAD_SLEEP("2_mt","打瞌睡闭眼","司机端播报：同程安全中心提醒，长时间行车，开窗换气通通风，一股新鲜空气进车中，提神又醒脑"),
    BAD_OVER_SPEED("3_mt","车速过快","同程安全中心提醒，前方虽然一路畅通，还是要慢慢开，让乘客和您一起看看沿途美丽的风景，养眼又舒心"),
    BAD_SPEED_UP("4_mt","急加速/减速","同程安全中心提醒，道路上的司机形形色色，不要因为人和事耽误了你的好心情，一路平缓驾驶，省油又安心"),
    BAD_CHANGE_LINE("5_mt","频繁变道","同程安全中心提醒，频繁变道超车很有感觉，但事故发生频率增加，驾车不急不躁，安全又舒心"),
    BAD_FOLLOW_NEARLY("6_mt","跟车过近","同程安全中心提醒，跟车过近容易发生追尾事故，驾车保持安全车距，堵车不堵心"),

    ;

    private String ruleNo;
    private String title;
    private String voice;

    public String getRuleNo() {
        return ruleNo;
    }

    public void setRuleNo(String ruleNo) {
        this.ruleNo = ruleNo;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getVoice() {
        return voice;
    }

    public void setVoice(String voice) {
        this.voice = voice;
    }

    DriverWarnNotifyEnum(String ruleNo, String title, String voice){
        this.ruleNo = ruleNo;
        this.title = title;
        this.voice = voice;
    }

    public static DriverWarnNotifyEnum getByRuleNo(String ruleNo){
        for(DriverWarnNotifyEnum notifyEnum : DriverWarnNotifyEnum.values()){
            if(notifyEnum.ruleNo.equals(ruleNo)){
                return notifyEnum;
            }
        }
        return null;
    }

}
