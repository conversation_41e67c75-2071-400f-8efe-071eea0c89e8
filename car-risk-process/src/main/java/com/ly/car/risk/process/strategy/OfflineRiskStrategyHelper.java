package com.ly.car.risk.process.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.OfflineMetricFieldMapper;
import com.ly.car.risk.process.repo.risk.mapper.OfflineMetricStrategyMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.OfflineMetricField;
import com.ly.car.risk.process.repo.risk.mapper.entity.OfflineRiskFieldDTO;
import com.ly.car.risk.process.repo.risk.mapper.entity.OfflineRiskSceneStrategyDTO;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import com.ly.car.risk.process.strategy.model.OfflineRiskStrategyDetail;
import com.ly.car.risk.process.strategy.model.detail.OfflineRiskFieldDetail;
import com.ly.car.risk.process.utils.LoggerUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

/**
 * Description of RiskStrategryHelper
 *
 * <AUTHOR>
 * @date 2024/5/31
 * @desc
 */
@Service
@Slf4j
public class OfflineRiskStrategyHelper implements InitializingBean {
    
    private static final ThreadLocal<HashMap<String, List<CarRiskOrderDetail>>> localCache = ThreadLocal.withInitial(() -> new HashMap<>());
    
    @Resource
    private OfflineMetricStrategyMapper strategyMapper;
    @Resource
    private OfflineMetricFieldMapper    fieldMapper;
    
    private CopyOnWriteArrayList<OfflineRiskStrategyDetail> riskStrategy = new CopyOnWriteArrayList<>();
    
    @Override
    public void afterPropertiesSet() throws Exception {
        ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(1);
        executorService.scheduleWithFixedDelay(this::initRiskStrategy, 0, 60, TimeUnit.SECONDS);
    }
    
    private void initRiskStrategy() {
        LoggerUtils.initLogMap("OFFLINE_STRATEGY_INIT", "", "", "");
        List<OfflineRiskStrategyDetail> riskStrategyList = new ArrayList<>();
        
        try {
            // 策略
            List<OfflineRiskSceneStrategyDTO> strategyList = strategyMapper.findStrategy();
            // 策略-指标关系
            Map<Long, List<OfflineRiskFieldDTO>> strategyFieldMap = fieldMapper.findField().stream().collect(Collectors.groupingBy(OfflineRiskFieldDTO::getStrategyId));
            // 指标
            Map<Long, OfflineMetricField> fieldMap = fieldMapper.selectList(new QueryWrapper<>()).stream().collect(Collectors.toMap(OfflineMetricField::getId, v -> v, (k1, k2) -> k1));
            
            for (OfflineRiskSceneStrategyDTO strategy : strategyList) {
                
                OfflineRiskStrategyDetail riskStrategy = new OfflineRiskStrategyDetail();
                riskStrategyList.add(riskStrategy);
                
                // 1.策略基础内容
                BeanUtils.copyProperties(strategy, riskStrategy);
                riskStrategy.setProductLine(StrUtil.split(strategy.getProductLines(), ",", true, true));
                riskStrategy.setChannels(StrUtil.split(strategy.getChannels(), ",", true, true));
                riskStrategy.setSupplierCodes(StrUtil.split(strategy.getSupplierCodes(), ",", true, true));
                if (riskStrategy.getChannels().contains("all")) {
                    // 代表不限
                    riskStrategy.setChannels(Collections.emptyList());
                }
                
                // 2.查出指标
                List<OfflineRiskFieldDTO> strategyFieldList = strategyFieldMap.get(strategy.getStrategyId());
                if (CollUtil.isEmpty(strategyFieldList)) {
                    continue;
                }
                
                List<OfflineRiskFieldDetail> riskFields = strategyFieldList.stream().map(fieldDTO -> {
                    OfflineRiskFieldDetail fieldDetail = new OfflineRiskFieldDetail();
                    fieldDetail.setFieldId(String.valueOf(fieldDTO.getField()));
                    fieldDetail.setOperator(fieldDTO.getOperator());
                    fieldDetail.setRightType(fieldDTO.getRightType());
                    fieldDetail.setRightValue(fieldDTO.getRightValue());
                    fieldDetail.setSort(fieldDTO.getSort());
                    OfflineMetricField field = fieldMap.get(fieldDTO.getField());
                    if (null != field) {
                        fieldDetail.setFieldNo(field.getFieldNo());
                        fieldDetail.setScript(field.getScript());
                        fieldDetail.setCategory(field.getCategory());
                        fieldDetail.setType(field.getType());
                        fieldDetail.setFieldName(field.getName());
                    }
                    return fieldDetail;
                }).collect(Collectors.toList());
                
                riskStrategy.setFields(riskFields);
            }
            
            riskStrategy = new CopyOnWriteArrayList<>(riskStrategyList);
            
        } catch (Exception ex) {
            LoggerUtils.error(log, "离线策略初始化 异常", ex);
        } finally {
            LoggerUtils.info(log, "离线策略初始化结束，初始化策略条数：{}", riskStrategy.size());
            LoggerUtils.removeAll();
        }
        
    }
    
    public List<OfflineRiskStrategyDetail> findStrategy(String during) {
        return riskStrategy.parallelStream().filter(s -> Objects.equals(s.getDuring(), during)).collect(Collectors.toList());
    }
    
}