package com.ly.car.risk.process.service.rule.mtGroup;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.CommonRiskClient;
import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.api.rsp.DriverReviewV2Rsp;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyReqExtConst;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverCheck;
import com.ly.car.risk.process.service.DriverCheckService;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.StrategyUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class MtDriverScoreService extends MtFilterHandler {

    @Resource
    private CommonRiskClient commonRiskClient;
    @Resource
    private TianChuangRiskClient tianChuangRiskClient;
    @Resource
    private MqSendConvertService mqSendConvertService;
    @Resource
    private DriverCheckService driverCheckService;

    @Override
    public void doHandler(MtFilterContext context) {
        RiskResultNewDTO dto = new RiskResultNewDTO();
        Map<String, Object> data = new HashMap<>();
        data.put("driverId", context.getParam().get("driverId"));
        data.put("idCard", context.getParam().getString("idCard"));
        Map<String, Object> resultMap = new HashMap<>();
        dto.setObj(data);
        if (!validParam(context.getParam())) {
            dto.setCode(1);
            dto.setMessage("验证异常！");
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
            context.getDto().setCode(1);
            context.getDto().setMessage("参数缺失！");
            resultMap.put("scoreInfo", 1);
            context.getDto().setObj(resultMap);
            return;
        }
        //验证次数
        List<DriverCheck> driverCheckList = context.getDriverCheckService()
                .queryResult(1, context.getParam().getString("name"), context.getParam().getString("idCard"));
        driverCheckList = driverCheckList.stream().filter(driver -> driver.getResult() == 0).collect(Collectors.toList());
        //过了就不在验证三方，直接走下面的流程
        if (CollectionUtils.isNotEmpty(driverCheckList)) {
            if (this.nextHandler == null && dto.getCode() == 0) {
                mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
            } else {
                this.nextHandler.doHandler(context);
            }
            return;
        }

        StringBuilder msg = new StringBuilder();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("name", (String) context.getParam().get("name"));
        paramMap.put("idNum", (String) context.getParam().get("idCard"));
        paramMap.put("productLine", (String) context.getParam().get("productLine"));
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setName((String) context.getParam().get("name"));
        param.setIdCard((String) context.getParam().get("idCard"));
        param.setProductLine((String) context.getParam().get("productLine"));
//        Integer result = tianChuangRiskClient.verifyScoreInfo(param,msg);
//        context.getDriverCheckService().insert(context.getParam(),1,result,msg.toString());

        StringBuilder msg1 = new StringBuilder();
//        Integer result = commonRiskClient.queryDriverByBds(paramMap,msg1);
        Integer result = this.queryDriverRiskV2(paramMap, msg1);

        context.getDriverCheckService().insert(context.getParam(), 1, result, msg1.toString());
        if (result != 0) {
            //发送mq
            dto.setCode(1);
            dto.setMessage("背景信息调查异常，认证失败");
            resultMap.put("scoreInfo", 1);
            data.put("resultMap", resultMap);
            dto.setObj(data);
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
            context.getDto().setCode(1);
            context.getDto().setMessage("");
            resultMap.put("scoreInfo", 1);
            context.getDto().setObj(resultMap);
            return;
        }
        if (this.nextHandler == null && dto.getCode() == 0) {
            mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"), JsonUtils.json(dto));
        } else {
            this.nextHandler.doHandler(context);
        }
    }

    public Boolean validParam(Map<String, Object> context) {
        if (context.get("driverId") == null) {
            return false;
        }
        if (context.get("idCard") == null) {
            return false;
        }
        if (context.get("name") == null) {
            return false;
        }
        return true;
    }

    public Integer queryDriverRiskV2(Map<String, String> param, StringBuilder msg) {
        DriverReviewV2Rsp driverReviewV2Rsp = this.commonRiskClient.queryDriverRiskV2(param);
        if (driverReviewV2Rsp == null || driverReviewV2Rsp.getData() == null || driverReviewV2Rsp.getData().getRetData() == null) {
            msg.append("系统异常，请联系平台");
            return 1;
        }
        if (driverReviewV2Rsp.getData().getRetData().getHit().equals("0")) {
            msg.append("核验正常");
            return 0;
        }
        //转成map
        DriverReviewV2Rsp.RetData retData = driverReviewV2Rsp.getData().getRetData();
        Map<String, String> retDataMap = JSONObject.parseObject(JSONObject.toJSONString(retData), Map.class);
        Map<String, String> hitDataMap = new HashMap<>();
        for (Map.Entry<String, String> entry : retDataMap.entrySet()) {
            if (!entry.getValue().equals("0")) {
                if (!entry.getKey().equals("hit")) {
                    hitDataMap.put(entry.getKey(), entry.getValue());
                }

            }
        }
        if (hitDataMap.size() > 1) {
            //直接命中
            msg.append(StringUtils.join(hitDataMap.keySet(), ","));
            return 1;
        }
        //到这边直接是只有一个命中的了
        for (Map.Entry<String, String> entry : hitDataMap.entrySet()) {
            msg.append(entry.getKey() + "_" + entry.getValue());
            if (entry.getKey().equals("score_A_1") || entry.getKey().equals("score_A_7")) {
                return 0;
            } else if (entry.getKey().startsWith("score_A")) {
                if (Integer.valueOf(entry.getValue()) > 3) {
                    return 0;
                } else {
                    return 1;
                }
            } else {
                //剩余的情况不通过
                return 1;
            }
        }
        return 1;
    }

    public RiskSceneResult scoreCheck(UnifyCheckRequest request) {
        String certName = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NAME);
        String certNo = StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NO);

        RiskResultNewDTO dto = new RiskResultNewDTO();
        Map<String, Object> data = new HashMap<>();
        data.put("driverId", StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.DRIVER_ID));
        data.put("idCard", certNo);
        Map<String, Object> resultMap = new HashMap<>();
        dto.setObj(data);

        if (StringUtils.isAnyBlank(certName, certNo)) {
            dto.setCode(1);
            dto.setMessage("验证异常！");
            mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
            return RiskSceneResult.fail("信息不匹配，请提交有效信息");
        }

        //验证次数
        List<DriverCheck> driverCheckList = driverCheckService.queryResult(1, certName, certNo);
        driverCheckList = driverCheckList.stream().filter(driver -> driver.getResult() == 0).collect(Collectors.toList());
        //过了就不在验证三方，直接走下面的流程
        if (CollectionUtils.isNotEmpty(driverCheckList)) {
            return RiskSceneResult.pass("");
        }

        StringBuilder msg = new StringBuilder();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("name", certName);
        paramMap.put("idNum", certNo);
        paramMap.put("productLine", request.getProductLine());
        Integer result = this.queryDriverRiskV2(paramMap, msg);
        driverCheckService.insert(request, 1, result, msg.toString());
        if (result != 0) {
            //发送mq
            dto.setCode(1);
            dto.setMessage("背景信息调查异常，认证失败");
            resultMap.put("scoreInfo", 1);
            data.put("resultMap", resultMap);
            dto.setObj(data);
            mqSendConvertService.sendNotifyMq("MT", JsonUtils.json(dto));
            return RiskSceneResult.fail("背景信息调查异常，认证失败");
        }
        return RiskSceneResult.pass("");
    }
}
