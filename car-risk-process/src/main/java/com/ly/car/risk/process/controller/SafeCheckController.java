package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSON;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.request.safecenter.*;
import com.ly.car.risk.process.model.enums.WorkOrderEnum;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.rule.common.SafeWarningResult;
import com.ly.car.risk.process.service.safecenter.SafeCenterService;
import com.ly.car.risk.process.support.UiResultWrapper;
import com.ly.car.risk.process.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description of SafeCheckController
 *
 * <AUTHOR>
 * @date 2024/3/12
 * @desc 安全中心相关controller
 */
@RestController
@RequestMapping("/safecenter/")
@Slf4j
public class SafeCheckController {

    @Resource
    private CarOrderService carOrderService;

    @Resource
    private SafeCenterService safeCenterService;

    /**
     * 安全提示
     */
    @RequestMapping("/safeReminder")
    public UiResultWrapper<List<SafeWarningResult>> safeReminder(@RequestBody ReminderRequest request) throws Exception {
        LoggerUtils.setFilter(request.getTraceId(), request.getOrderId());
        LoggerUtils.info(log, "安全提示 req:{}", JSON.toJSONString(request));
        try {
            UiResult result = safeCenterService.querySafeReminder(request);
            LoggerUtils.info(log, "安全提示 resp:{}", JSON.toJSONString(result));
            return UiResultWrapper.convert(result);
        } catch (Exception e) {
            LoggerUtils.error(log, "安全提示，未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }

    /**
     * 人车不符工单反馈
     */
    @RequestMapping("/workOrderFeedBack")
    public UiResultWrapper workNoFeedBack(@RequestBody AppealOrderRequest request) throws Exception {
        LoggerUtils.setFilter(request.getTraceId(), request.getOrderId());
        LoggerUtils.info(log, "人车不符工单反馈 req:{}", JSON.toJSONString(request));
        try {
            request.setWorkOrderType(WorkOrderEnum.YC_CAR_NUMBER_NOT_MATCH);
            UiResult result = safeCenterService.feedBackWorkOrder(request);
            LoggerUtils.info(log, "人车不符工单反馈 resp:{}", JSON.toJSONString(request));
            return UiResultWrapper.convert(result);
        } catch (BizException e) {
            LoggerUtils.warn(log, "人车不符工单反馈，业务异常", e);
            return UiResultWrapper.fail(-1,e.getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "人车不符工单反馈，未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }

    /**
     * 工单反馈入口查询
     */
    @RequestMapping("/queryWorkOrderFeedBack")
    public UiResultWrapper queryWorkOrderFeedBack(@RequestBody AppealOrderRequest request) throws Exception {
        LoggerUtils.setFilter(request.getTraceId(), request.getOrderId());
        LoggerUtils.info(log, "工单反馈入口查询 req:{}", JSON.toJSONString(request));
        try {
            UiResult result = safeCenterService.queryWorkOrderFeedBack(request);
            LoggerUtils.info(log, "工单反馈入口查询 resp:{}", JSON.toJSONString(request));
            return UiResultWrapper.convert(result);
        } catch (Exception e) {
            LoggerUtils.error(log, "工单反馈入口查询，未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }


    /**
     * 紧急联系人修改
     */
    @RequestMapping("/saveEmergencyContact")
    public UiResultWrapper saveEmergencyContact(@RequestBody EmergencyContactRequest request) throws Exception {
        LoggerUtils.setFilter(request.getTraceId(), request.getMemberId());
        try {
            LoggerUtils.info(log,"紧急联系人修改,req:{}",JSON.toJSONString(request));
            UiResult uiResult = safeCenterService.saveEmergencyContact(request);
            return UiResultWrapper.convert(uiResult);
        } finally {
            LoggerUtils.removeAll();
        }
    }

    /**
     * 分享配置修改 （包含时间，开关，以及授权）
     */
    @RequestMapping("/saveShareInfo")
    public UiResultWrapper saveShareInfo(@RequestBody EmergencyContactRequest request) throws Exception {
        LoggerUtils.setFilter(request.getTraceId(), request.getMemberId());
        try {
            LoggerUtils.info(log,"分享配置修改,req:{}",JSON.toJSONString(request));
            UiResult uiResult = safeCenterService.saveShareInfo(request);
            return UiResultWrapper.convert(uiResult);
        } finally {
            LoggerUtils.removeAll();
        }
    }
    /**
     * 查询分享配置（包含紧急联系人）
     */
    @RequestMapping("/queryUserShareInfo")
    public UiResultWrapper queryUserShareInfo(@RequestBody EmergencyContactRequest request) throws Exception {

        UiResult result = safeCenterService.queryUserShareInfo(request);

        return UiResultWrapper.convert(result);
    }

    /**
     * 110告警工单
     */
    @RequestMapping("/saveAlterWorkOrder")
    public UiResultWrapper saveAlterWorkOrder(@RequestBody AppealOrderRequest request) throws Exception {
        LoggerUtils.setFilter(request.getTraceId(), request.getOrderId());
        LoggerUtils.info(log, "110告警工单 req:{}", JSON.toJSONString(request));
        try {
            request.setWorkOrderType(WorkOrderEnum.SAFETY_POLICE);
            UiResult result = safeCenterService.saveAlterWorkOrder(request);
            LoggerUtils.info(log, "110告警工单 resp:{}", JSON.toJSONString(request));
            return UiResultWrapper.convert(result);
        } catch (BizException e) {
            LoggerUtils.warn(log, "110告警工单，业务异常", e);
            return UiResultWrapper.fail(-1,e.getMessage());
        }  catch (Exception e) {
            LoggerUtils.error(log, "110告警工单，未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }

    }

    /**
     * 物品遗失工单反馈
     */
    @RequestMapping("/saveItemMissingAppear")
    public UiResultWrapper saveItemMissingAppear(@RequestBody AppealOrderRequest request) throws Exception {
        LoggerUtils.setFilter(request.getTraceId(), request.getOrderId());
        LoggerUtils.info(log, "物品遗失工单反馈 req:{}", JSON.toJSONString(request));
        try {
            request.setWorkOrderType(WorkOrderEnum.ITEM_MISSING);
            UiResult result = safeCenterService.saveItemMissingAppear(request);
            LoggerUtils.info(log, "物品遗失工单反馈 resp:{}", JSON.toJSONString(request));
            return UiResultWrapper.convert(result);
        } catch (BizException e) {
            LoggerUtils.warn(log, "物品遗失工单反馈，业务异常", e);
            return UiResultWrapper.fail(-1,e.getMessage());
        }  catch (Exception e) {
            LoggerUtils.error(log, "物品遗失工单反馈，未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }


    /**
     * 查询费用申诉申诉
     */
    @RequestMapping("/queryFeeAppearWorkOrder")
    public UiResult queryFeeAppearWorkOrder(@RequestBody AppealOrderRequest request) throws Exception {
        UiResult result = safeCenterService.queryFeeAppearWorkOrder(request);
        return result;
    }
}