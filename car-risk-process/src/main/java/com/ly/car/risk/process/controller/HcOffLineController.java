package com.ly.car.risk.process.controller;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.service.HcOffLineRiskService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("hcOffLine")
@RestController
public class HcOffLineController {

    @Resource
    private HcOffLineRiskService hcOffLineRiskService;

    @RequestMapping("queryRiskOrder")
    public UiResult queryRiskOrder(){
        hcOffLineRiskService.queryHcRiskOrder();
        return UiResult.ok();
    }
}
