package com.ly.car.risk.process.supplier.Tsan;

import lombok.Data;

import java.util.List;

@Data
public class T3ViolationSyncParam extends T3BaseReq{

    private String violationId;//违规id
    private String orderNo;//T3平台订单号
    private String carNum;//车牌号
    private String disposeReasonCode;//处置类目code
    private Long appealEndTime;//违规申诉截止时间戳
    private List<DisposeDetail> disposeDetails;
    private Integer violationStatus;//违规状态 1：识别有责 2：违规撤销
    private Long violationTime;//违规发生时间戳

    @Data
    public static class DisposeDetail{
        private String unionId;//处罚的唯一标识，会去重，我们的业务只会传一条
        private Integer disposeType;//1-罚款 5- 禁封 6-价格清零，7-退款，8-调价
        private String reason;//原因
        private PriceChangeDetail priceChangeDetail;//流水扣减明细
        private Integer disposeFare;//罚款金额，类型为罚款时必填
        private Long disposeStartTime;//禁封开始时间
        private Long disposeEndTime;//禁封结束时间
    }

    @Data
    public static class PriceChangeDetail{
        private Integer changeFee;//总扣减金额
        private Integer changeOrderFee=0;//行程费扣减金额
        private Integer changeAttachFee=0;//附加费扣减金额
//        private String changeCrossCityFee="0";//跨城费扣减金额
//        private String changeFestivalFee="0";//节日服务费扣减金额
        private Integer changeParkingFee=0;//停车费扣减金额
        private Integer changeHighwayFee=0;//高速费扣减金额
        private Integer changeRoadBrigeFee=0;//路桥费扣减金额
        private Integer changeOtherFee=0;//其他附加费扣减金额
    }

}
