package com.ly.car.risk.process.turboMQ.consumer;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.repo.risk.mapper.entity.ActivityRelation;
import com.ly.car.risk.process.service.ActivityRelationService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.dto.SfcOrderStatusChangeEventOrderData;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class SfcStatusChangeConsumer implements MessageListenerConcurrently {

    private MqRiskProducer mqRiskProducer;
    private ActivityRelationService activityRelationService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for(MessageExt messageExt : list){
            try {
                String body = new String(messageExt.getBody(),"utf-8");
                log.info("[binlog][SfcStatusChangeConsumer][][][]收到turbo消息,消息id={}, body={}", messageExt.getMsgId(), body);
                SfcOrderStatusChangeEventOrderData eventOrderData = JsonUtils.json(body, SfcOrderStatusChangeEventOrderData.class);
//                if(eventOrderData.getOrderInfo().getStatus() == 20 && eventOrderData.getOldOrderStatus() != 20){
//                    //发送轮询任务,司机接单后开始监听通话记录
//                    mqRiskProducer = SpringContextUtil.getBean("riskSecurityProducer");
//                    JSONObject jsonObject = new JSONObject();
//                    jsonObject.put("orderId",eventOrderData.getOrderInfo().getOrderId());
//                    jsonObject.put("startTime",new Date());
//                    mqRiskProducer.send(MqTagEnum.car_risk_self_security_task,JsonUtils.json(jsonObject), DateUtil.addMinute(new Date(),5).getTime());
//                }
//                if(eventOrderData.getOrderInfo().getStatus() != 300 && eventOrderData.getOrderInfo().getStatus() == 300){
//                    //查询活动
//                    activityRelationService = SpringContextUtil.getBean("activityRelationService");
//                    List<ActivityRelation> activityRelationList = activityRelationService.queryActivityRelation(eventOrderData.getOrderInfo().getUnionId());
//                    if(CollectionUtils.isEmpty(activityRelationList)){
//                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
//                    }
//                    //将订单绑定到关系表里面去
//                    for(ActivityRelation relation : activityRelationList) {
//                        relation.convertActivityOrderId(eventOrderData.getOrderInfo().getOrderId());
//                        activityRelationService.updateActivityRelation(relation);
//                    }
//                }
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
