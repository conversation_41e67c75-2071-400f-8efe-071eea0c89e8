package com.ly.car.risk.process.service;

import com.ly.car.risk.process.api.ListByMemberIdParam;
import com.ly.car.risk.process.api.MemberApi;
import com.ly.car.risk.process.api.MemberQueryParam;
import com.ly.car.risk.process.api.dto.MemberData;
import com.ly.car.risk.process.api.rsp.MemberQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class MemberService {

    @Resource
    private MemberApi memberApi;

    MemberQueryResponse getInfoByMemberId(String memberId)
    {
        MemberQueryResponse memberInfoByMemberId = memberApi.getMemberInfoByKey(new MemberQueryParam(memberId, 33,1));
        try {
            if(memberInfoByMemberId == null || null == memberInfoByMemberId.getData() || StringUtils.isBlank(memberInfoByMemberId.getData().getMobile())){
                List<MemberData> listByMemberId = memberApi.getListByMemberId(new ListByMemberIdParam(memberId)).getData();
                for(MemberData data : listByMemberId){
                    if(data.getMemberId().equals(memberId)){
                        continue;
                    }
                    memberInfoByMemberId = memberApi.getMemberInfoByKey(new MemberQueryParam(String.valueOf(data.getMemberId()), 0,1));
                }
            }
        } catch (Exception e) {
            log.error("获取手机号出错：{}",e);
        }
        return memberInfoByMemberId;
    }
}
