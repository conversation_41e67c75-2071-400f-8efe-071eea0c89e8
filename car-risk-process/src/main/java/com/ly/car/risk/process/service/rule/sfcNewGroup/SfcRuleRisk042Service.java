package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.redis.OrderPassengerCellPhone;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 一个memberID在一个小时内接单后取消2笔以上，且下单目的地为3个城市以上，此策略对涉及memberID进行拉黑处置
 * */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk042Service extends FilterSfcHandler{

    private static final String ruleNo = "042";
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        log.info("[FilterRuleChain][SfcRuleRisk042Service][][]前置判断已通过，进入规则042判断{}", JsonUtils.json(context.getMemberCancelList()));
        try {
            if(!context.getSfcRiskRuleConfig().getOnOff042()){
                if(this.nextHandler != null){
                    this.nextHandler.doHandler(context);
                }
                return;
            }
            List<OrderStatusCancelDTO> dealList = context.getMemberCancelList().stream()
                    .filter(data->data.getCancelTime().after(DateUtil.addMinute(new Date(),-context.getSfcRiskRuleConfig().getTime042())))
                    .filter(data->data.getStartCityId() != null)
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(dealList) && dealList.size() > context.getSfcRiskRuleConfig().getOrderNum042()){
                List<Integer> cityNum = dealList.stream().map(OrderStatusCancelDTO::getStartCityId).distinct().collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(cityNum) && cityNum.size() > context.getSfcRiskRuleConfig().getCityNum042()){
                    List<String> orderIds = dealList.stream().map(OrderStatusCancelDTO::getOrderId).collect(Collectors.toList());
                    distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                    log.info("[SfcRuleRisk042Service][doHandler][{}][{}]命中042规则{}",context.getMemberId(),context.getUnionId(),orderIds);
                    RiskResultDTO dto = new RiskResultDTO(405,"风控不通过042",null,null);
                    context.getUiResult().setData(dto);
                    context.getUiResult().setMsg("风控不通过");

                    if(StringUtils.isBlank(context.getRuleNo())){
                        context.setRuleNo(ruleNo);
                    } else {
                        context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                    }
                    riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                }
            }
        } catch (Exception e) {
            log.error("[SfcRuleRisk042Service][doHandler][{}][{}]报错信息:{}",context.getMemberId(),context.getUnionId(),e);
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
