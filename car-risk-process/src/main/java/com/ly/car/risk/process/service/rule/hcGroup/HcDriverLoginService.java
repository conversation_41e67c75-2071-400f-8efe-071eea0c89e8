package com.ly.car.risk.process.service.rule.hcGroup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.hitchorder.entity.DriverLoginRecord;
import com.ly.car.risk.process.repo.hitchorder.mapper.DriverLoginRecordMapper;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HcDriverLoginService extends FilterHcAroundHandler{

    @Resource
    private DriverLoginRecordMapper driverLoginRecordMapper;

    @Override
    public void doHandler(FilterHcContext context) {
        List<DriverLoginRecord> driverLoginRecords = this.driverLoginRecordMapper.selectList(
                new QueryWrapper<DriverLoginRecord>().eq("device_id", context.getDeviceId())
                        .gt("create_time", DateUtil.addHour(new Date(), -context.getSceneConfig().getScene7_3Num()))

        );
        List<DriverLoginRecord> collect = driverLoginRecords.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(DriverLoginRecord::getMemberId))),ArrayList::new)
        );
        if(CollectionUtils.isNotEmpty(collect) && collect.size() > context.getSceneConfig().getScene7_3Num()){
            RiskResultNewDTO<Object> resultNewDTO = new RiskResultNewDTO<>(405,"同设备登录账户数异常","");
            context.getUiResult().setData(resultNewDTO);
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }
    }

    @Override
    public void doAfter(RiskResultNewDTO resultDTO) {

    }
}
