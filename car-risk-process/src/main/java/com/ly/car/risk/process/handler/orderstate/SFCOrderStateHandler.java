package com.ly.car.risk.process.handler.orderstate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.SfcOrderNumDTO;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.utils.DateUtilRisk;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * Description of SfcOrderStateHandler
 *
 * <AUTHOR>
 * @date 2024/3/13
 * @desc
 */
@Service
@Slf4j
public class SFCOrderStateHandler extends AbstractOrderStateHandler {

    @Override
    public String supportType() {
        return "SFC";
    }

    @Override
    public void dealDispatchingState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
        sendOrderInfoToKafka(orderDetail, fromState, toState);
        // 分销单，跳过
        if(orderDetail.isDistributionOrder()){
            return;
        }
        binLogProducer.send(MqTagEnum.car_risk_place_order_phone, orderDetail.getOrderId(), DateUtilRisk.addSeconds(new Date(), 5).getTime());
        LoggerUtils.info(log, "发送给[binlogConsumer]，tag:{},msg:{}", MqTagEnum.car_risk_place_order_phone.name(), orderDetail.getOrderId());
    }

    @Override
    public void dealReceiveOrderState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
        sendOrderInfoToKafka(orderDetail, fromState, toState);
//        virutalCallCheckTask(orderDetail);
    }

    @Override
    public void dealCancelState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
        sendOrderInfoToKafka(orderDetail, fromState, toState);

        binLogProducer.send(MqTagEnum.car_risk_convert_sfc_data, orderDetail.getOrderId(), DateUtilRisk.addSeconds(new Date(), 10).getTime());
        LoggerUtils.info(log, "发送给[binlogConsumer],tag:{},msg:{}",  MqTagEnum.car_risk_convert_sfc_data.name(), orderDetail.getOrderId());

        if(orderDetail.isDistributionOrder()){  //分销单下面的不执行
            return;
        }

        //发一个5s的延迟mq，顺风车取消事件，将内容最终加入缓存
        binLogProducer.send(MqTagEnum.car_risk_binlog_sfc_cancel, orderDetail.getOrderId(), DateUtilRisk.addSeconds(new Date(), 5).getTime());
        LoggerUtils.info(log, "发送给[binlogConsumer],tag:{},msg:{}",  MqTagEnum.car_risk_binlog_sfc_cancel.name(), orderDetail.getOrderId());

        SfcOrderNumDTO sfcOrderNumDTO = new SfcOrderNumDTO(orderDetail.getOrderId(), new Date(), orderDetail.getPassengerInfo().getPassengerCellPhone());
        String key = RedisKeyConstants.SFC_USER_ORDER_NUMBER + orderDetail.getMemberId();
        saveScoredSortedSetService.save(key, 3 * 24 * 60 * 60L, sfcOrderNumDTO, sfcOrderNumDTO.getCancelTime().getTime());

    }

    @Override
    public void dealInTripState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
        if(orderDetail.isDistributionOrder()){
            return;
        }
        // 行程自动分享
        safetyTripShare(orderDetail);
        // 用户上车
        binLogProducer.send(MqTagEnum.car_risk_sfc_user_onCar, orderDetail.getOrderId(), DateUtilRisk.addSeconds(new Date(), 10).getTime());
        LoggerUtils.info(log, "发送给[binlogConsumer],tag:{},msg:{}", MqTagEnum.car_risk_sfc_user_onCar.name(), orderDetail.getOrderId());
        // 预估行程记录
        binLogProducer.send(MqTagEnum.car_risk_safe_warning, orderDetail.getOrderId(), 0L);
        LoggerUtils.info(log, "发送给[binlogConsumer],tag:{},msg:{}", MqTagEnum.car_risk_safe_warning.name(), orderDetail.getOrderId());

    }

    @Override
    public void dealTripFinishState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
        sendOrderInfoToKafka(orderDetail, fromState, toState);
        //这个时候洗到数据库,并且放到缓存
        OrderRiskContext orderRiskContext = OrderUtils.convertSFCTripFinishOrderCache(orderDetail);
        // 不是分销单,才根据memberId记录
        if(!orderDetail.isDistributionOrder()){
            saveScoredSortedSetService.save(RedisKeyConstants.USER_SLIDING_WINDOW + orderRiskContext.getMemberId(), 3 * 24 * 60 * 60L,
                    orderRiskContext, DateUtil.string2Date(orderRiskContext.getFinishTime()).getTime());
        }

        saveScoredSortedSetService.save(RedisKeyConstants.DRIVER_SLIDING_WINDOW + orderRiskContext.getDriverCardNo(), 3 * 24 * 60 * 60L,
                orderRiskContext, DateUtil.string2Date(orderRiskContext.getFinishTime()).getTime());

        //完单的发送个mq监听
        binLogProducer.send(MqTagEnum.car_risk_convert_sfc_data, orderDetail.getOrderId(), DateUtilRisk.addSeconds(new Date(), 10).getTime());
        LoggerUtils.info(log, "发送给[binlogConsumer],tag:{},msg:{}", MqTagEnum.car_risk_convert_sfc_data.name(), orderDetail.getOrderId());

        //如果存在顺风车行程过半确认缓存，则清除掉
        String sfcHalfTripConfirmCache = String.format(RedisKeyConstants.SAFE_REMINDER_ORDER_NO_HINT_RULE_CACHE, orderDetail.getOrderId());
        RMap<String, String> cacheMap = redissonClient.getMap(sfcHalfTripConfirmCache);
        if (cacheMap.isExists()) {
            cacheMap.delete();
        }

    }


    private void sendOrderInfoToKafka(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId", orderDetail.getOrderId());

        switch (toState) {
            case DISPATCHING:
                jsonObject.put("eventTime", orderDetail.getBaseInfo().getGmtCreate());
                jsonObject.put("eventType", "1");
                break;
            case RECEIVING_ORDER:
                jsonObject.put("eventTime", new Date());
                jsonObject.put("eventType", "2");
                break;
            case CANCELED:
                jsonObject.put("eventTime", orderDetail.getBaseInfo().getGmtCanceled());
                jsonObject.put("eventType", "3");
                break;
            case TRIP_FINISHED:
                jsonObject.put("eventTime", orderDetail.getBaseInfo().getGmtTripFinished());
                jsonObject.put("eventType", "4");
                break;
            default:
                return;
        }

        binLogProducer.send(MqTagEnum.car_risk_order_event_topic, JsonUtils.json(jsonObject), DateUtilRisk.addSeconds(new Date(), 5).getTime());
        LoggerUtils.info(log,"顺风车orderHandler,给外部推送消息:{}", JSON.toJSONString(jsonObject));
    }
}