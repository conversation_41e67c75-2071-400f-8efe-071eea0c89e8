package com.ly.car.risk.process.service.rule.hcGroup;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class HcRuleServiceContextUtil {

    public static Map<String, List<String>> serviceNameMap;

    static {
        Map<String,List<String>> map = new HashMap<>();

        List<String> SELF_HITCH_DRIVER_REGISTER = new ArrayList<>();
        SELF_HITCH_DRIVER_REGISTER.add("hcDriverRegisterService");
        map.put("7-1",SELF_HITCH_DRIVER_REGISTER);
        //司机接单
        List<String> SELF_HITCH_CERTIFICATION = new ArrayList<>();
//        SELF_HITCH_CERTIFICATION.add("hcDriverBlackService");
        SELF_HITCH_CERTIFICATION.add("hcDriverIdCardService");
        SELF_HITCH_CERTIFICATION.add("hcDriverScoreService");
        SELF_HITCH_CERTIFICATION.add("hcDriverHistoryService");
        SELF_HITCH_CERTIFICATION.add("hcDriverLicenseService");
        SELF_HITCH_CERTIFICATION.add("hcDriverCarService");
        map.put("7-2",SELF_HITCH_CERTIFICATION);

        List<String> SELF_HITCH_DRIVER_LOGIN = new ArrayList<>();
        SELF_HITCH_DRIVER_LOGIN.add("hcDriverLoginService");
        map.put("7-3",SELF_HITCH_DRIVER_LOGIN);

        List<String> SELF_HITCH_DRIVER_RELEASE = new ArrayList<>();
        SELF_HITCH_DRIVER_RELEASE.add("hcDriverCommonBlackService");
        map.put("7-4",SELF_HITCH_DRIVER_RELEASE);

        List<String> SELF_HITCH_DRIVER_RECEIVE = new ArrayList<>();
        SELF_HITCH_DRIVER_RECEIVE.add("hcDriverCommonBlackService");
        map.put("7-5",SELF_HITCH_DRIVER_RECEIVE);

        List<String> SELF_HITCH_DRIVER_CASH = new ArrayList<>();
        SELF_HITCH_DRIVER_CASH.add("hcDriverCommonBlackService");
        map.put("7-7",SELF_HITCH_DRIVER_CASH);

        List<String> SELF_HITCH_DRIVER_INVITE = new ArrayList<>();
        SELF_HITCH_DRIVER_INVITE.add("hcDriverCommonBlackService");
        map.put("7-8",SELF_HITCH_DRIVER_INVITE);

        List<String> SELF_HITCH_DRIVER_RETURN_CASH = new ArrayList<>();
        SELF_HITCH_DRIVER_RETURN_CASH.add("hcDriverCommonBlackService");
        SELF_HITCH_DRIVER_RETURN_CASH.add("hcDriverReturnCashService");
        map.put("7-9",SELF_HITCH_DRIVER_RETURN_CASH);

        List<String> SELF_HITCH_BAN_BANKCARD = new ArrayList<>();
        SELF_HITCH_BAN_BANKCARD.add("hcBankCardService");
        map.put("7-11",SELF_HITCH_BAN_BANKCARD);

        List<String> SELF_HITCH_FIRST_ORDER = new ArrayList<>();
        SELF_HITCH_FIRST_ORDER.add("hcDriverReturnCashService");
        map.put("7-12",SELF_HITCH_FIRST_ORDER);
        serviceNameMap = map;
    }
}
