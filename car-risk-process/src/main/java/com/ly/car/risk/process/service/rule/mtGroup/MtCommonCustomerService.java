package com.ly.car.risk.process.service.rule.mtGroup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.HcCustomerMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcCustomer;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class MtCommonCustomerService extends MtFilterHandler{

    @Resource
    private HcCustomerMapper hcCustomerMapper;
    @Resource(name = "mtRiskProducer")
    private MqRiskProducer mqRiskProducer;
    @Resource
    private MqSendConvertService mqSendConvertService;

    @Override
    public void doHandler(MtFilterContext context) {
        if(StringUtils.isNotBlank(context.getParam().getString("userId"))){
            context.getParam().put("driverId",context.getParam().getString("userId"));
        }
        if(StringUtils.isBlank(context.getParam().getString("plate"))
                && StringUtils.isBlank(context.getParam().getString("driverId"))
                && StringUtils.isBlank(context.getParam().getString("mobile"))
                && StringUtils.isBlank(context.getParam().getString("toUserId"))
        ){
            //直接过
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
            log.info("[][][][]当前无名单需要过滤，直接走规则");
            return;
        }
        List<HcCustomer> hcCustomers = hcCustomerMapper.selectList(new QueryWrapper<HcCustomer>()
                .eq("line_type",0).gt("invalid_time",new Date()).and(data->data
                        .or().eq(StringUtils.isNotBlank(context.getParam().getString("plate")),"driver_card_no", context.getParam().get("plate"))
                        .or().eq(StringUtils.isNotBlank(context.getParam().getString("driverId")),"driver_id",context.getParam().getString("driverId"))
                        .or().eq(StringUtils.isNotBlank(context.getParam().getString("mobile")),"driver_phone",context.getParam().getString("mobile"))
//                        .or().eq(StringUtils.isNotBlank(context.getParam().getString("toPlate")),"driver_card_no",context.getParam().getString("toPlate"))
                        .or().eq(StringUtils.isNotBlank(context.getParam().getString("toUserId")),"driver_id",context.getParam().getString("toDriverId"))
//                        .or().eq(StringUtils.isNotBlank(context.getParam().getString("toMobile")),"driver_phone",context.getParam().getString("toMobile"))
                        .or().eq(StringUtils.isNotBlank(context.getParam().getString("idCard")),"id_card_no",context.getParam().getString("idCard"))
                )
        );
        log.info("[][][][]当前名单查询结果{}",JsonUtils.json(hcCustomers));
        //查询是否有白名单
        if(CollectionUtils.isNotEmpty(hcCustomers)){
            List<HcCustomer> collect = hcCustomers.stream().filter(data -> data.getCustomerType() == 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                context.getDto().setMessage("当前司机存在白名单");
                if(context.getParam().getInteger("mainScene") != null
                        && context.getParam().getInteger("mainScene") == 11
                        && context.getParam().getInteger("childScene") == 2){
                    RiskResultNewDTO dto = new RiskResultNewDTO();
                    Map<String,Object> data = new HashMap<>();
                    data.put("driverId",context.getParam().getString("driverId"));
                    data.put("idCard",context.getParam().getString("idCard"));
                    dto.setObj(data);
//                    mqRiskProducer.send(MqTagEnum.car_risk_mt_notify, JsonUtils.json(dto),0L);
                    mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"),JsonUtils.json(dto));
                }
                return;
            }
            List<HcCustomer> blackCollect = hcCustomers.stream().filter(data->data.getCustomerType()==1).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(blackCollect)){
                context.getDto().setCode(1);
                context.getDto().setMessage("当前司机存在黑名单");
                if(context.getParam().getInteger("mainScene") != null
                        && context.getParam().getInteger("mainScene") == 11
                        && context.getParam().getInteger("childScene") == 2){
                    RiskResultNewDTO dto = new RiskResultNewDTO();
                    Map<String,Object> data = new HashMap<>();
                    data.put("driverId",context.getParam().getString("driverId"));
                    data.put("idCard",context.getParam().getString("idCard"));
                    dto.setObj(data);
                    dto.setCode(1);
                    dto.setMessage("存在黑名单不通过");
//                    mqRiskProducer.send(MqTagEnum.car_risk_mt_notify, JsonUtils.json(dto),0L);
                    mqSendConvertService.sendNotifyMq(context.getParam().getString("productLine"),JsonUtils.json(dto));
                }
                return;
            }
            if(CollectionUtils.isNotEmpty(context.getWhiteType())){
                //局部白
                List<HcCustomer> partWhiteCustomers = hcCustomers.stream().filter(data->context.getWhiteType().contains(data.getCustomerType()))
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(partWhiteCustomers)){
                    context.getDto().setMessage("当前司机存在局部白名单");
                    return;
                }
            }
            if(CollectionUtils.isNotEmpty(context.getBlackType())){
                //局部黑
                List<HcCustomer> partBlackCustomers = hcCustomers.stream().filter(data->context.getBlackType().contains(data.getCustomerType()))
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(partBlackCustomers)){
                    context.getDto().setCode(1);
                    context.getDto().setMessage("当前司机存在局部黑名单");
                    return;
                }
            }
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }
    }
}
