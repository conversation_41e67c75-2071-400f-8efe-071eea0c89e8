package com.ly.car.risk.process.turboMQ.listen.newCar;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
import com.ly.car.risk.process.bean.properties.UrlsProperties;
import com.ly.car.risk.process.turboMQ.consumer.newCar.VirtualCallSyncConsumer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class VirtualCallSyncListen implements ApplicationListener<ApplicationStartedEvent>, DisposableBean {

    @Resource
    private UrlsProperties urlsProperties;
    @Value("${config.turbo-mq.virtualCallSyncSwitch}")
    private String virtualCallSyncSwitch;

    @Value("${config.turbo-mq.virtualCallSyncTopic}")
    private String TOPIC;

    @Value("${config.turbo-mq.virtualCallConsumerGroup}")
    private String GROUP;
    
    private DefaultMQPushConsumer consumer;
    
    @SneakyThrows
    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        if(!Boolean.parseBoolean(virtualCallSyncSwitch)){
            return;
        }
        consumer = new DefaultMQPushConsumer(GROUP);
        consumer.setNamesrvAddr(urlsProperties.getPublicMqServer());
        //每次拉去N条消息
        consumer.setConsumeMessageBatchMaxSize(1024);
        //设置消费模式=集群
        consumer.setMessageModel(MessageModel.CLUSTERING);
        //订阅PushTopic下tag为push的消息
        consumer.subscribe(TOPIC,"");
        consumer.registerMessageListener(new VirtualCallSyncConsumer());
        consumer.start();
        log.info("[MqStartupRunner][initTurboConsumer][VirtualCallSyncListen][]启动tuborMQ消费者-虚拟通话同步消费");
    }
    
    @Override
    public void destroy() {
        if(null != consumer){
            consumer.shutdown();
        }
    }
}
