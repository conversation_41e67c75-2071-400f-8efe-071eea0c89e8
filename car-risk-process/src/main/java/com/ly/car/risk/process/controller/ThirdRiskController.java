package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.api.CommonRiskClient;
import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.constants.CommonMap;
import com.ly.car.risk.process.controller.dto.CommonReturn;
import com.ly.car.risk.process.controller.params.HitchRiskParam;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverCheck;
import com.ly.car.risk.process.service.DriverCheckService;
import com.ly.car.risk.process.service.rule.mtGroup.MtDriverScoreService;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequestMapping("/thirdRisk")
@RestController
@Slf4j
public class ThirdRiskController {

    @Resource
    private TianChuangRiskClient tianChuangRiskClient;
    @Resource
    private DriverCheckService driverCheckService;
    @Resource
    private CommonRiskClient commonRiskClient;
    @Resource
    private MtDriverScoreService mtDriverScoreService;

    //0-身份证 1-风险评分 2-驾驶证 3-行驶证 4-银行卡
    @RequestMapping("/verifyIdCard")
    public UiResult verifyIdCard(@RequestBody HitchRiskParam param){
        log.info("[][verifyIdCard][][]独立接口调用实名{}",JsonUtils.json(param));
        TianChuangCommonParam commonParam = new TianChuangCommonParam();
        BeanUtils.copyProperties(param,commonParam);
        UiResult uiResult = UiResult.ok();
        StringBuilder msg = new StringBuilder();
        Integer result = this.tianChuangRiskClient.verifyIdCard(commonParam, msg);
        driverCheckService.insert(JSONObject.parseObject(JsonUtils.json(param)),0,result,msg.toString());
        uiResult.setData(new CommonReturn(result,msg.toString()));
        log.info("[][][][]身份验证返回{}",JsonUtils.json(uiResult));
        return uiResult;
    }

    @RequestMapping("/verifyBankCard")
    public UiResult verifyBankCard(@RequestBody HitchRiskParam param){
        log.info("[][verifyBankCard][][]独立接口调用银行卡{}",JsonUtils.json(param));
        TianChuangCommonParam commonParam = new TianChuangCommonParam();
        BeanUtils.copyProperties(param,commonParam);
        UiResult uiResult = UiResult.ok();
        uiResult.setData(this.tianChuangRiskClient.verifyBankCard(commonParam));
        return uiResult;
    }

    @RequestMapping("/verifyMobileInfo")
    public UiResult verifyMobileInfo(@RequestBody HitchRiskParam param){
        TianChuangCommonParam commonParam = new TianChuangCommonParam();
        BeanUtils.copyProperties(param,commonParam);
        UiResult uiResult = UiResult.ok();
        StringBuilder msg = new StringBuilder();
        Integer result = this.tianChuangRiskClient.verifyMobileInfo(commonParam);
        uiResult.setData(new CommonReturn(result, msg.toString()));
        return uiResult;
    }

    @RequestMapping("/verifyScoreInfo")
    public UiResult verifyScoreInfo(@RequestBody HitchRiskParam param){
        log.info("[][verifyScoreInfo][][]独立接口调用背审{}",JsonUtils.json(param));
        //查一下近180天有没有验证过
        UiResult uiResult = UiResult.ok();
        StringBuilder msg = new StringBuilder();
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("name", param.getName());
        paramMap.put("idNum", param.getIdCard());
        paramMap.put("productLine", param.getProductLine());

//        List<DriverCheck> driverCheckList = driverCheckService.queryResultAgain(1, param.getName(), param.getIdCard());
//        if(CollectionUtils.isNotEmpty(driverCheckList)){
//            List<DriverCheck> filterDriverCheckList = driverCheckList.stream().filter(data->data.getResult()==1).collect(Collectors.toList());
//            if(filterDriverCheckList != null && filterDriverCheckList.size() > 0){
//                uiResult.setData(new CommonReturn(1,filterDriverCheckList.get(0).getRemark()));
//            } else {
//                uiResult.setData(new CommonReturn(0,"在有效期内，无需验证"));
//            }
//            return uiResult;
//        }

        Integer result = mtDriverScoreService.queryDriverRiskV2(paramMap, msg);
//        Integer result = this.commonRiskClient.queryDriverByBds(paramMap, msg);
        driverCheckService.insert(JSONObject.parseObject(JsonUtils.json(param)),1,result,msg.toString());
        uiResult.setData(new CommonReturn(result,msg.toString()));
        return uiResult;
    }

    /**
     *  行驶证验证
     * */
    @RequestMapping("/verifyCarInfo")
    public UiResult verifyCarInfo(@RequestBody HitchRiskParam param){
        TianChuangCommonParam commonParam = new TianChuangCommonParam();
        if(param.getPlate().length() == 8){
            param.setPlateType("52");
        } else {
            param.setPlateType(param.getPlateType());
        }
        BeanUtils.copyProperties(param,commonParam);
        UiResult uiResult = UiResult.ok();
        StringBuilder msg = new StringBuilder();
        Integer result = this.tianChuangRiskClient.verifyCarInfo(commonParam, msg);
        driverCheckService.insert(JSONObject.parseObject(JsonUtils.json(param)),3,result,msg.toString());
        uiResult.setData(new CommonReturn(result,msg.toString()));
        return uiResult;
    }

    @RequestMapping("/verifyDriverLicenseInfo")
    public UiResult verifyDriverLicenseInfo(@RequestBody HitchRiskParam param){
        TianChuangCommonParam commonParam = new TianChuangCommonParam();
        BeanUtils.copyProperties(param,commonParam);
        UiResult uiResult = UiResult.ok();
//        this.tianChuangRiskClient.verifyDriverLicenseInfo(commonParam,null)
        uiResult.setData(new CommonReturn(0,""));
        return uiResult;
    }

    @RequestMapping("/queryPlateType")
    public UiResult queryPlate(){
        Map<String, String> carMap = CommonMap.tianChuangCarMap;
        UiResult uiResult = UiResult.ok();
        uiResult.setData(carMap);
        return uiResult;
    }

}
