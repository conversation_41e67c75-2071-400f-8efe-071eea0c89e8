package com.ly.car.risk.process.strategy.blacklist;

import com.ly.car.risk.process.constants.*;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class BlackListHandler implements BlackListCheck {

    @Resource
    private MTBlackListHandler mtBlackListHandler;

    @Resource
    private CommonBlackListHandler commonBlackListHandler;

    @Override
    public RiskSceneResult blackListCheck(UnifyCheckRequest request) {

        beforeBlackWhiteCheck(request);
        StrategySceneEnum scene = StrategySceneEnum.of(request.getScene());

        // 黑白名单，MT的有特殊性
        if (Objects.equals(request.getProductLine(), ProductLineEnum.MT.getCode())) {
            return mtBlackListHandler.blackListCheck(request);
        } else if (scene == StrategySceneEnum.CANCEL_REMINDER || scene == StrategySceneEnum.FINISH_ORDER) {
            // 取消-安全提示 不校验
            return null;
        } else {
            return commonBlackListHandler.blackListCheck(request);
        }
    }
    private void beforeBlackWhiteCheck(UnifyCheckRequest request) {
        if (request.isDistributionFlag()) {
            // 分销单的mid是批量虚拟账号，不校验mid相关，防止误伤
            request.setMemberId(StringUtils.EMPTY);
        }
    }
}
