package com.ly.car.risk.process.constants;

import java.util.HashMap;
import java.util.Map;

public class CustomerConstants {

    public static Map<String, String> customerMap;

    static {
        Map<String, String> map = new HashMap<>();
        map.put("ruleRisk1001Service","1");
        map.put("ruleRisk1003Service","1");
        map.put("ruleRisk2001Service","1");
        map.put("customerUserService","1");
        map.put("hcDriverBlackService","1");
        customerMap = map;
    }
}
