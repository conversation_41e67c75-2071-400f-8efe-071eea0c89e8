package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.common.bean.model.UiResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("badAction")
public class DriverBadActionController {

    @RequestMapping("collect")
    public UiResult actionCollect(@RequestBody JSONObject jsonObject){

        return UiResult.ok();
    }
}
