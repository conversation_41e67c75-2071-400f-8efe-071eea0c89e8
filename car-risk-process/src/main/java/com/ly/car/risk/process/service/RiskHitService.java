package com.ly.car.risk.process.service;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Sets;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.order.entity.OrderCoupon;
import com.ly.car.order.entity.OrderWxCard;
import com.ly.car.order.entity.SfcCouponRecord;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.api.rsp.MemberQueryResponse;
import com.ly.car.risk.process.constants.*;
import com.ly.sof.utils.common.DateUtil;
import com.ly.car.risk.process.controller.params.FilterParams;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.repo.order.mapper.OrderCouponMapper;
import com.ly.car.risk.process.repo.order.mapper.OrderWxCardMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcCouponRecordMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskHitMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.MetricScene;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskHit;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.RiskHitDTO;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.dto.order.UsedDiscountInfo;
import com.ly.car.risk.process.service.groovy.HitStrategyDTO;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.ly.car.risk.process.utils.LoggerUtils;

@Service
@Slf4j
public class RiskHitService {

    // 特定场景：需要按freezeTime排序的场景
    public static final Set<String> FREEZE_TIME_PRIORITY_SCENES = Sets.newHashSet(
            StrategySceneEnum.DRIVER_AUTO_FINISH_ORDER.getScene(),
            StrategySceneEnum.CAR_OWNER_REVIEW.getScene(),
            StrategySceneEnum.CAR_OWNER_WITHDRAW.getScene(),
            StrategySceneEnum.CAR_OWNER_UNFREEZE.getScene());

    @Resource
    private RiskHitMapper riskHitMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private OrderWxCardMapper orderWxCardMapper;
    @Resource
    private OrderCouponMapper orderCouponMapper;
    @Resource
    private SfcCouponRecordMapper sfcCouponRecordMapper;

    @Resource(name = "executorService")
    private ExecutorService executorService;
    @Resource
    private MemberService memberService;
    @Resource
    private RiskHitLinkService riskHitLinkService;
    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private RiskOrderManageService riskOrderManageService;
    @Resource
    private CarOrderService   carOrderService;
    @Resource
    private MetricSceneService metricSceneService;


    public void saveRiskHit(RiskHitDTO dto){
        RiskHit riskHit = new RiskHit();
        riskHit.setOrderId(dto.getOrderId());
        riskHit.setMainScene(dto.getMainScene());
        riskHit.setChildScene(dto.getChildScene());
        riskHit.setMainSceneNo("");
        riskHit.setChildSceneNo("");
        riskHit.setMemberId(dto.getMemberId());
        riskHit.setReqParam(dto.getRequestParam());
        riskHit.setRiskLevel(5);//需要查询规则计算 todo
        riskHit.setHitType(dto.getHitType());
        riskHit.setHitRule(dto.getRuleNo());
        riskHit.setCustomerValue(dto.getCustomerValue());
        riskHit.setIsCheating(0);
        riskHit.setProductLine(dto.getProductLine());
        riskHit.setMainSceneName(MainSceneEnum.getMsgByCode(dto.getMainScene()));
        riskHit.setChildSceneName(ChildSceneEnum.getMsgByCode(dto.getMainScene()+"-"+dto.getChildScene()));
        riskHit.setCreateTime(new Date());
        riskHit.setUpdateTime(new Date());
        riskHit.setRequestId(dto.getRequestId());
        riskHit.setResResult(dto.getResResult());
        riskHit.setDriverCardNo(dto.getDriverCardNo());
        riskHit.setUnionId(dto.getUnionId());
        riskHit.setHitStrategy(dto.getStrategyNo());
        riskHit.setHitField(StringUtils.EMPTY);
        riskHit.setControlTarget(StringUtils.EMPTY);
        riskHit.setDisposeAction(StringUtils.EMPTY);
        if(StringUtils.isBlank(dto.getOrderId())){
            riskHitMapper.insert(riskHit);
            return;
        }
        if (OrderUtils.isNewOrder(dto.getOrderId())) {

            CarOrderDetail order = carOrderService.queryOrderDetail(dto.getOrderId());

            if (null != order) {

                riskHit.setTotalAmount(new BigDecimal(order.getBaseInfo().getAmount()));
                riskHit.setPassengerCellphone(order.getPassengerInfo().getPassengerCellPhone());
                riskHit.setMemberId(order.getMemberId());
                riskHit.setCityId(order.getOrderTrip().getDepartureCityCode());
                riskHit.setCityName(order.getOrderTrip().getDepartureCityName());
                riskHit.setStartAddress(order.getOrderTrip().getDepartureAddress());
                riskHit.setEndAddress(order.getOrderTrip().getArrivalAddress());


                if (CollectionUtils.isNotEmpty(order.getUsedCoupons())) {

                    List<UsedDiscountInfo> coupons = order.getUsedCoupons()
                            .stream()
                            .filter(c -> Objects.equals(c.getBusinessType(), OrderItemBizTypeEnum.COUPON.getCode()))
                            .collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(coupons)) {
                        riskHit.setCouponNo(coupons.stream().map(UsedDiscountInfo::getProductName).collect(Collectors.joining(",")));
                        riskHit.setOrderAmount(coupons.stream().filter(p->StringUtils.isNotBlank(p.getRealPrice()))
                                .map(UsedDiscountInfo::getRealPrice).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                }

                riskHit.setEnv(Objects.equals(StringUtils.upperCase(order.getEnv()), "PROD") ? "PROD" : "STAGE");
            }
        } else {
            if (dto.getOrderId().startsWith("YNC") || dto.getOrderId().startsWith("GNC")) {
                OrderInfo orderInfo = orderInfoMapper.findByOrderId(dto.getOrderId());
                if (orderInfo != null) {
                    riskHit.setTotalAmount(orderInfo.getTotalAmount());
                    riskHit.setPassengerCellphone(orderInfo.getPassengerCellphone());
                    if (StringUtils.isBlank(dto.getMemberId())) {
                        riskHit.setMemberId(orderInfo.getMemberId());
                    }
                }
                OrderAddress orderAddress = orderAddressMapper.findByOrderId(dto.getOrderId());
                if (orderAddress != null) {
                    riskHit.setCityId(orderAddress.getStartCityId());
                    riskHit.setCityName(orderAddress.getStartCityName());
                    riskHit.setStartAddress(orderAddress.getStartAddress());
                    riskHit.setEndAddress(orderAddress.getEndAddress());
                }
                //先查微信优惠券
                List<OrderWxCard> orderWxCards = orderWxCardMapper.selectList(
                        new QueryWrapper<OrderWxCard>().eq("order_id", dto.getOrderId())
                );
                if (!CollectionUtils.isEmpty(orderWxCards)) {
                    List<String> couponNameList = orderWxCards.stream().map(OrderWxCard::getTitle).collect(Collectors.toList());
                    //                List<String> couponList = orderWxCards.stream().map(OrderWxCard::getCardId).collect(Collectors.toList());
                    riskHit.setCouponNo(StringUtils.join(couponNameList, ","));
                    riskHit.setOrderAmount(orderWxCards.stream().map(OrderWxCard::getReduceCost).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add));
                } else {
                    //查app优惠券
                    List<OrderCoupon> orderCoupons = orderCouponMapper.selectList(
                            new QueryWrapper<OrderCoupon>().eq("order_id", dto.getOrderId())
                    );
                    List<String> couponNameList = orderCoupons.stream().map(OrderCoupon::getBatchName).collect(Collectors.toList());
                    riskHit.setCouponNo(StringUtils.join(couponNameList, ","));
                    riskHit.setOrderAmount(orderCoupons.stream().map(OrderCoupon::getCouponAmount).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                riskHit.setEnv(orderInfo.getEnv().equals("PROD") ? "PROD" : "STAGE");
            } else {
                SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(dto.getOrderId());
                if (sfcOrder != null) {
                    riskHit.setTotalAmount(new BigDecimal(sfcOrder.getTotalAmount() / 100));
                    riskHit.setEnv(sfcOrder.getEnv().equals("PROD") ? "PROD" : "STAGE");
                    riskHit.setPassengerCellphone(sfcOrder.getPassengerCellphone());
                    if (StringUtils.isBlank(dto.getMemberId())) {
                        riskHit.setMemberId(String.valueOf(sfcOrder.getMemberId()));
                    }
                }
                OrderAddress orderAddress = orderAddressMapper.findByOrderId(dto.getOrderId());
                if (orderAddress != null) {
                    riskHit.setCityId(orderAddress.getStartCityId());
                    riskHit.setCityName(orderAddress.getStartCityName());
                    riskHit.setStartAddress(orderAddress.getStartAddress());
                    riskHit.setEndAddress(orderAddress.getEndAddress());
                }
                List<SfcCouponRecord> sfcCouponRecord = sfcCouponRecordMapper.getListByOrderId(sfcOrder.getOrderId());
                if (!CollectionUtils.isEmpty(sfcCouponRecord)) {
                    List<String> couponNameList = sfcCouponRecord.stream().map(SfcCouponRecord::getTitle).collect(Collectors.toList());
                    riskHit.setCouponNo(StringUtils.join(couponNameList, ","));
                    riskHit.setOrderAmount(sfcCouponRecord.stream().map(SfcCouponRecord::getReduceCost).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
        }
        if(StringUtils.isNotBlank(riskHit.getMemberId())  && !riskHit.getMemberId().equals("0")){
            MemberQueryResponse infoByMemberId = memberService.getInfoByMemberId(String.valueOf(riskHit.getMemberId()));
            if(infoByMemberId != null && infoByMemberId.getData() != null){
                riskHit.setUserPhone(infoByMemberId.getData().getMobile());
            }
        }
        this.riskHitMapper.insert(riskHit);

        //这个时候也要判断下命中的规则进行拉黑
        if(StringUtils.isNotBlank(riskHit.getHitRule()) && (riskHit.getHitRule().contains("KH_003") || riskHit.getHitRule().contains("KH_004"))){
            riskCustomerService.addRiskCustomer(riskHit.getMemberId(),1, 1, 30);
        }

    }
    
    public void initHitRisk(UnifyCheckRequest request, RiskSceneResult result) {
        executorService.execute(() -> {
            
            RiskHit riskHit = new RiskHit();
            riskHit.setOrderId(StringUtils.defaultString(request.getOrderId()));
            if (StringUtils.isNotBlank(request.getScene()) && request.getScene().contains("-")) {
                String[] split = request.getScene().split("-");
                riskHit.setMainScene(Integer.parseInt(split[0]));
                riskHit.setChildScene(Integer.parseInt(split[1]));
            }
            riskHit.setMainSceneNo("");
            riskHit.setChildSceneNo("");
            riskHit.setMemberId(request.getMemberId());
            riskHit.setReqParam(JSON.toJSONString(request));
            riskHit.setRiskLevel(5);
            riskHit.setHitType(request.getHitType());
            riskHit.setHitRule(StringUtils.defaultString(request.getHitRule()));
            riskHit.setCustomerValue(StringUtils.defaultString(request.getCustomerValue()));
            riskHit.setCustomerType(request.getCustomerType() == null ? 0 : request.getCustomerType());
            riskHit.setIsCheating(0);
            riskHit.setProductLine(StringUtils.defaultString(request.getProductLine()));
            Pair<MetricScene, MetricScene> pair = metricSceneService.getByNo(String.valueOf(riskHit.getMainScene()), String.valueOf(riskHit.getChildScene()));
            if (null != pair && null != pair.getKey() && null != pair.getValue()) {
                riskHit.setMainSceneName(pair.getKey().getName());
                riskHit.setChildSceneName(pair.getValue().getName());
            } else {
                riskHit.setMainSceneName("");
                riskHit.setChildSceneName("");
            }
            riskHit.setCreateTime(new Date());
            riskHit.setUpdateTime(new Date());
            riskHit.setRequestId(StringUtils.defaultString(request.getTraceId()));
            riskHit.setResResult(JSON.toJSONString(result));
            riskHit.setDriverCardNo(StringUtils.defaultString(request.getCarNum()));
            riskHit.setUnionId(StringUtils.defaultString(request.getUnionId()));
            riskHit.setHitStrategy(StringUtils.defaultString(request.getStrategyNos()));
            riskHit.setHitField(StringUtils.defaultString(request.getHitField()));
            riskHit.setControlTarget(StringUtils.defaultString(request.getControlTarget()));
            riskHit.setUserPhone(StringUtils.defaultString(request.getPassengerPhone(),StringUtils.defaultString(request.getUserPhone())));
            riskHit.setDisposeAction(StringUtils.defaultString(request.getDisposeAction()));

            // 在特定场景下设置freezeEndTime - 在写入时就进行历史记录比较
            if (FREEZE_TIME_PRIORITY_SCENES.contains(request.getScene()) && StringUtils.isNotBlank(result.getFreezeTime())) {
                try {
                    Date currentFreezeTime = DateUtil.parseDateNewFormat(result.getFreezeTime());
                    Date finalFreezeEndTime = currentFreezeTime;

                    // 查询历史记录并比较
                    if (StringUtils.isNotBlank(request.getOrderId())) {
                        Date historyFreezeEndTime = queryLatestFreezeEndTimeByOrderId(request.getOrderId());
                        if (historyFreezeEndTime != null && historyFreezeEndTime.after(currentFreezeTime)) {
                            finalFreezeEndTime = historyFreezeEndTime;
                            // 同时更新result中的freezeTime，确保返回值一致
                            result.setFreezeTime(DateUtil.getNewFormatDateString(historyFreezeEndTime));
                            LoggerUtils.info(log, "场景{}使用历史记录的freezeTime: {}", request.getScene(),
                                    DateUtil.getNewFormatDateString(historyFreezeEndTime));
                        }
                    }

                    riskHit.setFreezeEndTime(finalFreezeEndTime);
                } catch (Exception e) {
                    log.warn("处理freezeTime失败: {}", result.getFreezeTime(), e);
                }
            }

            if (StringUtils.isBlank(request.getOrderId())) {
                riskHitMapper.insert(riskHit);
                return;
            }
            if (OrderUtils.isNewOrder(request.getOrderId())) {
                
                CarOrderDetail order = carOrderService.queryOrderDetail(request.getOrderId());
                
                if (null != order) {
                    
                    riskHit.setTotalAmount(new BigDecimal(order.getBaseInfo().getAmount()));
                    riskHit.setPassengerCellphone(order.getPassengerInfo().getPassengerCellPhone());
                    riskHit.setMemberId(order.getMemberId());
                    riskHit.setCityId(order.getOrderTrip().getDepartureCityCode());
                    riskHit.setCityName(order.getOrderTrip().getDepartureCityName());
                    riskHit.setStartAddress(order.getOrderTrip().getDepartureAddress());
                    riskHit.setEndAddress(order.getOrderTrip().getArrivalAddress());
                    
                    if (CollectionUtils.isNotEmpty(order.getUsedCoupons())) {
                        
                        List<UsedDiscountInfo> coupons = order.getUsedCoupons()
                                .stream()
                                .filter(c -> Objects.equals(c.getBusinessType(), OrderItemBizTypeEnum.COUPON.getCode()))
                                .collect(Collectors.toList());
                        
                        if (CollectionUtils.isNotEmpty(coupons)) {
                            riskHit.setCouponNo(coupons.stream().map(UsedDiscountInfo::getProductName).collect(Collectors.joining(",")));
                            riskHit.setOrderAmount(coupons.stream().filter(p -> StringUtils.isNotBlank(p.getRealPrice()))
                                    .map(UsedDiscountInfo::getRealPrice).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add));
                        }
                    }
                    
                    riskHit.setEnv(Objects.equals(StringUtils.upperCase(order.getEnv()), "PROD") ? "PROD" : "STAGE");
                }
            }
            this.riskHitMapper.insert(riskHit);
        });
    }
    

    /**
     * 实时规则使用
     * */
    public void initHitRisk(FilterParams params, HitInfoDTO hitInfoDTO,String strategyNos){
        executorService.execute(()->{
            RiskHitDTO dto = new RiskHitDTO();
            dto.setOrderId(params.getOrderId());
            dto.setMainScene(params.getMainScene());
            dto.setChildScene(params.getChildScene());
            dto.setMemberId(params.getMemberId());
            dto.setDriverCardNo(params.getDriverCardNo());
            dto.setRequestParam(convertRequestParam(params));
            dto.setHitType(hitInfoDTO.getHitType());
            dto.setRuleNo(hitInfoDTO.getRuleNo());
            dto.setCustomerValue(hitInfoDTO.getCustomerValue());
            dto.setProductLine(params.getProductLine());
            dto.setRequestId(params.getRequestId());
            dto.setResResult(JsonUtils.json(hitInfoDTO.getResResult()));
            dto.setRiskLevel(hitInfoDTO.getRiskLevel());
            dto.setUnionId(params.getUnionId());
            dto.setStrategyNo(strategyNos);

            /**
             * 线程挂起一秒，这个先看看延迟情况是否是导致数据库同步不及时的情况
             * */
            if(StringUtils.isNotBlank(params.getOrderId())
                    && params.getMainScene() == 2 && params.getChildScene() == 2){
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            saveRiskHit(dto);
        });
    }

    /**
     * 实时规则使用
     * */
    public void initHitRisk(FilterParams params, HitInfoDTO hitInfoDTO){
        executorService.execute(()->{
            RiskHitDTO dto = new RiskHitDTO();
            dto.setOrderId(params.getOrderId());
            dto.setMainScene(params.getMainScene());
            dto.setChildScene(params.getChildScene());
            dto.setMemberId(params.getMemberId());
            dto.setDriverCardNo(params.getDriverCardNo());
            dto.setRequestParam(convertRequestParam(params));
            dto.setHitType(hitInfoDTO.getHitType());
            dto.setRuleNo(hitInfoDTO.getRuleNo());
            dto.setCustomerValue(hitInfoDTO.getCustomerValue());
            dto.setProductLine(params.getProductLine());
            dto.setRequestId(params.getRequestId());
            dto.setResResult(hitInfoDTO.getResResult()==null?null:JsonUtils.json(hitInfoDTO.getResResult()));
            dto.setRiskLevel(hitInfoDTO.getRiskLevel());
            dto.setUnionId(params.getUnionId());

            /**
             * 线程挂起一秒，这个先看看延迟情况是否是导致数据库同步不及时的情况
             * */
            if(StringUtils.isNotBlank(params.getOrderId())
                    && params.getMainScene() == 2 && params.getChildScene() == 2){
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            saveRiskHit(dto);
        });
    }

    /**
     * 离线规则用
     * */
    public void initHitRiskOff(Map<String,Object> map, HitInfoDTO hitInfoDTO){
        executorService.execute(()->{
            RiskHitDTO dto = new RiskHitDTO();
            dto.setOrderId((String) map.get("orderId"));
            dto.setMainScene((Integer) map.get("mainScene"));
            dto.setChildScene((Integer) map.get("childScene"));
            dto.setMemberId((String) map.get("memberId"));
            dto.setRequestParam("");
            dto.setHitType(hitInfoDTO.getHitType());
            dto.setRuleNo(hitInfoDTO.getRuleNo());
            dto.setCustomerValue(hitInfoDTO.getCustomerValue());
            dto.setProductLine((String) map.get("productLine"));
            dto.setRequestId((String) map.get("requestId"));
            dto.setResResult(JsonUtils.json(hitInfoDTO.getResResult()));
            dto.setRiskLevel(hitInfoDTO.getRiskLevel());
            saveRiskHit(dto);
        });
    }

    public String convertRequestParam(FilterParams params){
        Class aClass = params.getClass();
        JSONObject jsonObject = new JSONObject();
        Field[] fs = aClass.getDeclaredFields();
        for (Field f : fs) {
            // 设置些属性是可以访问的
            f.setAccessible(true);
            try {
                if(f.get(params) != null){
                    jsonObject.put(f.getName(),f.get(params));
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return JsonUtils.json(jsonObject);
    }

    public List<String> saveNewRiskHit(FilterParams params, Map<String, List<HitStrategyDTO>> resultMap,
                               RiskResultDTO resultDTO){
        HitInfoDTO hitInfoDTO;
        List<String> strategyNos = new ArrayList<>();
        if(resultMap.get("customer") != null){
            hitInfoDTO = new HitInfoDTO(
                    RiskCustomerRiskTypeEnum.getMsgByCode(Integer.valueOf(resultMap.get("customer").get(0).getStrategyNo())),
                    5,
                    1,
                    resultMap.get("customer").get(0).getValue(),
                    UiResult.ok(resultDTO)
            );
            strategyNos = resultMap.get("customer").stream().map(HitStrategyDTO::getStrategyNo).collect(Collectors.toList());
        } else {
            //所有的规则编号
            List<HitStrategyDTO> hitStrategyDTOS = resultMap.get("rule");
            List<String> ruleNos = new ArrayList<>();
            for(HitStrategyDTO dto : hitStrategyDTOS){
                ruleNos.addAll(dto.getHitInfoDTO().stream().map(com.ly.car.risk.process.service.groovy.HitInfoDTO::getHit).collect(Collectors.toList()));
            }
            log.info("[][][][]插入关联命中订单信息{}",JsonUtils.json(hitStrategyDTOS));
            List<String> saveRuleNoList = new ArrayList<>();
            for(HitStrategyDTO dto : hitStrategyDTOS){
                for (com.ly.car.risk.process.service.groovy.HitInfoDTO info : dto.getHitInfoDTO()) {
                    if(saveRuleNoList.contains(info.getHit())){
                        continue;
                    }
                    saveRuleNoList.add(info.getHit());
                    this.riskHitLinkService.saveHitLink(new RiskHitLinkDTO(params.getRequestId(), params.getOrderId(), info.getHit(), Arrays.asList(info.getValue().split(","))));
                }
            }

            hitInfoDTO = new HitInfoDTO(
                    StringUtils.join(new HashSet<>(ruleNos),","),
                    5,
                    0,
                    null,
                    UiResult.ok(resultDTO)
            );
            strategyNos = resultMap.get("rule").stream().map(HitStrategyDTO::getStrategyNo).collect(Collectors.toList());
        }
        this.initHitRisk(params,hitInfoDTO,StringUtils.join(strategyNos,","));
        return strategyNos;

    }

    /**
     * 根据订单号查询最新的风控命中记录的freeze_end_time
     * @param orderId 订单号
     * @return freeze_end_time
     */
    public Date queryLatestFreezeEndTimeByOrderId(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return null;
        }
        return riskHitMapper.queryLatestFreezeEndTimeByOrderId(orderId);
    }

    /**
     * 确保freezeTime不早于历史记录
     * 适用于所有场景：策略命中、兜底冻结、司机风控等
     * @param request 请求参数
     * @param freezeTime 当前计算的冻结时间
     * @return 最终的冻结时间（不早于历史记录）
     */
    public String ensureFreezeTimeNotEarlierThanHistory(UnifyCheckRequest request, String freezeTime) {
        // 参数校验
        if (!FREEZE_TIME_PRIORITY_SCENES.contains(request.getScene()) ||
            StringUtils.isBlank(freezeTime) ||
            StringUtils.isBlank(request.getOrderId())) {
            return freezeTime;
        }

        try {
            Date currentFreezeTime = DateUtil.parseDateNewFormat(freezeTime);
            if (currentFreezeTime == null) {
                LoggerUtils.warn(log, "解析freezeTime失败: {}", freezeTime);
                return freezeTime;
            }

            // 查询历史记录
            Date historyFreezeEndTime = queryLatestFreezeEndTimeByOrderId(request.getOrderId());
            if (historyFreezeEndTime != null && historyFreezeEndTime.after(currentFreezeTime)) {
                String finalFreezeTime = DateUtil.getNewFormatDateString(historyFreezeEndTime);
                LoggerUtils.info(log, "场景{}的freezeTime被历史记录覆盖: {} -> {}",
                        request.getScene(), freezeTime, finalFreezeTime);
                return finalFreezeTime;
            }

            return freezeTime;

        } catch (Exception e) {
            LoggerUtils.error(log, "确保freezeTime不早于历史记录时异常, orderId: {}, scene: {}",
                    request.getOrderId(), request.getScene(), e);
            return freezeTime; // 异常时返回原值
        }
    }

}
