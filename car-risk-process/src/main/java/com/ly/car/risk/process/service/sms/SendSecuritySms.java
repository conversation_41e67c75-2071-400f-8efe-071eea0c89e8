package com.ly.car.risk.process.service.sms;

import com.ly.car.risk.process.controller.request.safecenter.SafetySmsRequest;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.travel.pushcore.facade.response.PushResponse;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/14 11:18
 **/
public interface SendSecuritySms {

    public PushResponse sendSecuritySms(SafetySmsRequest request, CarOrderDetail orderDetail);
}
