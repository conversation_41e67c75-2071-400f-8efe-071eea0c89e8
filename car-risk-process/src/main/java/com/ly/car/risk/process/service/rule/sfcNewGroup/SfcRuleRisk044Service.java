package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 一个memberID在24小时内，由同一个司机接单>=3单，且这些订单100%都完单，且
 * 这些订单50%的预估里程< 5公里
 * */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk044Service extends FilterSfcHandler{

    private static final String ruleNo = "044";
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        log.info("[FilterRuleChain][SfcRuleRisk044Service][][]前置判断已通过，进入规则044判断{}",
                JsonUtils.json(context.getUserContextList()));
        try {
            if(!context.getSfcRiskRuleConfig().getOnOff044()){
                if(this.nextHandler != null){
                    this.nextHandler.doHandler(context);
                }
                return;
            }
            List<OrderRiskContext> orderRiskContextList = context.getUserContextList().stream()
                    .filter(data-> DateUtil.string2Date(data.getFinishTime()).after(DateUtil.addHour(new Date(),-context.getSfcRiskRuleConfig().getTime044())))
                    .distinct()
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(orderRiskContextList)){
                int count = 0;
                for(OrderRiskContext order : orderRiskContextList){
                    if(order.getEstimateKilo().compareTo(new BigDecimal(context.getSfcRiskRuleConfig().getKilo044())) < 0){
                        count = count + 1;
                    }
                }
                BigDecimal rate = new BigDecimal(count).divide(new BigDecimal(orderRiskContextList.size()),2,BigDecimal.ROUND_CEILING);
                //以司机维度进行分组
                Map<String,List<OrderRiskContext>> driverGroup = orderRiskContextList.stream().collect(Collectors.groupingBy(OrderRiskContext::getDriverCardNo));

                for(Map.Entry<String,List<OrderRiskContext>> entry : driverGroup.entrySet()){
                    if(entry.getValue().size() >= context.getSfcRiskRuleConfig().getOrderNum044() && rate.compareTo(context.getSfcRiskRuleConfig().getRate044()) > 0){
                        //查看是否优惠金额大于3元
                        List<OrderRiskContext> list = entry.getValue().stream().filter(data->data.getCouponAmount() != null)
                                .filter(data->data.getCouponAmount().compareTo(new BigDecimal("3"))>0).collect(Collectors.toList());
                        List<String> orderIds = entry.getValue().stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList());
                        distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                        log.info("[SfcRuleRisk044Service][doHandler][{}][{}]命中044规则{}",context.getMemberId(),context.getUnionId(),orderIds);
                        if(StringUtils.isBlank(context.getRuleNo())){
                            context.setRuleNo(ruleNo);
                        } else {
                            context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                        }
                        riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                        if(context.getSfcRiskRuleConfig().getSync044() || (list != null && list.size() > 0)){
                            RiskResultDTO dto = new RiskResultDTO(405,"风控不通过044",null,null);
                            context.getUiResult().setData(dto);
                            context.getUiResult().setMsg("风控不通过");
                            break;
                        } else {
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("[SfcRuleRisk044Service][doHandler][{}][{}]报错信息:{}",context.getMemberId(),context.getUnionId(),e);
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
