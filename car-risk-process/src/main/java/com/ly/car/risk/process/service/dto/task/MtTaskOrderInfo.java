package com.ly.car.risk.process.service.dto.task;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Description of MtTaskOrderInfo
 *
 * <AUTHOR>
 * @date 2024/3/16
 * @desc
 */
@Data
public class MtTaskOrderInfo {
    private String orderId;

    private String supplierOrderId;

    private BigDecimal estimateKilo;

    private Integer estimateMinute;

    private BigDecimal startLng;

    private BigDecimal startLat;

    private BigDecimal endLng;

    private BigDecimal endLat;

    private String plateNumber;

    private String memberId;

    private String unionId;

    private BigDecimal totalAmount;

    private Date finishTime;

}