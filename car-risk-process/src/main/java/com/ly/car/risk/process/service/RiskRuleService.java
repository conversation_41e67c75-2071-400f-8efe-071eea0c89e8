package com.ly.car.risk.process.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.RiskRule;
import com.ly.car.risk.process.repo.risk.mapper.RiskRuleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class RiskRuleService {

    @Resource
    private RiskRuleMapper riskRuleMapper;

    public boolean isEnable(String ruleNo){
        RiskRule riskRule = riskRuleMapper.selectOne(
                new QueryWrapper<RiskRule>().eq("rule_no",ruleNo)
        );
        if(riskRule == null){
            return true;
        }
        if(riskRule.getEnableStatus() == 1){
            return true;
        }
        return false;
    }

}
