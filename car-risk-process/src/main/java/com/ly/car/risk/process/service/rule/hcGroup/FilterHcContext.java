package com.ly.car.risk.process.service.rule.hcGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.service.DriverCheckService;
import com.ly.car.risk.process.service.dto.HcSceneConfig;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import lombok.Data;

import java.util.List;

@Data
public class FilterHcContext {

    private Integer mainScene;
    private Integer childScene;
    private String driverId;
    private String bankCard;
    private String name;
    private String idCard;
    private String mobile;
    private String plate;//车牌号
    private String plateType;//车牌类型
    private String carName;//车辆绑定名称
    private String licenseNo;
    private List<RiskCustomerManage> customerManageList;
    private UiResult uiResult = UiResult.ok();
    private String deviceId;
    private HcSceneConfig sceneConfig;
    private List<String> orderIds;
    private DriverCheckService driverCheckService;

    //所有返回信息在dto的obj中
    public FilterHcContext(){
        UiResult result = this.uiResult;
        result.setData(new RiskResultNewDTO());
    }

}
