package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSON;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.api.LabelClient;
import com.ly.car.risk.process.controller.request.AssignDutyRequest;
import com.ly.car.risk.process.controller.request.CashRateQueryRequest;
import com.ly.car.risk.process.controller.request.DriverCancelRequest;
import com.ly.car.risk.process.controller.request.IsRiskUserRequest;
import com.ly.car.risk.process.controller.request.OrderAcceptCheckRequest;
import com.ly.car.risk.process.controller.request.QueryVirtualCallContentRequest;
import com.ly.car.risk.process.controller.request.RiskLevelQueryRequest;
import com.ly.car.risk.process.controller.request.TextCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.model.risk.DutyResultDTO;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderVirtualCallRecord;
import com.ly.car.risk.process.service.core.RiskQueryService;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.order.CarRiskService;
import com.ly.car.risk.process.service.riskMetrics.RiskMetricsService;
import com.ly.car.risk.process.strategy.RiskStrategyHandler;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.support.UiResultWrapper;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.sof.utils.common.UUID;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 对外风控核验
 *
 * <AUTHOR>
 * @since 2024/3/5 16:09
 **/
@RestController
@RequestMapping("riskCheck")
@Slf4j
public class RiskCheckController {

    @Resource
    private RiskQueryService riskQueryService;
    @Resource
    private LabelClient labelClient;
    @Resource
    private CarOrderService carOrderService;
    @Resource
    private CarRiskService carRiskService;

    @Resource
    private RiskStrategyHandler riskStrategyHandler;

    @Resource
    private RiskMetricsService riskMetricsService;

    /**
     * 查询预付押金倍率
     */
    @RequestMapping("queryCashRate")
    public UiResultWrapper queryCashRate(@RequestBody CashRateQueryRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getMemberId());
        LoggerUtils.info(log, "查询用户预付金倍率，req:{}", JSON.toJSONString(request));
        try {
            UiResult uiResult = carRiskService.queryCashRate(request);
            LoggerUtils.info(log, "查询用户预付金倍率,resp:{}", JSON.toJSONString(uiResult));
            return UiResultWrapper.convert(uiResult);
        } catch (BizException e) {
            LoggerUtils.warn(log, "查询用户预付金倍率,业务异常", e);
            return UiResultWrapper.fail(-1,e.getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "查询用户预付金倍率,未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }

    /**
     * 查询用户风控等级
     */
    @RequestMapping("queryRiskLevel")
    public UiResultWrapper queryRiskLevel(@RequestBody RiskLevelQueryRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getMemberId());
        LoggerUtils.info(log, "查询用户风控等级，req:{}", JSON.toJSONString(request));
        try {
            UiResult uiResult = carRiskService.queryRiskLevel(request);
            LoggerUtils.info(log, "查询用户风控等级,resp:{}", JSON.toJSONString(uiResult));
            return UiResultWrapper.convert(uiResult);
        } catch (BizException e) {
            LoggerUtils.warn(log, "查询用户风控等级,业务异常", e);
            return UiResultWrapper.fail(-1,e.getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "查询用户风控等级,未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }

    }

    /**
     * 下单时判定是否风控用户
     * 场景2-3 网约车下单
     * 场景2-2 顺风车预订
     */
    @RequestMapping("isRiskUser")
    public UiResultWrapper isRiskUser(@RequestBody IsRiskUserRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getMemberId());
        LoggerUtils.info(log, "下单判定风控用户，req:{}", JSON.toJSONString(request));
        try {
            UiResult<RiskResultDTO> uiResult = carRiskService.checkIsRiskUser(request);
            uiResult.getData().setRiskFlag(uiResult.getData().getCode() != 0 ? 1 : 0);
            LoggerUtils.info(log, "下单判定风控用户,resp:{}", JSON.toJSONString(uiResult));
            return UiResultWrapper.convert(uiResult);
        } catch (BizException e) {
            LoggerUtils.warn(log, "下单判定风控用户,业务异常", e);
            return UiResultWrapper.fail(-1,e.getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "下单判定风控用户,未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }

    /**
     * 用户判责
     */
    @RequestMapping("assignDuty")
    public UiResultWrapper assignDuty(@RequestBody AssignDutyRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getOrderId());
        LoggerUtils.info(log, "用户取消判责 req:{}", JSON.toJSONString(request));
        try {
            DutyResultDTO dutyResult = carRiskService.assignDuty(request);
            LoggerUtils.info(log, "用户取消判责 resp:{}", JSON.toJSONString(dutyResult));
            return UiResultWrapper.ok(dutyResult);
        } catch (BizException e) {
            LoggerUtils.warn(log, "用户取消判责，业务异常", e);
            return UiResultWrapper.fail(-1,e.getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "用户取消判责，未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }

    }

    /**
     * 司机接单判定风控用户
     * 场景2-3 网约车下单
     * 场景2-2 顺风车预订
     */
    @RequestMapping("orderAcceptCheck")
    public UiResultWrapper orderAcceptCheck(@RequestBody OrderAcceptCheckRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getOrderId());
        LoggerUtils.info(log, "司机接单判定风控用户 req:{}", JSON.toJSONString(request));
        try {
            UiResult<RiskResultDTO> result = carRiskService.orderAcceptCheck(request);
            result.getData().setRiskFlag(result.getData().getCode() != 0 ? 1 : 0);
            LoggerUtils.info(log, "司机接单判定风控用户 resp:{}", JSON.toJSONString(result));
            return UiResultWrapper.convert(result);
        } catch (BizException e) {
            LoggerUtils.warn(log, "司机接单判定风控用户，业务异常", e);
            return UiResultWrapper.fail(-1,e.getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "司机接单判定风控用户失败，未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }

    /**
     * 查询订单虚拟通话内容
     */
    @RequestMapping("queryVirtualCallContent")
    public UiResultWrapper queryVirtualCallContent(@RequestBody QueryVirtualCallContentRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getOrderId());
        LoggerUtils.info(log, "查询订单虚拟通话内容 req:{}", JSON.toJSONString(request));
        try {
            List<RiskOrderVirtualCallRecord> records = carOrderService.queryOrderVirtualRecords(request.getOrderId());
            LoggerUtils.info(log, "查询订单虚拟通话内容 resp:{}", JSON.toJSONString(records));
            return UiResultWrapper.ok(records);
        } catch (Exception e) {
            LoggerUtils.error(log, "查询订单虚拟通话内容，未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }

    }

    /**
     * 司机取消
     */
    @RequestMapping("driverCancel")
    public UiResultWrapper driverCancel(@RequestBody DriverCancelRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getOrderId());
        LoggerUtils.info(log, "司机取消 req:{}", JSON.toJSONString(request));
        try {
            carRiskService.driverCancel(request);
            return UiResultWrapper.convert(UiResult.ok());
        } catch (Exception e) {
            LoggerUtils.error(log, "司机取消，未知异常", e);
            return UiResultWrapper.fail(500,"系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }

    @RequestMapping("sensitiveWordsCheck")
    public UiResultWrapper sensitiveWordsCheck(@RequestBody TextCheckRequest request) {

        return UiResultWrapper.ok();
    }

    @PostMapping("riskStrategyTest")
    public UiResultWrapper riskStrategyTest(@RequestBody UnifyCheckRequest request){
        try {
            LoggerUtils.info(log,"req:{}",JSON.toJSONString(request));
            RiskSceneResult result = riskStrategyHandler.strategyCheck(request);
            LoggerUtils.info(log,"resp:{}",JSON.toJSONString(result));
            return UiResultWrapper.ok();
        } catch (Exception e) {
            LoggerUtils.error(log,"异常",e);
            return UiResultWrapper.fail();
        }
    }


    @PostMapping("unifyCheck")
    public UiResultWrapper unifyCheck(@RequestBody UnifyCheckRequest request) {
        LoggerUtils.initLogMap("unifyCheck", request.getProductLine(), request.getTraceId(), StringUtils.defaultIfBlank(request.getMemberId(), request.getCarNum()));
        try {
            LoggerUtils.info(log,"unifyCheck req:{}",JSON.toJSONString(request));
            RiskSceneResult result = riskStrategyHandler.unifyCheck(request);
            LoggerUtils.info(log,"unifyCheck resp:{}",JSON.toJSONString(result));
            return UiResultWrapper.ok(result);
        } catch (BizException e){
            LoggerUtils.error(log,"unifyCheck业务异常",e);
            return UiResultWrapper.fail(e.getCode(),e.getMessage());
        }catch (Exception e) {
            LoggerUtils.error(log,"unifyCheck系统异常",e);
            return UiResultWrapper.fail(-1,"系统异常");
        }finally {
            LoggerUtils.removeAll();
        }
    }


    /** ************************************************  下面是数据清洗 ************************************************************** */

    @GetMapping("dataClean")
    public UiResultWrapper cleanRiskMetricOldData(){
        try {
            LoggerUtils.info(log,"dataClean");
            riskMetricsService.cleanRiskMetricOldData();
            return UiResultWrapper.ok();
        } catch (Exception e) {
            LoggerUtils.error(log,"异常",e);
            return UiResultWrapper.fail();
        }
    }


    @GetMapping("couponDataClean")
    public UiResultWrapper cleanRiskMetricOldCouponData(){
        try {
            LoggerUtils.info(log,"couponDataClean");
            riskMetricsService.cleanRiskMetricOldCouponData();
            return UiResultWrapper.ok();
        } catch (Exception e) {
            LoggerUtils.error(log,"异常",e);
            return UiResultWrapper.fail();
        }
    }

}
