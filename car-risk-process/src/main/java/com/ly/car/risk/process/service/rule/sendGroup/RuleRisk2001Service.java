package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.CommonCustomerParam;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.rule.receiveGroup.FilterReceiveOrderContext;
import com.ly.car.risk.process.service.rule.receiveGroup.FilterReceiveOrderHandler;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Scope("prototype")
public class RuleRisk2001Service extends FilterSendOrderHandler {

    private static final String ruleNo = "2001";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource
    private RiskHitService riskHitService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        CommonCustomerParam param = new CommonCustomerParam();
        param.setDriverCardNo(context.getDriverCardNo());
        List<RiskCustomerManage> customerManageList = riskCustomerService.getListByValueByGroup(param,new Date());
        if(CollectionUtils.isNotEmpty(customerManageList)){
            //先判断一对一
            RiskCustomerManage oneToOneCustomer = customerManageList.stream()
                    .filter(data->data.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode()) && data.getBindUser().equals(context.getUserPhone()))
                    .findFirst().orElse(null);
            if(oneToOneCustomer != null){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(RiskCustomerRiskTypeEnum.getMsgByCode(oneToOneCustomer.getRiskType()),
                        RiskLevelEnum.HIGH.getCode(),1,oneToOneCustomer.getCustomerValue(),context.getUiResult()));
                log.info("[][][][]命中一对一名单{}", JsonUtils.json(oneToOneCustomer));
                context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.HIGH.getCode()));
                return;
            }
            //有白名单直接过
            RiskCustomerManage customerManage = customerManageList.stream()
                    .filter(manage -> manage.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode())).findFirst().orElse(null);

            if(customerManage != null){
                return;
            }
            for(RiskCustomerManage manage : customerManageList){
                if(manage.getRiskType().equals(RiskCustomerRiskTypeEnum.black_list.getCode()) ||
                    manage.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_receive_list.getCode()) ||
                    manage.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_ync_receive_list.getCode()) ||
                        (manage.getRiskType().equals( RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode()) && manage.getBindUser().equals(context.getUserPhone()))){
                    List<String> orderIds = new ArrayList<>();
                    orderIds.add(context.getOrderId());
                    distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),
                            0,context.getDriverCardNo(), RiskLevelEnum.HIGH.getCode());
                    context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.HIGH.getCode()));

                    riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(RiskCustomerRiskTypeEnum.getMsgByCode(manage.getRiskType()),
                            RiskLevelEnum.HIGH.getCode(),1,manage.getCustomerValue(),context.getUiResult()));
                    return;
                }
            }
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }
    }
}
