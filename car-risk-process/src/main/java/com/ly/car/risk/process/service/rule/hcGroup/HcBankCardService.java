package com.ly.car.risk.process.service.rule.hcGroup;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.TianChuangRiskClient;
import com.ly.car.risk.process.api.param.TianChuangCommonParam;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class HcBankCardService extends FilterHcAroundHandler{

    @Resource
    private TianChuangRiskClient tianChuangRiskClient;

    @Override
    public void doAfter(RiskResultNewDTO resultDTO) {

    }

    @Override
    public void doHandler(FilterHcContext context) {
        String msg="";
        TianChuangCommonParam param = new TianChuangCommonParam();
        param.setIdCard(context.getIdCard());
        param.setName(context.getName());
        param.setBankCard(context.getBankCard());
//        param.setMobile(context.getMobile());
        JSONObject jsonObject = new JSONObject();
        Integer result = tianChuangRiskClient.verifyBankCard3(param,null);
        if(result != 0){
            RiskResultNewDTO<Object> resultNewDTO = new RiskResultNewDTO<>(1,"解绑卡异常","");
            context.getUiResult().setData(resultNewDTO);
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }
    }
}
