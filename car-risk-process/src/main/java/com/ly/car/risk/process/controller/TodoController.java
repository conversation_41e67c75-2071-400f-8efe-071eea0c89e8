package com.ly.car.risk.process.controller;

import com.ly.car.risk.process.service.TodoService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("todo")
@RestController
public class TodoController {
    @Resource
    private TodoService todoService;


    @GetMapping("send")
    public String send() {
        todoService.send("Hello World~~");
        return "SUCCESS";
    }
}
