package com.ly.car.risk.process;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ly.car.risk.process.component.FastjsonCodec;
import com.ly.tcbase.config.ConfigCenterClient;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class RedissonConfig {

    @Bean(value = "riskProcessRedisson", destroyMethod = "shutdown")
    public RedissonClient RedissonConfig() throws Exception {
        String configStr = ConfigCenterClient.get("TCBase.Cache.v2");
        List<CacheInstance> cacheInstances = JSONObject.parseObject(configStr, new TypeReference<List<CacheInstance>>(){});
        CacheInstance cacheInstance = cacheInstances.stream()
                .filter(p -> StringUtils.equals(p.getName(), "risk.process.redis"))
                .findFirst()
                .get();
        List<String> ips = cacheInstance.getInstances().stream().map(p -> p.getIp()).collect(Collectors.toList());
        Config config = new Config();
        config.setCodec(new org.redisson.client.codec.StringCodec());
        ClusterServersConfig clusterServersConfig = config.useClusterServers();
        if (ips.size() != 0) {
            for (String ip : ips) {
                clusterServersConfig.addNodeAddress("redis://" + ip);
            }
        }
        RedissonClient client = Redisson.create(config);
        return client;
    }
}
