package com.ly.car.risk.process.strategy.model;

import com.ly.car.risk.process.strategy.model.detail.RiskRuleDetail;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Description of RiskStrategy
 *
 * <AUTHOR>
 * @date 2024/5/31
 * @desc
 */
@Data
public class RiskStrategyDetail {

    // 策略名称
    private String strategyName;
    // 策略id
    private Long strategyId;
    //策略编号
    private String strategyNo;
    // 场景
    private String scene;
    // 业务线
    private List<String> productLine;
    // 渠道(852,433)
    private List<String> channels;

    // 规则
    private List<RiskRuleDetail> rules = new ArrayList<>();

    // 策略分类 0-安全 1-风控 2-全部
    private Integer strategyType;

    //风险等级 1-低 2-中 3-高
    private Integer level;

    //风险类型 0-安全 1-风控 2-全部
    private Integer riskType;

    //城市id，空为全国
    private String cityId;

    //表达式
    private String expression;

    //执行脚本
    private String script;

    //策略返回文案
    private String strategyWord;

    //管控时间 单位天
    private Integer controlTime;
    // 冻结时长，单位分钟
    private int freezeTime;
    //管控对象 0-司机 1-用户
    private Integer controlType;

    //命中字段
    private String hitField;

    //命中动作0:加全局黑 1-加1v1
    private Integer hitAction;

    //处置动作 0-禁止 1-增强校验 2-通过
    private Integer disposeAction;
    // 0-测试 1-上线运行 2-下线
    private int status;
    // 供应商
    private List<String> supplierCodes;
}