package com.ly.car.risk.process.service.rule.priceGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.context.FilterSceneContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 当前用户手机号、设备号、关联乘车人手机号在黑名单中
 * */
@Component
@Slf4j
@Scope("prototype")
public class RuleRisk1001Service extends FilterCheckPriceHandler{

    public static final String ruleNo = "1001";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Override
    public UiResult doHandler(FilterSceneContext context) {
        UiResult result = context.getUiResult();
        result.setData(new RiskResultDTO());
        //是否白名单，有一个直接返回，什么逻辑都不走
        RiskCustomerManage whiteManage = context.getRiskCustomerManageList().stream().filter(e->e.getRiskType().equals(RiskCustomerRiskTypeEnum.white_list.getCode())).findFirst().orElse(null);
        if(whiteManage != null){
            result = UiResult.ok();
            RiskResultDTO dto = new RiskResultDTO(0,"风控通过","", whiteManage.getCustomerValue(),RiskLevelEnum.NO.getCode());
            dto.setCashRate("1.0");
            result.setData(dto);
            return result;
        }
        //判断是否黑名单
        RiskCustomerManage blackManage = context.getRiskCustomerManageList().stream().filter(e-> e.getRiskType().equals(RiskCustomerRiskTypeEnum.black_list.getCode())).findFirst().orElse(null);
        log.info("[RuleRisk1001Service][][][]1001查询黑名单:{}", JsonUtils.json(blackManage));
        if(blackManage != null){
            result = UiResult.ok();
            result.setMsg("风控不通过");
            RiskResultDTO dto = new RiskResultDTO(405,"风控不通过",ruleNo,blackManage.getCustomerValue(), RiskLevelEnum.HIGH.getCode());
            dto.setCashRate(context.getRateMap().get(ruleNo));
            result.setData(dto);
            //这个又是规则又是名单那，放到名单里面
            // distributionRiskManageService.addByCustomerNoOrder(ruleNo,context.getMainScene(), context.getChildScene(), 0,blackManage.getCustomerValue());
            return result;
        }
        if(this.nextHandler != null){
            return this.nextHandler.doHandler(context);
        }
        return result;
    }
}
