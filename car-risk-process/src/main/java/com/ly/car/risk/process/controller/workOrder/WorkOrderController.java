package com.ly.car.risk.process.controller.workOrder;

import com.ly.car.risk.process.controller.params.WorkerOrderParams;
import com.ly.car.risk.process.supplier.Tsan.T3WorkOrderCallBackParam;
import com.ly.car.risk.process.service.SupplierWorkOrderService;
import com.ly.car.risk.process.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("workOrder")
@RestController
@Slf4j
public class WorkOrderController {

    @Resource
    private SupplierWorkOrderService supplierWorkOrderService;

    @RequestMapping("sync")
    public void sync() {
        try {
            long starTime = System.currentTimeMillis();
            LoggerUtils.info(log,"开始进行供应商订单同步");
            this.supplierWorkOrderService.sync("", false);
            LoggerUtils.info(log,"供应商订单同步结束，用时:{} ms",System.currentTimeMillis() - starTime);
        } catch (Exception e) {
            LoggerUtils.error(log, "供应商订单同步", e);
        } finally {
            LoggerUtils.removeAll();
        }
    }

    @RequestMapping("callBack")
    public void callBack(@RequestBody T3WorkOrderCallBackParam param){
        this.supplierWorkOrderService.syncWorkOrderBack(param);
    }

    @RequestMapping("generateWorkOrder")
    public Boolean generateWorkOrder(@RequestBody WorkerOrderParams params){
        this.supplierWorkOrderService.syncAdminWorker(params);
        return true;
    }

    @RequestMapping("generateWorkOrderResult")
    public Boolean generateWorkOrderResult(@RequestBody WorkerOrderParams params){
        this.supplierWorkOrderService.generateWorkOrderResult(params);
        return true;
    }
}
