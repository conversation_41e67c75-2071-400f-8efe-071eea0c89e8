package com.ly.car.risk.process.repo.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskHit;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface RiskHitMapper extends BaseMapper<RiskHit> {

    /**
     * 根据订单号查询最新的风控命中记录的freeze_end_time
     * @param orderId 订单号
     * @return freeze_end_time
     */
    Date queryLatestFreezeEndTimeByOrderId(@Param("orderId") String orderId);

}
