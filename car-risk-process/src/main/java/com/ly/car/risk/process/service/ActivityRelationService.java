package com.ly.car.risk.process.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.ActivityConfigMapper;
import com.ly.car.risk.process.repo.risk.mapper.ActivityRelationMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.ActivityConfig;
import com.ly.car.risk.process.repo.risk.mapper.entity.ActivityRelation;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityRelationService {

    @Resource
    private ActivityRelationMapper activityRelationMapper;
    @Resource
    private ActivityConfigMapper activityConfigMapper;

    public List<ActivityRelation> queryActivityRelation(String userId){
        if(StringUtils.isBlank(userId)){
            return new ArrayList<>();
        }
        List<Long> activityIds = this.activityConfigMapper.selectList(new QueryWrapper<ActivityConfig>()
                .gt("start_time",new Date())
                .lt("end_time",new Date())
                .eq("status",1)
        ).stream().map(ActivityConfig::getActivityId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(activityIds)){
            return new ArrayList<>();
        }
        return activityRelationMapper.selectList(new QueryWrapper<ActivityRelation>()
                .eq("user_id", userId)
                .in("activity_id",activityIds)
        );
    }

   /**
    * 通过更新时间查询近期订单 3天
    * */
    public List<ActivityRelation> queryActivityByUpdateTime(Long id){
        ActivityConfig activityConfig = activityConfigMapper.selectOne(new QueryWrapper<ActivityConfig>()
                .eq("activity_id", id)
        );
        if(activityConfig == null || new Date().before(activityConfig.getStartTime()) || new Date().after(activityConfig.getEndTime()) ||
            activityConfig.getStatus() != 1){
            return new ArrayList<>();
        }
        return activityRelationMapper.selectList(new QueryWrapper<ActivityRelation>()
                .between("update_time", DateUtil.addDay(new Date(),-3),new Date())
                .eq("activity_id",id)
        );
    }

    /**
     * 通过活动id、邀请者id、被邀请者id查询 关系绑定
     * */
    public ActivityRelation queryOneRelation(Long activityId,String userId,String inviterUserId){
        return this.activityRelationMapper.selectOne(new QueryWrapper<ActivityRelation>()
                .eq("activity_id",activityId)
                .eq("user_id",userId)
                .eq("inviter_user_id",inviterUserId)
        );
    }





    public void updateActivityRelation(ActivityRelation relation){
        this.activityRelationMapper.updateById(relation);
    }
}
