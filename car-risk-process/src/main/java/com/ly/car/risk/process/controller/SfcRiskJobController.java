package com.ly.car.risk.process.controller;

import com.ly.car.risk.process.controller.dto.DriverWithdrawalReq;
import com.ly.car.risk.process.controller.dto.SfcRiskJobExecuteReq;
import com.ly.car.risk.process.service.SfcRiskJobService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: fulin.li
 * @create-date: 2025/9/10 16:09
 */
@RestController
@RequestMapping("/sfcRiskJob")
public class SfcRiskJobController {

    @Resource
    private SfcRiskJobService sfcRiskJobService;

    /**
     * 真车主执行离线策略job
     * @param req
     */
    @RequestMapping("execute")
    public void execute(@RequestBody SfcRiskJobExecuteReq req) {
        sfcRiskJobService.execute(req);
    }

    /**
     * 真车主提现离线job
     * @param req
     */
    @RequestMapping("driverWithdrawal")
    public void driverWithdrawal(@RequestBody DriverWithdrawalReq req) {
        sfcRiskJobService.driverWithdrawal(req);
    }
}
