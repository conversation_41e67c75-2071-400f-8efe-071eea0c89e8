package com.ly.car.risk.process.service.dto.order;

import com.ly.travel.car.common.model.enums.CarTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * Description of TicketInfo
 *
 * <AUTHOR>
 * @date 2024/3/11
 * @desc
 */
@Data
public class CarInfo {
    /** 车牌号 */
    private String carNum;

    /** 供应商code */
    private String supplierCode;

    /** 供应商全称code */
    private String supplierFullCode;

    /** 供应商名称 */
    private String supplierName;

    /** c车编号 */
    private String carNo;

    /** 车类型（经济型、舒适型等 */
    private CarTypeEnum carType;

    /** 车辆品牌 */
    private String brand;

    /** 车辆颜色 */
    private String color;

    /** 车次标签 */
    private List<String> carTags;

    /** 接单司机Code */
    private String driverCode;

    /** 司机姓名 */
    private String driverName;

    /** 司机电话 */
    private String driverPhone;

    /**
     * 司机虚拟手机号
     */
    private String driverVirtualPhone;



}