package com.ly.car.risk.process.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderItemBizTypeEnum {

    UNKNOWN(0, "未知"),
    TICKET(1, "车票"),
    AUXILIARY(2, "统一辅营"),
    COUPON(3, "红包/抵用券"),
    MILEAGE(4, "里程"),
    PROMOTION(5, "活动营销"),
    DEDUCT(6, "抵扣商品"),

    ;

    private final Integer code;
    private final String  name;

    public static OrderItemBizTypeEnum getNameByCode(Integer code) {
        for (OrderItemBizTypeEnum enumItem : OrderItemBizTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem;
            }
        }
        return null;
    }
}
