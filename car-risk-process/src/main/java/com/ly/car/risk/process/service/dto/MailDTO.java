package com.ly.car.risk.process.service.dto;

import lombok.Data;

import java.util.List;

@Data
public class MailDTO {

    private String account = "tcwireless.quick.car";
    private String password = "";
    private String workId;
    private List<AttachmentDTO> attachments;
    private String templateId = "";
    private MailBody parameter;

    @Data
    public static class AttachmentDTO{
        private String fileName;
        private String fileSuffix;
        private String fileContent;
    }

    @Data
    public static class MailBody{
        private String emailSubject;
        private String emailBody;
    }
}
