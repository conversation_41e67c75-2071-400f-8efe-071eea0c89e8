package com.ly.car.risk.process.constants;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description:
 * @Author: jay.he
 * @Date: 2025-09-09 19:18
 * @Version: 1.0
 **/
public class EsIndexConstant {
    String esUrl = "http://es.dss.17usoft.com";

    public static enum EsConfig {
        commonTrafficSaasTrailData("commontraffic-saastraildata", "saas_traildata", "commontraffic-saastraildata-data", "215b5a1c356eb31520514cf01ac1dde7"),

        commonTrafficCurrDriverPos("carowner-driverpos", "driverpos", "", "2908c53f-a08f-4205-9cf8-b024bb9ed9fb"),
        ;

        public String indexPrefix;
        public String index;
        public String indexAlias;
        public String token;

        public static String getIndexAlias(String index) {
            EsConfig[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                EsConfig config = var1[var3];
                if (StringUtils.equals(index, config.index)) {
                    return config.indexAlias;
                }
            }

            return null;
        }

        public String getIndexPrefix() {
            return this.indexPrefix;
        }

        public String getIndex() {
            return this.index;
        }

        public String getIndexAlias() {
            return this.indexAlias;
        }

        public String getToken() {
            return this.token;
        }

        private EsConfig(String indexPrefix, String index, String indexAlias, String token) {
            this.indexPrefix = indexPrefix;
            this.index = index;
            this.indexAlias = indexAlias;
            this.token = token;
        }
    }
}
