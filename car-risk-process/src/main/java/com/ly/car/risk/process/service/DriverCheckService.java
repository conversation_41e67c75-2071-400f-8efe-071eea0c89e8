package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyReqExtConst;
import com.ly.car.risk.process.repo.risk.mapper.DriverCheckMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverCheck;
import com.ly.car.risk.process.utils.StrategyUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class DriverCheckService {

    @Resource
    private DriverCheckMapper driverCheckMapper;

    //0-身份证 1-风险评分 2-驾驶证 3-行驶证 4-银行卡
    public void insert(JSONObject jsonObject, Integer type,Integer result,String remark){
        DriverCheck driverCheck = new DriverCheck();
        driverCheck.setCardName(jsonObject.getString("name"));
        driverCheck.setMobile(jsonObject.getString("mobile"));
        driverCheck.setDriverCardNo(jsonObject.getString("plate"));
        driverCheck.setType(type);
        driverCheck.setBankCardNo(jsonObject.getString("bankCard"));
        driverCheck.setIdCard(jsonObject.getString("idCard"));
        driverCheck.setResult(result);
        driverCheck.setRemark(remark);
        driverCheck.setCreateTime(new Date());
        driverCheck.setUpdateTime(new Date());
        driverCheck.setOwnerName(jsonObject.getString("carName"));
        this.driverCheckMapper.insert(driverCheck);
    }

    public void insert(UnifyCheckRequest request, Integer type, Integer result, String msg) {
        DriverCheck driverCheck = new DriverCheck();
        driverCheck.setCardName(StrategyUtil.getStringFromExt(request.getExt(), UnifyReqExtConst.CERT_NAME));
        driverCheck.setMobile(StrategyUtil.getStringFromExt(request.getExt(),UnifyReqExtConst.DRIVER_PHONE));
        driverCheck.setDriverCardNo(StringUtils.defaultIfBlank(request.getCarNum(),""));
        driverCheck.setType(type);
        driverCheck.setBankCardNo(StrategyUtil.getStringFromExt(request.getExt(),UnifyReqExtConst.BANK_CARD_NO));
        driverCheck.setResult(result);
        driverCheck.setRemark(msg);
        driverCheck.setCreateTime(new Date());
        driverCheck.setUpdateTime(new Date());
        driverCheck.setIdCard(StrategyUtil.getStringFromExt(request.getExt(),UnifyReqExtConst.CERT_NO));
        driverCheck.setOwnerName(StrategyUtil.getStringFromExt(request.getExt(),UnifyReqExtConst.CAR_OWNER_NAME));
        this.driverCheckMapper.insert(driverCheck);
    }

    public void update(DriverCheck driverCheck){
        driverCheck.setUpdateTime(new Date());
        this.driverCheckMapper.updateById(driverCheck);
    }

    public List<DriverCheck> queryResult(Integer type,String cardName,String driverCardNo,String idCard){
        return driverCheckMapper.selectList(new QueryWrapper<DriverCheck>()
                .between("create_time", TimeUtil.currentDay(),new Date())
                .eq(StringUtils.isNotBlank(cardName),"card_name",cardName)
                .eq(StringUtils.isNotBlank(driverCardNo),"driver_card_no",driverCardNo)
                .eq(StringUtils.isNotBlank(idCard),"id_card",idCard)
                .eq("type",type)
        );
    }

    public List<DriverCheck> queryResult(Integer type,String name,String idCard){
        return driverCheckMapper.selectList(new QueryWrapper<DriverCheck>()
                .between("create_time", TimeUtil.currentDay(),new Date())
                .eq(StringUtils.isNotBlank(name),"card_name",name)
                .eq(StringUtils.isNotBlank(idCard),"id_card",idCard)
                .eq("type",type)
        );
    }

    public List<DriverCheck> queryResultAgain(Integer type,String name,String idCard){
        return driverCheckMapper.selectList(new QueryWrapper<DriverCheck>()
                .between("create_time", DateUtil.addDay(new Date(),-180),new Date())
                .eq(StringUtils.isNotBlank(name),"card_name",name)
                .eq(StringUtils.isNotBlank(idCard),"id_card",idCard)
                .eq("type",type)
        );
    }
}
