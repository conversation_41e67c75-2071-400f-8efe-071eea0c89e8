package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.constants.NewRiskOrderTypeEnum;
import com.ly.car.risk.process.controller.params.CommonParams;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.TcSupplierWorkOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.TcSupplierWorkOrder;
import com.ly.car.risk.process.service.dto.HcSceneConfig;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

@RequestMapping("admin")
@RestController
@Slf4j
public class AdminPortalController {

    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;
    @Resource
    private TcSupplierWorkOrderMapper tcSupplierWorkOrderMapper;
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;

    @RequestMapping("queryRisk")
    public UiResult queryRisk(@RequestBody CommonParams params){
        UiResult result = UiResult.ok();
        String orderId = params.getOrderId();
        Map<String,String> returnMap = new HashMap<>();
        RiskOrderManage manage = riskOrderManageMapper.selectOne(new QueryWrapper<RiskOrderManage>().eq("order_id", orderId));
        if(manage == null){
            return result;
        }
        TcSupplierWorkOrder tcSupplierWorkOrder = tcSupplierWorkOrderMapper.selectOne(new QueryWrapper<TcSupplierWorkOrder>().eq("order_id", orderId));
        returnMap.put("id",String.valueOf(manage.getId()));
        returnMap.put("hitTime", DateUtil.date2String(manage.getCreateTime()));
        returnMap.put("riskType", NewRiskOrderTypeEnum.getMsgByCode(manage.getRiskType()));
        if(tcSupplierWorkOrder != null){
            if(tcSupplierWorkOrder.getIsSync() == 1){
                returnMap.put("syncSupplier","是");
                if(tcSupplierWorkOrder.getJudgeResult() == 0){
                    returnMap.put("judgeResult","待反馈");
                }
                if(tcSupplierWorkOrder.getJudgeResult() == 1){
                    returnMap.put("judgeResult","司机有责");
                }
                if(tcSupplierWorkOrder.getJudgeResult() == 2){
                    returnMap.put("judgeResult","司机无责");
                }
            } else {
                returnMap.put("syncSupplier","否");
            }
            returnMap.put("judgeType",tcSupplierWorkOrder.getJudgeType() == 6?"清零":
                    tcSupplierWorkOrder.getJudgeType() == 7?"退款":"调价");
        }
        result.setData(returnMap);
        return result;
    }

    @RequestMapping("queryAuth")
    public UiResult queryAuth(){
        UiResult result = UiResult.ok();
        result.setData(queryConfig());
        return result;
    }

    @RequestMapping("queryBlackDriver")
    public UiResult queryBlackDriver(@RequestBody CommonParams params){
        UiResult result = UiResult.ok();
        if(params.getDriverNos().isEmpty()){
            return result;
        }
        List<RiskCustomerManage> customerManageList = this.riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .in("customer_value", params.getDriverNos())
                .gt("invalid_time",new Date())
                .in("risk_type",1,7)
        );
        result.setData(customerManageList);
        return result;
    }


    private List<String> queryConfig(){
        try {
            String configJson = ConfigCenterClient.get("black_auth_customer");
            if(StringUtils.isBlank(configJson)){
                return null;
            }
            List<String> workIds = Arrays.asList(configJson.split(","));
            return workIds;
        } catch (Exception e) {
        }
        return null;
    }

}
