package com.ly.car.risk.process.service.rule.mtGroup;

import com.ly.car.risk.process.repo.risk.mapper.entity.RiskScene;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskStrategy;
import com.ly.car.risk.process.service.core.RiskSceneCache;
import com.ly.car.risk.process.service.core.RiskStrategyCache;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class FilterTextConvertService {

    @Resource
    private RiskStrategyCache riskStrategyCache;
    @Resource
    private RiskSceneCache riskSceneCache;

    public String convertText(String sceneNo,String productLine){
        if(sceneNo.equals("11-7")){
            return null;
        }
        RiskScene riskScene = riskSceneCache.loadScene(sceneNo);
        log.info("[][][][]统一处理返回获取场景{}", JsonUtils.json(riskScene));
        if(riskScene == null){
            return null;
        }
        String guid = riskScene.getGuid();
        List<RiskStrategy> strategyList = riskStrategyCache.loadStrategyList(guid,productLine);
        log.info("[][][][]统一处理返回获取场景所属策略{}", JsonUtils.json(strategyList));
        if(CollectionUtils.isEmpty(strategyList)){
            return null;
        }
        return strategyList.get(0).getStrategyWord();
    }

}
