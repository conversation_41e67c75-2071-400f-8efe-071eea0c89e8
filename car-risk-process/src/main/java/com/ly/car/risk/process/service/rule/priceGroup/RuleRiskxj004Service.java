package com.ly.car.risk.process.service.rule.priceGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.context.FilterSceneContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Component
@Slf4j
@Scope("prototype")
public class RuleRiskxj004Service extends FilterCheckPriceHandler{

    private static final String ruleNo = "xj004";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Override
    public UiResult doHandler(FilterSceneContext context) {
        if(!context.getSpecialCarRuleConfig().getXj004onOff()){
            if(this.nextHandler != null){
                return this.nextHandler.doHandler(context);
            } else {
                return context.getUiResult();
            }
        }

        UiResult uiResult = context.getUiResult();
        if(context.getIsNewUser() == 1 && context.getEsAmount().compareTo(new BigDecimal(context.getSpecialCarRuleConfig().getXj004LeftAmount())) < 1
                && context.getEsAmount().compareTo(new BigDecimal(context.getSpecialCarRuleConfig().getXj004RightAmount())) > 0){
            // distributionRiskManageService.addByCustomerNoOrder(ruleNo,context.getMainScene(),context.getChildScene(),0,null);
            RiskResultDTO resultDTO = new RiskResultDTO(0,"",ruleNo,null, RiskLevelEnum.HIGH.getCode());
            resultDTO.setCashRate(context.getRateMap().get(ruleNo));
            uiResult.setData(resultDTO);
            return uiResult;
        }

        if(this.nextHandler != null){
            return this.nextHandler.doHandler(context);
        }
        return context.getUiResult();
    }
}
