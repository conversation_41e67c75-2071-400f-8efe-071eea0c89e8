package com.ly.car.risk.process.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.risk.process.api.param.DriverDisposeParam;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.controller.params.DriverBadParam;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.DriverReportDisposeMapper;
import com.ly.car.risk.process.repo.risk.mapper.DriverReportMapper;
import com.ly.car.risk.process.repo.risk.mapper.HcCustomerMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverReport;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverReportDispose;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcCustomer;
import com.ly.car.risk.process.service.dto.DriverReportDisposeDTO;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DriverReportService {

    @Resource
    private CarOrderService carOrderService;

    public List<String> badActionNoList = new ArrayList<>();

    public DriverReportService(){
        badActionNoList.add("CZPJ001");
        badActionNoList.add("CZPJ002");
        badActionNoList.add("CZPJ003");
        badActionNoList.add("CZPJ004");
        badActionNoList.add("CZPJ005");
        badActionNoList.add("CZPJ006");
        badActionNoList.add("CZPJ009");
        badActionNoList.add("CZPJ010");
    }

    @Resource
    private DriverReportMapper driverReportMapper;
    @Resource
    private DriverReportDisposeMapper driverReportDisposeMapper;
    @Resource
    private HcCustomerMapper hcCustomerMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;

//    @Scheduled(cron = "0 0 1 * * ?")
    public void computeBad(){
        Date startDate = DateUtil.addDay(TimeUtil.currentDay(), -1);
        Date endDate = DateUtil.addHour(startDate,24);
        List<DriverReport> driverReportList = this.driverReportMapper.selectList(new QueryWrapper<DriverReport>()
                .between("create_time", startDate,endDate)
        );
        driverReportList = driverReportList.stream().collect(Collectors.toMap(DriverReport::getOrderId, Function.identity(),(old,cur)->old))
                            .values()
                            .stream()
                            .collect(Collectors.toList());

        //对业务线分开处理
        List<DriverReport> mtDriverReportList = driverReportList.stream().filter(data->data.getProductLine().equals("MT")).collect(Collectors.toList());
        List<DriverReport> hcDriverReportList = driverReportList.stream().filter(data->data.getProductLine().equals("HC")).collect(Collectors.toList());
        Map<String,List<DriverReport>> driverReportMap = new HashMap<>();
        driverReportMap.put("mt",mtDriverReportList);
        driverReportMap.put("hc",hcDriverReportList);
        for(Map.Entry<String,List<DriverReport>> entrySource : driverReportMap.entrySet()){
            Map<String,List<DriverReport>> reportGroup = entrySource.getValue().stream()
                    .collect(Collectors.groupingBy(DriverReport::getDriverCardNo));
            for(Map.Entry<String,List<DriverReport>> entry : reportGroup.entrySet()){
                log.info("[][][][]开始处理用户投诉司机选项{}{}",entry.getKey(), JsonUtils.json(entry.getValue()));
                Integer banReceive = 0;
                List<DriverReport> reportList = entry.getValue();
                if(reportList.size() == 3){
                    banReceive = 1;
                } else if(reportList.size() > 3 && reportList.size() < 10){
                    banReceive = 2;
                } else if(reportList.size() >= 10){
                    banReceive = 30;
                }
                if(banReceive > 0){
                    //开始同步
                    DriverReportDispose dispose = new DriverReportDispose();
                    dispose.setDriverCardNo(entry.getKey());
                    dispose.setBadActionName(reportList.get(0).getBadActionName());
                    dispose.setDisposeResult("暂停服务"+banReceive+"天");
                    dispose.setCreateTime(new Date());
                    dispose.setValidityTime(banReceive);
                    dispose.setFinialTime(DateUtil.addDay(new Date(),banReceive));
                    dispose.setProductLine(reportList.get(0).getProductLine());
                    this.driverReportDisposeMapper.insert(dispose);
                    //放入禁止接单黑名单
                    HcCustomer hcCustomer = new HcCustomer();
                    hcCustomer.setDriverCardNo(dispose.getDriverCardNo());
                    hcCustomer.setCreateTime(new Date());
                    hcCustomer.setCreateUser("用户举报任务");
                    hcCustomer.setUpdateTime(new Date());
                    hcCustomer.setUpdateUser("用户举报任务");
                    hcCustomer.setCustomerType(7);
                    hcCustomer.setInvalidTime(DateUtil.addDay(new Date(),banReceive));
                    hcCustomer.setRemark("当前因为"+dispose.getBadActionName()+"暂停服务");
                    this.hcCustomerMapper.insert(hcCustomer);
                }
            }
        }
    }

    public List<DriverReportDisposeDTO> getList(DriverDisposeParam param){
        if(param.getIllegalType() != 2){
            return new ArrayList<>();
        }
        List<DriverReportDispose> driverReportDisposeList = this.driverReportDisposeMapper.selectList(new QueryWrapper<DriverReportDispose>()
                .gt("finial_time",new Date())
                .eq("product_line", StringUtils.isNotBlank(param.getProductLine())?param.getProductLine():"MT")
        );
        List<DriverReportDisposeDTO> dtoList = new ArrayList<>();
        for(DriverReportDispose dispose : driverReportDisposeList){
            if(dispose.getDriverCardNo().length() < 3){
                continue;
            }
            DriverReportDisposeDTO dto = new DriverReportDisposeDTO();
            if(dispose.getDriverCardNo().length() < 3){
                continue;
            }
            String replaceStr = dispose.getDriverCardNo().substring(3,dispose.getDriverCardNo().length());
            dto.setPlate(dispose.getDriverCardNo().replace(replaceStr,"*****"));//前三位保留，后五位星号
            dto.setDisposeType(dispose.getBadActionName());
            dto.setDisposeResult(dispose.getDisposeResult());
            dto.setHitTime(DateUtil.date2String(dispose.getFinialTime()));
            dtoList.add(dto);
        }
        dtoList.stream().sorted(Comparator.comparing(DriverReportDisposeDTO::getHitTime));
        return dtoList;
    }

    public void test(JSONObject jsonObject){
        DriverReportDispose dispose = new DriverReportDispose();
        dispose.setDriverCardNo(jsonObject.getString("driverCardNo"));
        dispose.setBadActionName("行驶过快");
        dispose.setDisposeResult("暂停服务"+jsonObject.getInteger("times")+"天");
        dispose.setCreateTime(new Date());
        dispose.setValidityTime(jsonObject.getInteger("times"));
        dispose.setFinialTime(DateUtil.addDay(new Date(),jsonObject.getInteger("times")));
        this.driverReportDisposeMapper.insert(dispose);
    }

    public void syncDriverReport(DriverBadParam param) {
        if (!badActionNoList.contains(param.getBadActionNo())) {
            return;
        }
        //查下当前订单是不是顺风车，且为萌艇或卡罗拉
        String supplierCode;
        if (OrderUtils.isNewOrder(param.getOrderId())) {
            CarOrderDetail sfcOrder = carOrderService.queryOrderDetail(param.getOrderId());
            if (null == sfcOrder || null == sfcOrder.getCarInfo()) {
                return;
            }
            supplierCode = sfcOrder.getCarInfo().getSupplierCode();
        } else {
            //查下当前订单是不是顺风车，且为萌艇或卡罗拉
            SfcOrder sfcOrder = this.sfcOrderMapper.queryByOrderId(param.getOrderId());
            if (sfcOrder == null) {
                return;
            }
            supplierCode = sfcOrder.getSupplierCode();
        }

        if (supplierCode.startsWith("MadaSaas") || supplierCode.startsWith("zhizhuxia") || supplierCode.startsWith("miaoda")) {
            DriverReport driverReport = new DriverReport();
            driverReport.setDriverCardNo(param.getDriverCardNo());
            driverReport.setBadAction(param.getBadAction());
            driverReport.setBadActionName(param.getActionName());
            driverReport.setOrderId(param.getOrderId());
            driverReport.setCreateTime(new Date());
            driverReport.setBadActionNo(param.getBadActionNo());
            driverReport.setMemberId(param.getMemberId());
            driverReport.setUnionId(param.getUnionId());
            driverReport.setPhone(param.getPhone());
            driverReport.setProductLine(supplierCode.startsWith("MadaSaas") ? "MT" : "HC");
            this.driverReportMapper.insert(driverReport);
        }
    }
}
