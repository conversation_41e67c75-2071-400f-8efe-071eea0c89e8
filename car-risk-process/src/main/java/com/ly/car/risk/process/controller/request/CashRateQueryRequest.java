package com.ly.car.risk.process.controller.request;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class CashRateQueryRequest {

    private String     traceId;
    private String     memberId;
    private String     unionId;
    private String     userPhone;
    private String     passengerCellphone;
    private String     deviceId;
    private BigDecimal esAmount;
    private String     productLine = "YNC";//YNC,SFC
    private Integer    channel;
}
