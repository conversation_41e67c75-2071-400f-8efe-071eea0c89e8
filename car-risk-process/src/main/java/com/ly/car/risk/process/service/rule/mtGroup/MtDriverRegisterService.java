package com.ly.car.risk.process.service.rule.mtGroup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.hitchorder.entity.DriverInfo;
import com.ly.car.risk.process.repo.hitchorder.mapper.DriverInfoMapper;
import com.ly.car.risk.process.repo.mtticket.entity.DriverAccount;
import com.ly.car.risk.process.repo.mtticket.mapper.DriverAccountMapper;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class MtDriverRegisterService extends MtFilterHandler{

    @Resource
    private DriverAccountMapper driverAccountMapper;
    @Resource
    private DriverInfoMapper driverInfoMapper;

    @Override
    public void doHandler(MtFilterContext context) {
        if(context.getParam().getString("productLine").equals("MT")){
            log.info("[MtDriverRegisterService][doHandler][][]萌艇司机注册：{}", JsonUtils.json(context));
            if(!validParam(context.getParam())){
                log.info("[MtDriverRegisterService][doHandler][][]参数验证不通过：{}", JsonUtils.json(context));
                context.getDto().setCode(1);
                context.getDto().setMessage("萌艇司机注册错误");
                return;
            }
            List<DriverAccount> driverAccounts = driverAccountMapper.selectList(new QueryWrapper<DriverAccount>()
                    .between("create_time", DateUtil.addDay(new Date(), -1), new Date())
                    .eq("device_id", context.getParam().get("deviceId"))
            );
            List<String> accountList = driverAccounts.stream().map(DriverAccount::getAccountName).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(accountList) && accountList.size() > 3){
                context.getDto().setCode(1);
                context.getDto().setMessage("当前设备存在多账户注册情况，请核实");
                return;
            }
        } else {
            List<DriverInfo> driverList = this.driverInfoMapper.selectList(
                    new QueryWrapper<DriverInfo>().eq("device_id", context.getParam().get("deviceId"))
                            .gt("create_time", DateUtil.addHour(new Date(), -context.getHcSceneConfig().getScene7_1Time()))
            );
            if(CollectionUtils.isNotEmpty(driverList) && driverList.size() > context.getHcSceneConfig().getScene7_1Num()){
                context.getDto().setCode(1);
                context.getDto().setMessage("当前设备存在多账户注册情况，请核实");
                return;
            }
        }

        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }
    }
    public Boolean validParam(Map<String,Object> context){
        if(context.get("productLine") == null){
            return false;
        }
        if(context.get("deviceId") == null){
            return false;
        }
        return true;
    }
}
