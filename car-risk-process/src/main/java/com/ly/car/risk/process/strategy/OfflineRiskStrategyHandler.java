package com.ly.car.risk.process.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.ly.car.risk.common.enums.OfflineStrategyDuringEnum;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarOfflineRiskOrderDetail;
import com.ly.car.risk.process.service.RiskOrderManageService;
import com.ly.car.risk.process.strategy.model.OfflineRiskStrategyDetail;
import com.ly.car.risk.process.strategy.model.OfflineStrategyContext;
import com.ly.car.risk.process.strategy.model.detail.OfflineRiskFieldDetail;
import com.ly.car.risk.process.utils.GroovyScriptUtil;
import com.ly.car.risk.process.utils.LoggerUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OfflineRiskStrategyHandler {
    
    private static final ThreadLocal<HashMap<String, List<CarOfflineRiskOrderDetail>>> localCache = ThreadLocal.withInitial(() -> new HashMap<>());
    
    public static final String DURING            = "during";
    public static final String START_TIME        = "startTime";
    public static final String END_TIME          = "endTime";
    public static final String PRODUCT_LINE_LIST = "productLineList";
    public static final String CHANNELS = "channels";
    public static final String SUPPLIER_CODE_LIST = "supplierCodeList";
    
    @Resource
    protected RiskOrderManageService riskOrderManageService;
    
    @Resource
    private OfflineRiskStrategyHelper offlineRiskStrategyHelper;
    
    public void wash(String during) {
        
        try {
            
            // 组装上下文
            OfflineStrategyContext context = buildContext(during);
            
            // 寻找执行策略
            findStrategy(context);
            
            // 执行策略
            strategyHandle(context);
            
            // 后置打理
            strategyPostProcessing(context);
            
        } finally {
            localCache.remove();
        }
    }
    
    private void strategyPostProcessing(OfflineStrategyContext context) {
        
        List<RiskOrderManage> riskOrderList = context.getRiskOrderList();
        if (CollUtil.isEmpty(riskOrderList)) {
            return;
        }
        
        // 过滤掉用于测试的策略
        // 过滤掉通过的策略
        Map<String, OfflineRiskStrategyDetail> strategyMap = context.getStrategyList()
                .stream()
                .filter(s -> s.getStatus() != 0 && s.getDisposeAction() != 2)
                .collect(Collectors.toMap(OfflineRiskStrategyDetail::getStrategyNo, v -> v, (v1, v2) -> v1));
        
        // 同一订单多次命中合并，创建风险单
        Map<String, List<RiskOrderManage>> riskOrderMap = context.getRiskOrderList()
                .stream()
                .filter(order -> strategyMap.containsKey(order.getRuleNo()))
                .collect(Collectors.groupingBy(RiskOrderManage::getOrderId));
        
        for (Entry<String, List<RiskOrderManage>> entry : riskOrderMap.entrySet()) {
            List<RiskOrderManage> list = entry.getValue();
            RiskOrderManage riskOrderManage = list.get(0);
            riskOrderManage.setRuleNo(list.stream().map(RiskOrderManage::getRuleNo).distinct().collect(Collectors.joining(",")));
            riskOrderManageService.addRiskOrder(riskOrderManage);
        }
    }
    
    private void strategyHandle(OfflineStrategyContext context) {
        List<OfflineRiskStrategyDetail> strategyList = context.getStrategyList();
        if (CollUtil.isEmpty(strategyList)) {
            return;
        }
        
        for (OfflineRiskStrategyDetail strategyDetail : strategyList) {
            
            // 单一策略执行结果
            checkStrategy(strategyDetail, context);
        }
    }
    
    private void checkStrategy(OfflineRiskStrategyDetail strategy, OfflineStrategyContext context) {
        
        Map<String, Object> copyParams = JSON.parseObject(JSON.toJSONString(context.getParams()), Map.class);
        copyParams.put(PRODUCT_LINE_LIST, strategy.getProductLine());
        copyParams.put(CHANNELS, strategy.getChannels());
        copyParams.put(SUPPLIER_CODE_LIST, strategy.getSupplierCodes());
        
        // 所有订单该策略下所有指标集合
        // -k orderNo
        // -v
        //    -- 指标
        //    -- 指标数值
        Map<String, Map<String, Object>> orderFieldMap = new HashMap<>();
        
        // 补充未命中的指标
        Set<String> fieldIdSet = strategy.getFields().stream().map(f -> "field" + f.getSort()).collect(Collectors.toSet());
        
        for (OfflineRiskFieldDetail field : strategy.getFields()) {
            
            // 符合指标的所有订单
            HashMap<String, Object> result = getFieldScriptResult(field.getScript(), copyParams, field.getFieldNo());
            List<CarOfflineRiskOrderDetail> orders = (List<CarOfflineRiskOrderDetail>) Optional.ofNullable(result.get("orders")).orElse(Collections.emptyList());
            
            // 填充订单下这个指标的值
            for (CarOfflineRiskOrderDetail order : orders) {
                
                String fieldId = "field" + field.getSort();
                
                Map<String, Object> map = orderFieldMap.get(order.getOrderSerialNo());
                
                if (null == map) {
                    Map<String, Object> fieldDataMap = new HashMap<>();
                    fieldDataMap.put(fieldId, 1);
                    orderFieldMap.put(order.getOrderSerialNo(), fieldDataMap);
                } else {
                    map.put(fieldId, 1);
                }
                LoggerUtils.info(log, "订单:{}，命中指标[{}]:{}", order.getOrderSerialNo(), field.getFieldName(), field.getFieldNo());
            }
        }
        
        for (Entry<String, Map<String, Object>> entry : orderFieldMap.entrySet()) {
            String orderSerialNo = entry.getKey();
            Map<String, Object> fieldValueMap = entry.getValue();
            Set<String> existFiledSet = fieldValueMap.keySet();
            
            // 补充未命中指标
            for (String fieldId : fieldIdSet) {
                if (!existFiledSet.contains(fieldId)) {
                    fieldValueMap.put(fieldId, 0);
                }
            }
            
            // 聚合指标结果，算出本条策略的结果
            boolean res = checkStrategyScript(strategy.getScript(), fieldValueMap, strategy.getStrategyNo());
            if (res) {
                RiskOrderManage manage = new RiskOrderManage();
                manage.setOrderId(orderSerialNo);
                manage.setIsRisk(1);
                manage.setRiskType(strategy.getRiskType());
                manage.setRuleNo(strategy.getStrategyNo());
                manage.setRemark(strategy.getStrategyWord());
                context.getRiskOrderList().add(manage);
                LoggerUtils.info(log, "订单:{}，命中策略:{}", orderSerialNo, strategy.getStrategyNo());
            }
        }
        
    }
    
    private Boolean checkStrategyScript(String script, Map data, String strategyNo) {
        Object[] args = { data };
        Boolean ret = false;
        try {
            ret = (Boolean) GroovyScriptUtil.invokeMethod(script, "check", args);
        } catch (Exception e) {
            LoggerUtils.warn(log, "执行指标获取，groovy脚本失败,strategyNo:{}", e, strategyNo);
        }
        return ret;
    }
    
    private HashMap<String, Object> getFieldScriptResult(String ruleScript, Map data, String fieldNo) {
        Object[] args = { data };
        HashMap<String, Object> ret = null;
        try {
            ret = (HashMap<String, Object>) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
        } catch (Exception e) {
            LoggerUtils.error(log, "执行规则,groovy脚本失败,fieldNo:{}", e, fieldNo);
            ret = new HashMap<>();
        }
        return ret;
    }
    
    private void findStrategy(OfflineStrategyContext context) {
        List<OfflineRiskStrategyDetail> strategyList = offlineRiskStrategyHelper.findStrategy((String) context.getParams().get(DURING));
        String strategyNos = strategyList.stream().map(OfflineRiskStrategyDetail::getStrategyNo).collect(Collectors.joining(","));
        LoggerUtils.info(log, "获取到对应策略:{} ", strategyNos);
        context.setStrategyList(strategyList);
    }
    
    private OfflineStrategyContext buildContext(String during) {
        OfflineStrategyContext context = new OfflineStrategyContext();
        
        // 执行过程参数
        context.setParams(fillParam(during));
        context.setRiskOrderList(new ArrayList<>());
        return context;
    }
    
    private Map<String, Object> fillParam(String during) {
        Map<String, Object> params = new HashMap<>();
        
        DateTime[] times = OfflineStrategyDuringEnum.getDuring(during);
        params.put(START_TIME, times[0].toString());
        params.put(END_TIME, times[1].toString());
        params.put(DURING, during);
        
        return params;
    }
    
    public List<CarOfflineRiskOrderDetail> getLocal(String methodName) {
        HashMap<String, List<CarOfflineRiskOrderDetail>> localMap = localCache.get();
        return localMap.get(methodName);
    }
    
    public void setLocal(String methodName, List<CarOfflineRiskOrderDetail> orderList) {
        HashMap<String, List<CarOfflineRiskOrderDetail>> localMap = localCache.get();
        localMap.put(methodName, orderList);
    }
}
