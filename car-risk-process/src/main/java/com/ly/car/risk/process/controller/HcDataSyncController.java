package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSONObject;
import com.ishumei.fp.boxid.BoxIdTranslator;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.api.ShuMeiClient;
import com.ly.car.risk.process.api.param.DriverActionCollectParam;
import com.ly.car.risk.process.api.param.DriverDisposeParam;
import com.ly.car.risk.process.controller.params.DriverBadParam;
import com.ly.car.risk.process.repo.risk.mapper.DriverActionCollectMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.DriverActionCollect;
import com.ly.car.risk.process.service.DriverReportService;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Date;

@RestController
@RequestMapping("hcData")
@Slf4j
public class HcDataSyncController {

    private static final String PRIVATE_KEY = "-----BEGIN RSA PRIVATE KEY-----" +
            "MIIBOwIBAAJBAKxB+oTH2zq+4uZLpF/Saj0VUPg4mde0+g3+C3F/VQGQR538tYUZ" +
            "LqkdAQ/7WGtPlNPSXlNll7pt9veOwVp4IqECAwEAAQJAEMbwTmRkvsj+sm1lmguV" +
            "W1OrKjHhEfhHbntyw+Ah+F/bIewVW4vCsjKsrfWQx7Qd80aVtO+vJDLyRCeXEm6k" +
            "UQIhANJ0fNJ8Wtwe39kMo/F6qYx7MJvXCEWD0VCeMZ41evglAiEA0YlOLwLDSTJk" +
            "BkIO5mNG9DJt0oww4UcsId+Go3HDqc0CIQCR8PPKZy/+624hrxFVWNGQuLDZsQFs" +
            "a1/QvxL7emy+/QIgNe8WvckDRUMANhAoz6p8J/0XAZvuNP5h4LItcMKmr30CIQCi" +
            "U3eVVA3jW0IzZbt3/HCtC3q+KTbE3rEqU/EzjVUS7Q==" +
            "-----END RSA PRIVATE KEY-----";
    @Resource
    private ShuMeiClient shuMeiClient;
    @Resource
    private DriverReportService driverReportService;
    @Resource
    private DriverActionCollectMapper driverActionCollectMapper;

    @RequestMapping("smCollect")
    public UiResult smDataCollect(HttpServletRequest request, @RequestBody JSONObject jsonObject) throws UnknownHostException {
        log.info("[][][][]秒嗒上报设备信息:{}", JsonUtils.json(jsonObject));
        return UiResult.ok(shuMeiClient.sendSmData(request, jsonObject));
    }

    @RequestMapping("computeBad")
    public UiResult computeBad(){
        this.driverReportService.computeBad();
        return UiResult.ok();
    }

    @RequestMapping("driverCollect")
    public UiResult driverCollect(@RequestBody DriverActionCollectParam param){
        log.info("[][][][]司机行为信息上报:{}", JsonUtils.json(param));
        DriverActionCollect collect = new DriverActionCollect();
        collect.setDriverId(param.getDriverId());
        collect.setMobile(param.getMobile());
        collect.setDriverCardNo(param.getPlate());
        collect.setEventType(param.getEventType());
        collect.setBoxId(BoxIdTranslator.outputSmid(param.getBoxId(), PRIVATE_KEY));//这里需要解密的设备指纹
        collect.setCreateTime(new Date());
        driverActionCollectMapper.insert(collect);
        return UiResult.ok();
    }

    @RequestMapping("disposeDriverList")
    public UiResult disposeDriverList(@RequestBody DriverDisposeParam param){
        return UiResult.ok(driverReportService.getList(param));
    }

    @RequestMapping("test")
    public UiResult test(@RequestBody JSONObject jsonObject){
        this.driverReportService.test(jsonObject);
        return UiResult.ok();
    }

    @RequestMapping("syncDriverReport")
    public UiResult syncDriverReport(@RequestBody DriverBadParam param){
        this.driverReportService.syncDriverReport(param);
        return UiResult.ok();
    }

    @RequestMapping("testDevice")
    public UiResult testDevice(@RequestBody JSONObject jsonObject){
        this.shuMeiClient.testDevice();
        return UiResult.ok();
    }
}
