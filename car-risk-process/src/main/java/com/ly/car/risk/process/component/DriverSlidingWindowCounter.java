package com.ly.car.risk.process.component;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Component
@Slf4j
public class DriverSlidingWindowCounter {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    public List<OrderRiskContext> getDriverWindow(String key, Long startMs){
        key = RedisKeyConstants.DRIVER_SLIDING_WINDOW + key;
        //当前时间
        long currentTime = System.currentTimeMillis();
        //窗口开始时间
//        long windowStartMs = currentTime - windowSecond * 1000L;
        //按score统计key中符合的订单
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        //优化下节省内存
        long initMs = TimeUtil.initMs();
        scoredSortedSet.removeRangeByScore(initMs,true,startMs,false);

        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, currentTime, true);
        List<OrderRiskContext> objList = new ArrayList<>();
        for(ScoredEntry<Object> obj : scoredEntries){
            OrderRiskContext context = JSONObject.parseObject(obj.getValue().toString(),OrderRiskContext.class);
            objList.add(context);
        }
        return objList;
    }
}
