package com.ly.car.risk.process.controller;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.component.DriverSlidingWindowCounter;
import com.ly.car.risk.process.component.OrderStatusSlidingWindowCounter;
import com.ly.car.risk.process.component.SendOrderSlidingWindowCounter;
import com.ly.car.risk.process.component.UserSlidingWindowCounter;
import com.ly.car.risk.process.controller.params.RedisParam;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.service.redis.OrderStatusDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RKeys;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("redis")
@Slf4j
public class RedisController {

    @Resource
    private UserSlidingWindowCounter userSlidingWindowCounter;
    @Resource
    private DriverSlidingWindowCounter driverSlidingWindowCounter;
    @Resource
    private SendOrderSlidingWindowCounter sendOrderSlidingWindowCounter;
    @Resource
    private OrderStatusSlidingWindowCounter orderStatusSlidingWindowCounter;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;

    @RequestMapping("getRedisByUser")
    public List<OrderRiskContext> getRedisByUser(@RequestBody RedisParam param){
        return userSlidingWindowCounter.getUserWindow(param.getKey(),param.getStartMs());
    }

    @RequestMapping("getRedisByDriver")
    public List<OrderRiskContext> getRedisByDriver(@RequestBody RedisParam param){
        return driverSlidingWindowCounter.getDriverWindow(param.getKey(),param.getStartMs());
    }

    @RequestMapping("getCommonRedis")
    public List<SendOrderContext> getCommonRedis(@RequestBody RedisParam param){
        return sendOrderSlidingWindowCounter.getCommonWindow(param.getKey(),param.getStartMs());
    }

    @RequestMapping("getStatusRedis")
    public List<OrderStatusDTO> getStatusRedis(@RequestBody RedisParam param){
        return orderStatusSlidingWindowCounter.getCommonWindow(param.getKey(),param.getStartMs());
    }

    @RequestMapping("queryRedis")
    public Map<String,Object> getRedisMap(@RequestBody RedisParam redisParam){
        return redissonClient.getMap(redisParam.getKey());
    }

    @RequestMapping("queryData")
    public UiResult queryData(@RequestBody RedisParam redisParam){
        RBucket<String> bucket = redissonClient.getBucket(redisParam.getKey());
        if(bucket.isExists() && StringUtils.isNotBlank(bucket.get())){
            return UiResult.ok(bucket.get());
        }
        return null;
    }

    @RequestMapping("queryExpireTime")
    public UiResult queryExpireTime(){
        RKeys keys = redissonClient.getKeys();
        Iterable<String> keys1 = keys.getKeys();
        for(String str : keys1){
            long ttl = redissonClient.getKeys().remainTimeToLive(str);
            if(ttl == -1){
                log.info("无过期时间的key"+str);
                redissonClient.getKeys().delete(str);
            }
        }
        return UiResult.ok();
    }


}
