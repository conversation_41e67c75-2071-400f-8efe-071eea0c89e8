package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 当前下单设备号（unionID）关联完单大于3单，且乘车人手机号大于等于3个
 * */
@Component
@Slf4j
@Scope("prototype")
public class RuleRisk017Service extends FilterSendOrderHandler{

    private static final String ruleNo = "017";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        if(!context.getSpecialCarRuleConfig().getRule017onOff()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                return;
            }
        } else {
            boolean flag = true;
            List<SendOrderContext> orderContextList = context.getDeviceList().stream()
                    .filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.oneDay())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderContextList) && orderContextList.size() > context.getSpecialCarRuleConfig().getRule017OrderNum()) {
                List<String> phoneList = orderContextList.stream()
                        .map(SendOrderContext::getPassengerCellphone).distinct().collect(Collectors.toList());
                if (phoneList.size() >= context.getSpecialCarRuleConfig().getRule017PhoneNum()) {
                    context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.MEDIUM.getCode()));
                    List<String> orderIds = orderContextList.stream().map(SendOrderContext::getOrderId)
                            .distinct()
                            .collect(Collectors.toList());
                    orderIds.add(context.getOrderId());
                    distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene()
                            , context.getChildScene(), 0, null, RiskLevelEnum.MEDIUM.getCode());
                    flag = false;
                }
            }
            List<SendOrderContext> orderContextByUnionId = context.getMemberList().stream()
                    .filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.oneDay())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderContextByUnionId) && orderContextByUnionId.size() > context.getSpecialCarRuleConfig().getRule017OrderNum()) {
                List<String> phoneList = orderContextByUnionId.stream()
                        .map(SendOrderContext::getPassengerCellphone).distinct().collect(Collectors.toList());
                if (phoneList.size() >= context.getSpecialCarRuleConfig().getRule017PhoneNum()) {
                    if(flag){
                        context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.MEDIUM.getCode()));
                    }
                    List<String> orderIds = orderContextByUnionId.stream().map(SendOrderContext::getOrderId)
                            .distinct()
                            .collect(Collectors.toList());
                    orderIds.add(context.getOrderId());
                    distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene()
                            , context.getChildScene(), 0, null, RiskLevelEnum.MEDIUM.getCode());

                    orderIds.remove(context.getOrderId());
                    if(StringUtils.isBlank(context.getRuleNo())){
                        context.setRuleNo(ruleNo);
                    } else {
                        context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                    }
                    riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                }
            }

            if (this.nextHandler != null) {
                this.nextHandler.doHandler(context);
            }
        }
    }
}
