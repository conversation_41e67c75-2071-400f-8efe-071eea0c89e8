package com.ly.car.risk.process.controller.ability;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.ability.params.MobileCheckParam;
import com.ly.car.risk.process.service.ability.AbilityMobileCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("mobile")
@Slf4j
public class MobileStatusController {

    @Resource
    private AbilityMobileCheckService abilityMobileCheckService;

    @RequestMapping("initBloomFilter")
    public UiResult initBloomFilter(){
        abilityMobileCheckService.initMobileBloom();
        return UiResult.ok();
    }

    @RequestMapping("check")
    public UiResult mobileCheck(@RequestBody MobileCheckParam param){
        return abilityMobileCheckService.mobileCheck(param);
    }

    @RequestMapping("checkTest")
    public UiResult checkTest(@RequestBody MobileCheckParam param){
        return abilityMobileCheckService.checkTest(param.getMobileList());
    }
}
