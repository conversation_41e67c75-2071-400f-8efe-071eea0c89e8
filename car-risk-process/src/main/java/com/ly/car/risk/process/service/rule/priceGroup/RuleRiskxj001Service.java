package com.ly.car.risk.process.service.rule.priceGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.context.FilterSceneContext;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Component
@Slf4j
@Scope("prototype")
public class RuleRiskxj001Service extends FilterCheckPriceHandler{

    public static final String ruleNo = "xj001";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;

    @Override
    public UiResult doHandler(FilterSceneContext context) {
        if(!context.getSpecialCarRuleConfig().getXj001onOff()){
            if(this.nextHandler != null){
                return this.nextHandler.doHandler(context);
            } else {
                return context.getUiResult();
            }
        }
        UiResult uiResult = context.getUiResult();
        if(context.getRateMap().get("xjOnOff") != null && context.getRateMap().get("xjOnOff").equals("2")){
            RiskResultDTO resultDTO = new RiskResultDTO(0,"",ruleNo,null, RiskLevelEnum.HIGH.getCode());
            resultDTO.setCashRate(context.getRateMap().get(ruleNo));
            uiResult.setData(resultDTO);
            return uiResult;
        }
        if(context.getIsNewUser() == 0 && context.getEsAmount().compareTo(new BigDecimal(context.getSpecialCarRuleConfig().getXj001RightAmount())) > 0){
            // distributionRiskManageService.addByCustomerNoOrder(ruleNo,context.getMainScene(),context.getChildScene(),0,null);
            RiskResultDTO resultDTO = new RiskResultDTO(0,"",ruleNo,null, RiskLevelEnum.HIGH.getCode());
            resultDTO.setCashRate(context.getRateMap().get(ruleNo));
            uiResult.setData(resultDTO);
            return uiResult;
        }
        if(this.nextHandler != null){
            return this.nextHandler.doHandler(context);
        }
        return context.getUiResult();
    }


}
