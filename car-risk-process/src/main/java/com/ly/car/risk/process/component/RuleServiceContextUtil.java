package com.ly.car.risk.process.component;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class RuleServiceContextUtil {

    public static Map<String, List<String>> serviceNameMap;

    static {
        Map<String,List<String>> map = new HashMap<>();
        //用户询价场景所有相关规则
        List<String> SEARCH_PRICE_USER = new ArrayList<>();
        SEARCH_PRICE_USER.add("ruleRisk1001Service");//名单类
        SEARCH_PRICE_USER.add("ruleRiskxj001Service");
        SEARCH_PRICE_USER.add("ruleRiskxj002Service");
        SEARCH_PRICE_USER.add("ruleRiskxj003Service");
        SEARCH_PRICE_USER.add("ruleRiskxj004Service");
        SEARCH_PRICE_USER.add("ruleRiskxj005Service");
        SEARCH_PRICE_USER.add("ruleRiskxj006Service");
        SEARCH_PRICE_USER.add("ruleRiskxj007Service");
        map.put("5-1",SEARCH_PRICE_USER);

        List<String> USER_ORDER_SCENE = new ArrayList<>();
        USER_ORDER_SCENE.add("ruleRisk1003Service");//名单类
        USER_ORDER_SCENE.add("ruleRisk012Service");
        USER_ORDER_SCENE.add("ruleRisk013Service");
        USER_ORDER_SCENE.add("ruleRisk014Service");
        USER_ORDER_SCENE.add("ruleRisk015Service");
        USER_ORDER_SCENE.add("ruleRisk016Service");
        USER_ORDER_SCENE.add("ruleRisk017Service");
        USER_ORDER_SCENE.add("ruleRisk041Service");
        USER_ORDER_SCENE.add("ruleRisk042Service");
        map.put("2-3",USER_ORDER_SCENE);

        List<String> PLACE_ORDER_RECEIVE_ORDER = new ArrayList<>();
        PLACE_ORDER_RECEIVE_ORDER.add("ruleRisk2001Service");//名单类
        PLACE_ORDER_RECEIVE_ORDER.add("ruleRisk2002Service");
        PLACE_ORDER_RECEIVE_ORDER.add("ruleRisk2004Service");
        map.put("2-1",PLACE_ORDER_RECEIVE_ORDER);

        List<String> YANG_ZHAO_CASH = new ArrayList<>();
        map.put("10-1",YANG_ZHAO_CASH);
        serviceNameMap = map;
    }



}
