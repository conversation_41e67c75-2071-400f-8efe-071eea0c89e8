package com.ly.car.risk.process.service.dto;

import com.ly.car.common.bean.model.UiResult;
import lombok.Data;

@Data
public class HitInfoDTO {

    private String ruleNo;//规则编号或者风险名单类型，黑名单、禁止名单等
    private Integer riskLevel;
    private Integer hitType;//0-规则 1-名单
    private String customerValue;//名单值，命中规则是没有的
    private UiResult resResult;//返回值

    public HitInfoDTO(String ruleNo,Integer riskLevel,Integer hitType,String customerValue,UiResult resResResult){
        this.resResult =resResResult;
        this.ruleNo = ruleNo;
        this.hitType = hitType;
        this.riskLevel = riskLevel;
        this.customerValue = customerValue;
    }

}
