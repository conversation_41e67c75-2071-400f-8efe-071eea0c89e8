package com.ly.car.risk.process.service.riskMetrics.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import com.ly.car.risk.process.repo.riskmetrics.mapper.*;
import com.ly.car.risk.process.service.ability.MetricsConfigCenterService;
import com.ly.car.risk.process.service.dto.SecretReportReq;
import com.ly.car.risk.process.service.dto.SecretReportResp;
import com.ly.car.risk.process.service.riskMetrics.SfcRiskMetricsService;
import com.ly.car.risk.process.utils.ConfigCenterUtils;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fulin.li
 * @create-date: 2025/9/10 10:58
 */
@Slf4j
@Service(value = "sfcRiskMetricsService")
public class SfcRiskMetricsServiceImpl implements SfcRiskMetricsService {

    @Resource
    private CarRiskOrderDetailMapper orderDetailMapper;

    @Autowired
    private MetricsConfigCenterService metricsConfigCenterService;

    @Override
    public boolean hour24DriverOrderAmountGraterThan100CountGraterThan1(String orderSerialNo) {
        try {
            if (StringUtils.isBlank(orderSerialNo)) {
                log.warn("订单编号不能为空");
                return false;
            }
            //查询司机车牌号
            CarRiskOrderDetail carRiskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
            if (Objects.isNull(carRiskOrderDetail)) {
                log.warn("[SfcRiskMetricsServiceImpl][hour24DriverOrderAmountGraterThan100CountGraterThan1][][]根据订单编号查询订单不存在:{}", orderSerialNo);
                return false;
            }
            //取出司机车牌号
            if (Objects.isNull(carRiskOrderDetail.getCarNum())) {
                log.warn("[SfcRiskMetricsServiceImpl][hour24DriverOrderAmountGraterThan100CountGraterThan1][][]车牌号为空:{}", orderSerialNo);
            }
            String methodName = "hour24DriverOrderAmountGraterThan100CountGraterThan1";
            String hour = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "hour");
            String amount = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "amount");
            String limit = metricsConfigCenterService.getMetricsCarOwnerConfigValue(methodName, "limit");
            log.info("[SfcRiskMetricsServiceImpl][hour24DriverOrderAmountGraterThan100CountGraterThan1][{}][{}] 24小时内订单金额大于100的统一配置订单数:{},金额:{},限制数量：{}",orderSerialNo,methodName,hour,amount,limit);
            //通过车牌号查询订单
            List<CarRiskOrderDetail> list = orderDetailMapper.querySfc24HourFinishOrderByCarNum(carRiskOrderDetail.getCarNum(),Integer.parseInt(hour),new BigDecimal(amount));
            if (CollectionUtils.isEmpty(list)) {
                return false;
            }
            List<String> orderIds = list.stream().map(CarRiskOrderDetail::getOrderSerialNo).collect(Collectors.toList());
            log.info("[SfcRiskMetricsServiceImpl][hour24DriverOrderAmountGraterThan100CountGraterThan1][][]用户金额大于100的真车主订单：{}", orderIds);
            return list.size() > Integer.parseInt(limit);
        } catch (Exception e) {
            log.error("[SfcRiskMetricsServiceImpl][hour24DriverOrderAmountGraterThan100CountGraterThan1][][]查询大额订单失败", e);
        }
        return false;
    }

    @Override
    public boolean havePhoneRecording(String orderSerialNo) {
        try {
            if (StringUtils.isBlank(orderSerialNo)) {
                log.warn("订单编号不能为空");
                return false;
            }
            //查询司机车牌号
            CarRiskOrderDetail carRiskOrderDetail = orderDetailMapper.getByOrderSerialNo(orderSerialNo);
            if (Objects.isNull(carRiskOrderDetail)) {
                log.warn("[SfcRiskMetricsServiceImpl][havePhoneRecording][][]根据订单编号查询订单不存在:{}", orderSerialNo);
                return false;
            }
            //获取创单时间到当前时间查询话单
            String startTime = DateUtil.format(carRiskOrderDetail.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN);
            String endTime = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
            String url = ConfigCenterUtils.getSecretReportUrl();
            String token = ConfigCenterUtils.getSecretReportToken();
            SecretReportReq secretReportReq = new SecretReportReq();
            secretReportReq.setPageNum(1);
            secretReportReq.setPageSize(1);
            secretReportReq.setStartRingTime(startTime);
            secretReportReq.setEndRingTime(endTime);
            secretReportReq.setOrderSerialNo(orderSerialNo);
            Map<String, String> header = new HashMap<>();
            header.put("Labrador-Token", token);
            header.put("Labrador-Passthrough","true");
            String res = OkHttpClientUtil.getInstance().post(url, JsonUtils.json(secretReportReq), header);
            log.info("[SfcRiskMetricsServiceImpl][havePhoneRecording][][]查询司乘通话记录返回内容:{},请求地址：{},token：{},参数:{}", res,url,token, JsonUtils.json(secretReportReq));
            if (StringUtils.isBlank(res)) {
                return false;
            }
            SecretReportResp secretReportResp = JSONObject.parseObject(res, SecretReportResp.class);
            return Objects.nonNull(secretReportResp) && CollectionUtils.isNotEmpty(secretReportResp.getRecords());
        } catch (Exception e) {
            log.error("[SfcRiskMetricsServiceImpl][havePhoneRecording][][]查询司乘通话记录失败", e);
        }
        return false;
    }

}
