package com.ly.car.risk.process.service.rule.sendGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RuleChain;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 当前用户近24小时内订单经纬度和上笔完单经纬度距离小于500米的订单大于等于2笔，且最近两笔订单的接单司机一致
 * */
@Component
@Slf4j
@Scope("prototype")
public class RuleRisk014Service extends FilterSendOrderHandler{

    private static final String ruleNo = "014";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSendOrderContext context) {
        if(!context.getSpecialCarRuleConfig().getRule014onOff()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            } else {
                return;
            }
        } else {
            //获取24h订单
            List<SendOrderContext> orderContextList = context.getMemberList().stream()
                    .filter(orderContext -> orderContext.getFinishTime().after(TimeUtil.oneDay())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(orderContextList)) {
                log.info("[][RuleRisk014Service][][]014规则参数:{}" + JsonUtils.json(context));
                log.info("[][RuleRisk014Service][][]014订单数:{}" + JsonUtils.json(orderContextList));
                if(orderContextList.size() > context.getSpecialCarRuleConfig().getRule014OrderNum()){
                    orderContextList = orderContextList.stream().sorted(Comparator.comparing(SendOrderContext::getFinishTime).reversed()).collect(Collectors.toList());
                    //先看下最近两笔订单完单司机是否一致
                    Set<String> stringSet = new HashSet<>();
                    if (orderContextList.get(0).getDriverCardNo().equals(orderContextList.get(1).getDriverCardNo())) {
                        for (SendOrderContext contextBefore : orderContextList) {
                            for (SendOrderContext contextAfter : orderContextList) {
                                if (contextBefore.getOrderId().equals(contextAfter.getOrderId())) {
                                    continue;
                                }
                                double distance = CoordUtil.getDistance(contextBefore.getStartLat(), contextBefore.getStartLng(), contextAfter.getEndLat(), contextAfter.getEndLng());
                                if (new BigDecimal(distance).compareTo(new BigDecimal(context.getSpecialCarRuleConfig().getRule014Distance())) < 0) {
                                    stringSet.add(contextBefore.getOrderId());
                                    stringSet.add(contextAfter.getOrderId());
                                }
                            }
                        }
                        if (stringSet.size() >= 2) {
                            context.getRuleList().add(new RuleChain(ruleNo, RiskLevelEnum.MEDIUM.getCode()));
                            List<String> orderIds = orderContextList.stream().map(SendOrderContext::getOrderId).distinct().collect(Collectors.toList());
                            orderIds.add(context.getOrderId());
                            distributionRiskManageService.addManageCommon(orderIds, ruleNo, context.getMainScene(), context.getChildScene(),
                                    0, null, RiskLevelEnum.MEDIUM.getCode());

                            orderIds.remove(context.getOrderId());
                            if(StringUtils.isBlank(context.getRuleNo())){
                                context.setRuleNo(ruleNo);
                            } else {
                                context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                            }
                            riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                        }
                    }
                }
            }
            if (this.nextHandler != null) {
                this.nextHandler.doHandler(context);
            }
        }

    }


}
