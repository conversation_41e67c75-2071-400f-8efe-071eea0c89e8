package com.ly.car.risk.process.service.rule;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SpecialCarRuleConfig {

        private Boolean xj001onOff;
           private Integer xj001RightAmount;
    private Boolean xj002onOff;
    private Integer xj002LeftAmount;
    private Boolean xj003onOff;
    private Integer xj003RightAmount;
    private Boolean xj004onOff;
    private Integer xj004LeftAmount;
    private Integer xj004RightAmount;
    private Boolean xj005onOff;
    private Integer xj005LeftAmount;
    private Boolean xj006onOff;
    private Integer xj006LeftAmount;
    private Boolean xj007onOff;
    private Integer xj007RightAmount;
    private Boolean rule012onOff;
    private Integer rule012UserNum;
    private Boolean rule013onOff;
    private Integer rule013OrderNum;
    private Boolean rule014onOff;
    private Integer rule014Distance;
    private Integer rule014OrderNum;
    private Boolean rule015onOff;
    private Integer rule015Time;
    private Integer rule015OrderNum;
    private Boolean rule016onOff;
    private Integer rule016Distance;
    private Integer rule016OrderNum;
    private Boolean rule017onOff;
    private Integer rule017OrderNum;
    private Integer rule017PhoneNum;
    private Boolean rule041onOff;
    private Integer rule041CancelNum;
    private BigDecimal rule041CancelRate;
    private Boolean rule042onOff;
    private Integer rule042OrderNum;
    private Boolean rule2002onOff;
    private Integer rule2002OrderNum;
    private Boolean rule2004onOff;
    private Integer rule2004OrderNum;
    private BigDecimal rule2004Rate;

}
