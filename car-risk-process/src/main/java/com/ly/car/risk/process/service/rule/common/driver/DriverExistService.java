package com.ly.car.risk.process.service.rule.common.driver;

import com.ly.car.risk.process.service.DriverHistoryService;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.car.risk.process.service.rule.common.CommonFilterContext;
import com.ly.car.risk.process.service.rule.common.CommonFilterHandler;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.dto.HitchBlackSendData;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class DriverExistService extends CommonFilterHandler {

    private static final String TAG_NAME = "_driver_notify";

    @Resource
    private DriverHistoryService driverHistoryService;
    @Resource(name = "commonRiskProducer")
    private MqRiskProducer mqRiskProducer;

    @Override
    public void doHandler(CommonFilterContext context) {
        //对车牌进行加密
        RiskResultNewDTO dto = new RiskResultNewDTO();
        Map<String,Object> data = new HashMap<>();
        Map<String,Object> resultMap = new HashMap<>();
        data.put("driverId",context.getParam().getString("driverId"));
        data.put("idCard",context.getParam().getString("idCard"));
        dto.setObj(data);
        Integer count = driverHistoryService.queryDriver(DigestUtils.md5Hex(context.getParam().getString("plate")));
        log.info("[HcDriverHistoryService][][][]司机认证查询完单司机库:{}",count);
        if(count > 0){
            mqRiskProducer.send(context.getParam().getString("productLine")+TAG_NAME, JsonUtils.json(dto),0L);
            return;
        } else {
            this.nextHandler.doHandler(context);
        }
    }
}
