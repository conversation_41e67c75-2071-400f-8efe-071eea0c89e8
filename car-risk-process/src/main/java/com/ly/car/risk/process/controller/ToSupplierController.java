package com.ly.car.risk.process.controller;

import com.ly.car.risk.process.service.supplier.HelloBikeService;
import com.ly.car.risk.process.support.UiResultWrapper;
import com.ly.car.risk.process.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Description of ToSupplierController
 *
 * <AUTHOR>
 * @date 2024/5/20
 * @desc 对供应商的controller
 */
@RestController
@RequestMapping("/toSupplier")
@Slf4j
public class ToSupplierController {

    @Resource
    private HelloBikeService helloBikeService;


    @GetMapping(value = "/syncBlackToHelloBike")
    public UiResultWrapper syncBlackToHelloBike() {
        // 查询当前系统中 供应商名称为 哈啰顺风车 的黑名单用户
        try {
            long start = System.currentTimeMillis();
            LoggerUtils.info(log, "哈啰风控数据同步，开始");
            helloBikeService.syncBlack();
            LoggerUtils.info(log, "哈啰风控数据同步，结束，耗时:{} ms", start - System.currentTimeMillis());
            return UiResultWrapper.ok();
        } catch (Exception e) {
            LoggerUtils.info(log, "哈啰风控数据同步异常", e);
            return UiResultWrapper.fail(-1, "同步异常，原因:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }

    @GetMapping(value = "/allSyncBlackToHelloBike")
    public UiResultWrapper allSyncBlackToHelloBike() {
        // 查询当前系统中 供应商名称为 哈啰顺风车 的黑名单用户
        try {
            long start = System.currentTimeMillis();
            LoggerUtils.info(log, "全量哈啰风控数据同步，开始");
            helloBikeService.syncBlackAll();
            LoggerUtils.info(log, "全量哈啰风控数据同步，结束，耗时:{} ms", start - System.currentTimeMillis());
            return UiResultWrapper.ok();
        } catch (Exception e) {
            LoggerUtils.info(log, "全量哈啰风控数据同步异常", e);
            return UiResultWrapper.fail(-1, "同步异常，原因:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }
}