package com.ly.car.risk.process.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.order.entity.*;
import com.ly.car.risk.entity.DistributionRiskManage;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.client.SupplierApiClient;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskOrderManage;
import com.ly.car.risk.process.constants.ChannelEnum;
import com.ly.car.risk.process.controller.params.CommonParams;
import com.ly.car.risk.process.controller.params.StringListParams;
import com.ly.car.risk.process.repo.order.mapper.*;
import com.ly.car.risk.process.repo.risk.mapper.DistributionRiskManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskOrderManageMapper;
import com.ly.car.risk.process.utils.AmountUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderDriver;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderDriverMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.sharding.order.mapper.OrderSupplierBillMapper;
import com.ly.dal.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/test1")
public class Test1Controller {

    @Resource
    private RiskOrderManageMapper riskOrderManageMapper;
    @Resource
    private OrderSupplierBillMapper orderSupplierBillMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private SfcOrderMapper sfcOrderMapper;
    @Resource
    private DistributionRiskManageMapper distributionRiskManageMapper;
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private BlackDriverBlacklistRecordMapper blackDriverBlacklistRecordMapper;
    @Resource
    private SupplierApiClient                supplierApiClient;
    @Resource
    private SfcCouponRecordMapper            sfcCouponRecordMapper;
    @Resource
    private OrderWxCardMapper orderWxCardMapper;
    @Resource
    private OrderCouponMapper orderCouponMapper;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource
    private OrderDriverMapper orderDriverMapper;

    @RequestMapping("delRiskOrder")
    public void delRiskOrder(@RequestBody CommonParams params){
        List<RiskOrderManage> manageList = riskOrderManageMapper.selectList(
                new QueryWrapper<RiskOrderManage>().lt("create_time",params.getEndTime())
                .eq("supplier_code",params.getStartTime())
                .eq("rule_no","025")
        );
        //查到就要删掉
        List<Long> ids = manageList.stream().map(RiskOrderManage::getId).collect(Collectors.toList());
        if(ids.size() > 100000){
            return;
        }
        riskOrderManageMapper.deleteBatchIds(ids);
    }

    @RequestMapping("setEnv")
    public void setEnv(@RequestBody CommonParams params){
        List<RiskOrderManage> manageList = riskOrderManageMapper.selectList(
                new QueryWrapper<RiskOrderManage>().lt("create_time",params.getStartTime())

        );
        for(RiskOrderManage manage : manageList){
            if(manage.getOrderId().startsWith("YNC")){
                OrderInfo byOrderId = orderInfoMapper.findByOrderId(manage.getOrderId());
                manage.setEnv(byOrderId.getEnv());
                manage.setProductLine("YNC");
            } else {
                SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(manage.getOrderId());
                manage.setEnv(sfcOrder.getEnv().equals("PROD")?"PROD":"STAGE");
                manage.setProductLine("SFC");
            }
            riskOrderManageMapper.updateById(manage);
        }
    }

    @RequestMapping("delManageBy2")
    public void delManageBy2(){
        List<DistributionRiskManage> manageList = distributionRiskManageMapper.selectList(
                new QueryWrapper<DistributionRiskManage>().eq("main_scene",2)
        );
        List<Long> ids = manageList.stream().map(DistributionRiskManage::getId).collect(Collectors.toList());
        distributionRiskManageMapper.deleteBatchIds(ids);
    }

    /**
     * 同步司机黑名单原因
     * */
    @RequestMapping("syncBlackRecord")
    public void syncBlackRecord(){
        //查询当前名单中是客服手动拉黑原因的
        List<RiskCustomerManage> riskCustomerManages = riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>().eq("ttl",-1)
                        .eq("risk_remark","客服手动拉黑")
        );
        for(RiskCustomerManage manage : riskCustomerManages){
            BlackDriverBlacklistRecord record = blackDriverBlacklistRecordMapper.selectOne(
                    new QueryWrapper<BlackDriverBlacklistRecord>().eq("driver_plate_number", manage.getCustomerValue())
                            .eq("type", 1).eq("status", 1).last("limit 1")
            );
            if(record != null){
                manage.setOptionName(record.getOperator());
                manage.setRiskRemark(record.getReason());
                riskCustomerManageMapper.updateById(manage);
            }
        }
    }

    @RequestMapping("delDriverRecord")
    public void delDriverRecord(@RequestBody StringListParams params){
        //删除司机黑名单
        List<RiskCustomerManage> manageList = riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>().in("customer_value",params.getStrList())
        );
        List<Long> ids = manageList.stream().map(RiskCustomerManage::getId).collect(Collectors.toList());
        riskCustomerManageMapper.deleteBatchIds(ids);
    }

    @RequestMapping("initRiskInfo")
    public void initRiskInfo(){
        List<DistributionRiskManage> manageList = distributionRiskManageMapper.selectList(
                new QueryWrapper<DistributionRiskManage>().gt("create_time","2022-12-12 00:00:00")
        );
        for(DistributionRiskManage manage : manageList){
            if(StringUtils.isNotBlank(manage.getOrderId())){
                if(manage.getOrderId().startsWith("YNC")){
                    manage.setProductLine("YNC");
                } else {
                    manage.setProductLine("SFC");
                }
            }
            OrderInfo orderInfo = orderInfoMapper.findByOrderId(manage.getOrderId());
            if(orderInfo == null){
                continue;
            }
            manage.setEnv(orderInfo.getEnv().equals("PROD")?"PROD":"STAGE");
            if(manage.getIsCustomer() == 1){
                manage.setHitValue(manage.getDriverCardNo());
            }
            distributionRiskManageMapper.updateById(manage);
        }
    }

    @RequestMapping("initRiskOrder")
    public void initRiskOrder(){
        List<RiskOrderManage> manageList = riskOrderManageMapper.selectList(
                new QueryWrapper<RiskOrderManage>().gt("create_time","2022-12-12 00:00:00")
        );

        for(RiskOrderManage manage : manageList){
            if(manage.getOrderId().startsWith("SFC")){
                manage.setProductLine("SFC");
                SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(manage.getOrderId());
                List<SfcCouponRecord> sfcCouponRecord = sfcCouponRecordMapper.getListByOrderId(sfcOrder.getOrderId());
                if(sfcOrder != null){
//                orderManage.setDistributeOrderId(sfcOrder.get());
                    manage.setSupplierCode(sfcOrder.getSupplierCode());
                    manage.setChannelId(String.valueOf(sfcOrder.getRefId()));
                    manage.setChannelName(ChannelEnum.getMsgByCode(String.valueOf(sfcOrder.getRefId())));
                    manage.setSupplierName(supplierApiClient.getSupplierCodeMap().getOrDefault(sfcOrder.getSupplierCode(),""));
                    manage.setSupplierOrderId(sfcOrder.getSupplierOrderId());
                    manage.setEnv(sfcOrder.getEnv().equals("PROD")?"PROD":"STAGE");
                    manage.setFinishTime(sfcOrder.getFinishTime());
                    manage.setTotalAmount(AmountUtil.changeF2Y(sfcOrder.getTotalAmount()));
                }
                OrderAddress orderAddress = orderAddressMapper.findByOrderId(manage.getOrderId());
                if(orderAddress != null){
                    manage.setCityId(orderAddress.getStartCityId());
                    manage.setCityName(orderAddress.getStartCityName());
                    manage.setActualKilo(orderAddress.getEstimateKilo());
                    manage.setActualDuration(orderAddress.getActualMinute());
                }
                if(!CollectionUtils.isEmpty(sfcCouponRecord)){
                    List<String> couponList = sfcCouponRecord.stream().map(SfcCouponRecord::getCardId).collect(Collectors.toList());
                    List<String> couponNameList = sfcCouponRecord.stream().map(SfcCouponRecord::getTitle).collect(Collectors.toList());
                    manage.setCouponName(StringUtils.join(couponNameList,","));
                    manage.setCouponBatchNo(StringUtils.join(couponList,","));
                    manage.setCouponAmount(sfcCouponRecord.stream().map(SfcCouponRecord::getReduceCost).reduce(BigDecimal.ZERO,BigDecimal::add));
                }
//                SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderService.getByOrderId(sfcOrder.getOrderId(),sfcOrder.getSupplierOrderId());
//                if(sfcSupplierOrder != null){
//
//                }
            } else {
                manage.setProductLine("YNC");
                OrderInfo orderInfo = orderInfoMapper.findByOrderId(manage.getOrderId());
                if(orderInfo == null){
                    continue;
                }
                OrderAddress orderAddress = orderAddressMapper.findByOrderId(manage.getOrderId());
                if(orderAddress == null){
                    continue;
                }
                OrderDriver orderDriver = orderDriverMapper.findByOrderId(orderInfo.getOrderId());
                manage.setEnv(orderInfo.getEnv());
                manage.setDistributeOrderId(orderInfo.getDistributorOrderId());
                manage.setChannelId(String.valueOf(orderInfo.getRefId()));
                manage.setChannelName(ChannelEnum.getMsgByCode(String.valueOf(orderInfo.getRefId())));
                manage.setSupplierCode(orderInfo.getSupplierCode());
                manage.setSupplierName(supplierApiClient.getSupplierCodeMap().getOrDefault(orderInfo.getSupplierCode(),""));
                manage.setSupplierOrderId(orderInfo.getSupplierOrderId());
                manage.setCityId(orderAddress.getStartCityId());
                manage.setCityName(orderAddress.getStartCityName());
                manage.setFinishTime(orderInfo.getFinishTime());
                manage.setTotalAmount(orderInfo.getTotalAmount());
                //先查微信优惠券
                List<OrderWxCard> orderWxCards = orderWxCardMapper.selectList(
                        new QueryWrapper<OrderWxCard>().eq("order_id",manage.getOrderId())
                );
                if(!CollectionUtils.isEmpty(orderWxCards)){
                    List<String> couponNameList = orderWxCards.stream().map(OrderWxCard::getTitle).collect(Collectors.toList());
                    List<String> couponList = orderWxCards.stream().map(OrderWxCard::getCardId).collect(Collectors.toList());
                    manage.setCouponName(StringUtils.join(couponNameList,","));
                    manage.setCouponBatchNo(StringUtils.join(couponList,","));
                    manage.setCouponAmount(orderWxCards.stream().map(OrderWxCard::getReduceCost).map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add));
                } else {
                    //查app优惠券
                    List<OrderCoupon> orderCoupons = orderCouponMapper.selectList(
                            new QueryWrapper<OrderCoupon>().eq("order_id",manage.getOrderId())
                    );
                    List<String> couponNameList = orderCoupons.stream().map(OrderCoupon::getBatchName).collect(Collectors.toList());
                    List<String> couponList = orderCoupons.stream().map(OrderCoupon::getBatchNo).collect(Collectors.toList());
                    manage.setCouponName(StringUtils.join(couponNameList,","));
                    manage.setCouponBatchNo(StringUtils.join(couponList,","));
                    manage.setCouponAmount(orderCoupons.stream().map(OrderCoupon::getCouponAmount).map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add));
                }
                manage.setCouponAmount(BigDecimal.ZERO);
                manage.setActualKilo(orderAddress.getActualKilo());
                manage.setActualDuration(orderAddress.getActualMinute());
                manage.setDriverCardNo(orderDriver.getPlateNumber());
            }
            riskOrderManageMapper.updateById(manage);
        }
    }

    @RequestMapping("initSupplierCodeFull")
    public void initSupplierCodeFull(@RequestBody CommonParams commonParams){
        List<RiskOrderManage> manageList = new ArrayList<>();
        if(commonParams.getIds() != null){
            manageList = riskOrderManageMapper.selectBatchIds(commonParams.getIds());
            for(RiskOrderManage manage : manageList){
                OrderInfo orderInfo = orderInfoMapper.findByOrderId(manage.getOrderId());
                manage.setSupplierCodeFull(orderInfo.getSupplierCodeFull());
                riskOrderManageMapper.updateById(manage);
            }
        } else {
            Date initDate = DateUtil.string2Date("2022-11-09 00:00:01");
            Date currentDate = new Date();
            int i = 0;
            while (initDate.before(currentDate) && i < 100){
                //设置结束时间
                Date endDate = DateUtil.addDay(initDate,1);
                List<RiskOrderManage> manageList1 = riskOrderManageMapper.selectList(new QueryWrapper<RiskOrderManage>().gt("create_time", initDate).lt("create_time", endDate));
                for(RiskOrderManage manage : manageList1){
                    OrderInfo orderInfo = orderInfoMapper.findByOrderId(manage.getOrderId());
                    manage.setSupplierCodeFull(orderInfo.getSupplierCodeFull());
                    riskOrderManageMapper.updateById(manage);
                }
                initDate = DateUtil.addDay(initDate,1);
                i = i+1;
            }
        }
    }

}
