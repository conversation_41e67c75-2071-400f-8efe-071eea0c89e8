package com.ly.car.risk.process.controller.params;

import lombok.Data;

@Data
public class DriverSyncParams {
    private String driverCardNo;
    private String invalidTime;
    private Integer optionType;//
    private String optionName;
    private String riskRemark;
    private String orderId;
    private Integer ttl;//有效期限 1-1天 7-7天 30- 一个月 365-一年 -1-永久
    private String flag;


    private Integer customerType;//名单类型 0-一对一拉黑 1-全部拉黑
    private String shieldingType;//拉黑类型 1-用户要求禁止司机为其服务 2-发生严重安全风险 3-其他问题
    private String shieldingTypeChild;
    private String bindUser;//绑定用户的memberId
    private String driverName;

    //下面汇川专用
    private String driverId;
    private Integer riskCustomerType;//7
    private Integer riskType;//1黑名单
    private String source;//来源-HC

    // 供应商名称
    private String supplierName;
    // 用户mid
    private String memberId;

}
