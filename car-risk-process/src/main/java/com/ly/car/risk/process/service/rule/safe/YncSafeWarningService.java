package com.ly.car.risk.process.service.rule.safe;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.api.EsQueryClient;
import com.ly.car.risk.process.client.OrderClient;
import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.redis.SaveScoredSortedSetService;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.DriverLocationResponse;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMapCache;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class YncSafeWarningService {

    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private SaveScoredSortedSetService saveScoredSortedSetService;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource(name = "riskExecutorService")
    private ExecutorService executorService;
    @Resource
    private EsQueryClient esQueryClient;
    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Resource(name = "riskSecurityProducer")
    private MqRiskProducer riskSecurityProducer;
    @Resource
    private CarOrderService carOrderService;
    @Resource
    private OrderClient orderClient;

    public void computeMoving() {
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(RedisKeyConstants.YNC_MOVING_ORDER);
        //移除3天前的订单
        //优化下节省内存
        long initMs = TimeUtil.initMs();
        long threeDayMs = TimeUtil.threeDayMs();
        scoredSortedSet.removeRangeByScore(initMs, true, threeDayMs, true);

        //获取前一分钟的开始和结束时间
        String minute = DateUtil.date2String(DateUtil.addMinute(new Date(), -1), "yyyy-MM-dd HH:mm");

        long startMs = DateUtil.string2Date(minute + ":00").getTime();
        String endMinute = DateUtil.date2String(new Date(), "yyyy-MM-dd HH:mm");
        long endMs = DateUtil.string2Date(endMinute + ":00").getTime();

        LoggerUtils.info(log, "网约车安全预警,获取权重{}～{}间的订单", startMs, endMs);

        Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRange(startMs, true, endMs, true);
        List<String> orderIds = new ArrayList<>();
        for (ScoredEntry<Object> obj : scoredEntries) {
            String orderId = (String) obj.getValue();
            orderIds.add(orderId);
        }

        if (CollectionUtils.isEmpty(orderIds)) {
            LoggerUtils.info(log, "网约车安全预警,当前无订单需处理");
            return;
        }

        LoggerUtils.info(log, "网约车安全预警, 需处理订单:{}个，订单号为:{}", orderIds.size(), JSON.toJSONString(orderIds));

        //获取配置
        Integer distanceNum = 100;
        Integer speedNum = 120;
        Integer movingNum = 2500;
        try {
            String result = ConfigCenterClient.get("safe_warning_config");
            LoggerUtils.info(log, "网约车安全预警,获取网约车统一配置:{}", result);
            if (result != null) {
                JSONObject jsonObject = JSON.parseObject(result);
                distanceNum = jsonObject.getInteger("distance");
                speedNum = jsonObject.getInteger("speed");
                movingNum = jsonObject.getInteger("moving");
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "网约车安全预警,获取网约车统一配置失败", e);
        }

        HashMap<String, String> logMap = LoggerUtils.getLogMap();

        for (String orderId : orderIds) {
            Integer finalSpeedNum = speedNum;
            Integer finalDistanceNum = distanceNum;
            Integer finalMovingNum = movingNum;
            CompletableFuture.runAsync(() -> {
                LoggerUtils.initLogMap(logMap);
                LoggerUtils.getLogMap().put("filter2",orderId);
                try {
                    //获取预估距离，没有的话就全部移除，都不走了
                    RBucket<String> bucket = redissonClient.getBucket(RedisKeyConstants.YNC_ESTIMATE_PATH + orderId);
                    if (bucket == null || StringUtils.isBlank(bucket.get())) {
                        scoredSortedSet.remove(orderId);
                        LoggerUtils.info(log,"网约车安全预警，order:{},未获取到预估距离，不再处理",orderId);
                        return;
                    }

                    LoggerUtils.info(log,"网约车安全预警，order:{}, 预估路径：{}",orderId, bucket.get());

                    BigDecimal startLng = null;
                    BigDecimal endLng = null;
                    BigDecimal startLat = null;
                    BigDecimal endLat = null;

                    if (OrderUtils.isNewOrder(orderId)) {

                        CarOrderDetail order = carOrderService.queryOrderDetail(orderId);
                        if (null == order || null == order.getOrderTrip()) {
                            LoggerUtils.info(log, "网约车安全预警，order:{},未查询到对应订单，不再处理", orderId);
                            scoredSortedSet.remove(orderId);
                            return;
                        }

                        // 查询当前状态是否是已经完单
                        if (order.getOrderState() == OrderState.CANCELED.getCode()
                                || order.getOrderState() == OrderState.TRIP_FINISHED.getCode()
                                || order.getOrderState() == OrderState.ORDER_CLOSED.getCode()) {
                            LoggerUtils.info(log, "网约车安全预警，order:{},订单已完结，不再处理", orderId);
                            scoredSortedSet.remove(orderId);
                            return;
                        }

                        startLng = order.getOrderTrip().getDepartureLng();
                        endLng = order.getOrderTrip().getArrivalLng();
                        startLat = order.getOrderTrip().getDepartureLat();
                        endLat = order.getOrderTrip().getArrivalLat();

                    } else {
                        OrderInfo orderInfo = orderInfoMapper.findByOrderId(orderId);
                        //查询当前状态是否是已经完单
                        if (orderInfo.getStatus() > 200) {
                            LoggerUtils.info(log, "网约车安全预警，order:{},订单已完结，不再处理", orderId);
                            scoredSortedSet.remove(orderId);
                            return;
                        }
                        if (orderInfo.getProductId() == 130) {
                            LoggerUtils.info(log, "网约车安全预警，order:{},产品类型不符，不再处理", orderId);
                            scoredSortedSet.remove(orderId);
                            return;
                        }
                        OrderAddress orderAddress = orderAddressMapper.findByOrderId(orderId);
                        startLng = orderAddress.getStartLng();
                        endLng = orderAddress.getEndLng();
                        startLat = orderAddress.getStartLat();
                        endLat = orderAddress.getEndLat();
                    }

                    String estimatePath = bucket.get();

                    String lng = "";
                    String lat = "";

                    if (OrderUtils.isNewOrder(orderId)) {

                        DriverLocationResponse driverLocationResponse = orderClient.driverLocation(orderId);
                        if (driverLocationResponse == null || !driverLocationResponse.isSuccess()) {
                            LoggerUtils.info(log, "网约车安全预警，order:{},未查询到司机当前位置，不再处理", orderId);
                            scoredSortedSet.remove(orderId);
                            return;
                        }

                        lng = String.valueOf(driverLocationResponse.getLongitude());
                        lat = String.valueOf(driverLocationResponse.getLatitude());

                    } else {
                        String locationPoint = esQueryClient.queryCurrentLocation(orderId);
                        if (locationPoint == null) {
                            LoggerUtils.info(log, "网约车安全预警，order:{},未查询到司机当前位置，不再处理", orderId);
                            scoredSortedSet.remove(orderId);
                            return;
                        }

                        lng = locationPoint.split(",")[0];
                        lat = locationPoint.split(",")[1];
                    }

                    String locationPoint = lng + "," + lat;

                    LoggerUtils.info(log, "网约车安全预警,order:{},当前位置{}", orderId, locationPoint);

                    //存储当前司机位置
                    RMapCache<String, String> mapCache = redissonClient.getMapCache(RedisKeyConstants.ORDER_DRIVER_LOCATION_MINUTES);
                    String locationStr = mapCache.get(orderId);

                    LoggerUtils.info(log, "网约车安全预警,order:{},获取上一循环当前位置:{}", orderId, locationStr);

                    if (StringUtils.isNotBlank(locationStr)) {
                        //当前不为空的话就计算规则，暂时能计算的是
                        //1.计算是否未移动,当前位置与五分钟之前的位置相差是不是小于100米,处理是否异常停留
                        //专车去除起点终点
                        double distance = CoordUtil.getDistance(lng, lat, locationStr.split(",")[0], locationStr.split(",")[1]);
                        double startDistance = CoordUtil.getDistance(lng, lat, String.valueOf(startLng), String.valueOf(startLat));
                        double endDistance = CoordUtil.getDistance(lng, lat, String.valueOf(endLng), String.valueOf(endLat));

                        if (new BigDecimal(startDistance).compareTo(new BigDecimal("500")) > 0 && new BigDecimal(endDistance).compareTo(new BigDecimal("500")) > 0) {
                            if (new BigDecimal(distance).compareTo(new BigDecimal(finalDistanceNum)) < 0) {
                                LoggerUtils.info(log, "网约车安全预警,order:{}，命中安全预警策略aq003,当前经纬度{}，五分钟前经纬度{}", orderId, locationPoint, locationStr);
                                //这个时候塞入缓存，前端页面需要展示
                                redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING + orderId).put("aq003", DateUtil.date2String(new Date()) + "," + "车辆长时间停留");
                                redissonClient.getKeys().expire(RedisKeyConstants.HIT_SAFE_WARNING + orderId, 6, TimeUnit.MINUTES);

                                riskSecurityProducer.send(MqTagEnum.car_risk_warn_record, JsonUtils.json(convertParam(orderId, 1, "YNC")), 0);
                                LoggerUtils.info(log,"网约车安全预警,order:{},命中aq003,给tag:{}发送信息处理后续流程",orderId,MqTagEnum.car_risk_warn_record.name());
                            }
                        }
                        //2.计算车速过快
                        BigDecimal speed = new BigDecimal(distance).divide(new BigDecimal("1000"))
                                .divide(new BigDecimal("6"), 2, BigDecimal.ROUND_CEILING)
                                .multiply(new BigDecimal("60")).setScale(2, BigDecimal.ROUND_CEILING);
                        if (speed.compareTo(new BigDecimal(finalSpeedNum)) > 0) {
                            LoggerUtils.info(log, "网约车安全预警,order:{},命中安全预警策略aq002车速过快,当前经纬度{}，五分钟前经纬度{}", orderId, locationPoint, locationStr);
                            //这个时候塞入缓存，前端页面需要展示
                            redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING + orderId).put("aq002", DateUtil.date2String(new Date()) + "," + "车辆行驶速度过快");
                            redissonClient.getKeys().expire(RedisKeyConstants.HIT_SAFE_WARNING + orderId, 6, TimeUnit.MINUTES);

                            riskSecurityProducer.send(MqTagEnum.car_risk_warn_record, JsonUtils.json(convertParam(orderId, 2, "YNC")), 0);
                            LoggerUtils.info(log,"网约车安全预警,order:{},命中aq002,给tag:{}发送信息处理后续流程",orderId,MqTagEnum.car_risk_warn_record.name());
                        }
                        //处理路线偏移
                        dealAqMoving(orderId, lng, lat, estimatePath, finalMovingNum, startLng, endLng, startLat, endLat);
                        //执行完后 后面未完单则要继续计算，所以还是要塞入缓存的
                        mapCache.put(orderId, locationPoint, 10, TimeUnit.MINUTES);
                    } else {
                        LoggerUtils.info(log, "网约车安全预警,order:{},本次为第一次执行,存储当前位置入缓存后，等待下次执行", orderId);
                        //当前为空的话塞入缓存,说明是第一次，更新下下次需要执行的时间
                        mapCache.put(orderId, locationPoint, 10, TimeUnit.MINUTES);
                    }
                    //更新行程中订单下次需要执行的时间
                    saveScoredSortedSetService.save(RedisKeyConstants.YNC_MOVING_ORDER,
                            3 * 24 * 60 * 60L, orderId, DateUtil.addMinute(new Date(), 5).getTime());
                    redissonClient.getKeys().expire(RedisKeyConstants.ORDER_DRIVER_LOCATION_MINUTES, 10, TimeUnit.MINUTES);
                    LoggerUtils.info(log,"网约车安全预警,order:{}处理完毕,更新缓存5分钟后再次执行",orderId);

                } catch (Exception e) {
                    LoggerUtils.error(log, "网约车安全预警,执行过程中异常，order:{}", e, orderId);
                } finally {
                    LoggerUtils.removeAll();
                }

            }, executorService).handle((result, e) -> {
                if (e != null) {
                    LoggerUtils.info(log, "[YncSafeWarningService]CompletableFuture处理异常", e);
                }
                return result;
            });
        }
    }

    //处理aq003异常停留
    public void dealAq003Moving() {

    }

    //处理aq001
    public void dealAqMoving(String orderId, String lng, String lat, String pointStr, Integer movingNum, BigDecimal startLng, BigDecimal endLng, BigDecimal startLat, BigDecimal endLat) {
        LoggerUtils.info(log, "网约车安全预警,order:{}，处理aq001开始,pointStr：{}", orderId, pointStr);
        //计算路线偏移，当前位置在预估路径最近的距离
        if (StringUtils.isNotBlank(pointStr)) {
            List<String> locationPoint = new ArrayList<>(Arrays.asList(pointStr.split(";")));
            List<String> removeAll = new ArrayList<>();//移除途径点
            int decrNum = 0;
            //判断连续减的拐 点后是否是连续增以及超过10个都是连续增的话则判断路线偏移
            double lastestDistance = 0;
            String hitPoint = "";
            String hitLat = "";
            String hitLng = "";
            for (int i = 0; i < locationPoint.size() - 1; i++) {
                //把前面的点先放到数据里面
                removeAll.add(locationPoint.get(i));
                hitPoint = locationPoint.get(i);
                double movingDistanceBefore = CoordUtil.getDistance(lng, lat, locationPoint.get(i).split(",")[0], locationPoint.get(i).split(",")[1]);
                double movingDistanceAfter = CoordUtil.getDistance(lng, lat, locationPoint.get(i + 1).split(",")[0], locationPoint.get(i + 1).split(",")[1]);
                hitLat = locationPoint.get(i).split(",")[1];
                hitLng = locationPoint.get(i).split(",")[0];
                //看看是否是连续减，连续减说明是持续靠近，需要找到最近的点
                if (new BigDecimal(movingDistanceAfter).compareTo(new BigDecimal(movingDistanceBefore)) < 0) {
                    decrNum = decrNum + 1;
                } else {
                    //到这里说明不是持续靠近了
                    decrNum = 0;
                }
                if (i >= 0 && decrNum != 0) {
                    continue;
                }
                //到了这边说明是突然有了拐点，这个时候要看下后面是不是连续增大
                int incrNum2 = 0;
                for (int j = i + 1; j < locationPoint.size() - 1; j++) {
                    if (locationPoint.size() - j < 6) {
                        //说明马上到终点了，没啥必要继续判断是否偏移，交给其他策略
                        break;
                    }
                    double movingDistanceBefore2 = CoordUtil.getDistance(lng, lat, locationPoint.get(j).split(",")[0], locationPoint.get(j).split(",")[1]);
                    double movingDistanceAfter2 = CoordUtil.getDistance(lng, lat, locationPoint.get(j + 1).split(",")[0], locationPoint.get(j + 1).split(",")[1]);
                    if (new BigDecimal(movingDistanceBefore2).compareTo(new BigDecimal(movingDistanceAfter2)) < 0) {
                        incrNum2 = incrNum2 + 1;
                    } else {
                        incrNum2 = 0;
                    }
                    if (j != i && incrNum2 == 0) {
                        break;
                    }
                    if (incrNum2 > 5) {
                        break;
                    }
                }
                if (incrNum2 > 5) {
                    //到这边说明满足了连续减和连续增的情况，说明i这个位置是最短的点
                    lastestDistance = movingDistanceBefore;
                    break;
                }
            }
            //判断i的距离到当前位置是否小于1000，小于则未偏移，大于则偏移，存入缓存，移除前面的点减少位移
            if (new BigDecimal(lastestDistance).compareTo(new BigDecimal(movingNum)) > 0) {
                BigDecimal planeAngle = new BigDecimal(CoordUtil.getDegree(startLng.doubleValue(), startLat.doubleValue(),
                        endLng.doubleValue(), endLat.doubleValue(), Double.parseDouble(lng), Double.parseDouble(lat)));
                if (planeAngle.compareTo(new BigDecimal("90")) > 0) {
                    redissonClient.getMap(RedisKeyConstants.HIT_SAFE_WARNING + orderId).put("aq001", DateUtil.date2String(new Date()) + "," + "车辆行驶路线偏移");
                    redissonClient.getKeys().expire(RedisKeyConstants.HIT_SAFE_WARNING + orderId, 6, TimeUnit.MINUTES);
                    log.info("网约车安全预警,order:{}, 命中安全预警策略aq001当前经纬度{}，预估最近点{},相差距离为{},夹角为{}", orderId, lng + "," + lat, hitPoint, lastestDistance, planeAngle);
                    riskSecurityProducer.send(MqTagEnum.car_risk_warn_record, JsonUtils.json(convertParam(orderId, 0, "YNC")), 0);
                }
            }
            locationPoint.removeAll(removeAll);
            //更新预估路径
            redissonClient.getBucket(RedisKeyConstants.YNC_ESTIMATE_PATH + orderId).set(StringUtils.join(locationPoint, ";"), 1, TimeUnit.DAYS);
        }
        LoggerUtils.info(log, "网约车安全预警,order:{}, 处理aq001结束,更新预估路径", orderId);
    }

    public JSONObject convertParam(String orderId, Integer type, String productLine) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId", orderId);
        jsonObject.put("warnType", type);
        jsonObject.put("productLine", productLine);
        return jsonObject;
    }


    public static void main(String[] args) {
        String startLat = "31.2115620";
        String startLng = "121.6275240";
        String currentLat = "31.2720211";
        String currentLng = "121.4769511";
        String endLat = "31.2320290";
        String endLng = "121.4161550";
        System.out.println(CoordUtil.getAngle(startLat, startLng, currentLat, currentLng, endLat, endLng));
    }

}
