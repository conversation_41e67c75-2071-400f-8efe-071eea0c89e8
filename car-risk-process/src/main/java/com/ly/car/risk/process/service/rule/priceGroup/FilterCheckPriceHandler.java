package com.ly.car.risk.process.service.rule.priceGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.service.context.FilterSceneContext;
import com.ly.car.risk.process.service.rule.FilterCommonHandler;

public abstract class FilterCheckPriceHandler {

    public abstract UiResult doHandler(FilterSceneContext context);

    protected FilterCheckPriceHandler nextHandler;

    public void next(FilterCheckPriceHandler nextHandler){
        this.nextHandler = nextHandler;
    }
}
