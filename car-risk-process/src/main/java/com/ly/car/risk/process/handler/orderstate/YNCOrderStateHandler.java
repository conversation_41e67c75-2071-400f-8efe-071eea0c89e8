package com.ly.car.risk.process.handler.orderstate;

import com.ly.car.risk.process.constants.RedisKeyConstants;
import com.ly.car.risk.process.service.dto.SendOrderContext;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.redis.OrderStatusDTO;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * Description of WycOrderStateHandler
 *
 * <AUTHOR>
 * @date 2024/3/13
 * @desc
 */
@Service
@Slf4j
public class YNCOrderStateHandler extends AbstractOrderStateHandler{

    @Override
    public String supportType() {
        return "YNC";
    }

    @Override
    public void dealCancelState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
        if(orderDetail.isDistributionOrder()){
            return;
        }
        OrderStatusDTO dto = new OrderStatusDTO(orderDetail.getOrderId(),1000,new Date());
        if(StringUtils.isNotBlank(orderDetail.getMemberId())){
            saveScoredSortedSetService.save(RedisKeyConstants.USER_ORDER_NUMBER_MEMBER+orderDetail.getMemberId(),
                    3 * 24 * 60 * 60L,dto,new Date().getTime());
        }
        if(StringUtils.isNotBlank(orderDetail.getUnionId())){
            saveScoredSortedSetService.save(RedisKeyConstants.USER_ORDER_NUMBER_UNION+orderDetail.getUnionId(),
                    3 * 24 * 60 * 60L,dto,new Date().getTime());
        }
    }

    @Override
    public void dealReceiveOrderState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
//        virutalCallCheckTask(orderDetail);
    }

    @Override
    public void dealInTripState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
        if(orderDetail.isDistributionOrder()){
            return;
        }
        // 行程自动分享
        safetyTripShare(orderDetail);
        // 预估行程记录
        binLogProducer.send(MqTagEnum.car_risk_safe_warning,orderDetail.getOrderId(), 0L);
        LoggerUtils.info(log, "发送给[binlogConsumer],tag:{},msg:{}", MqTagEnum.car_risk_safe_warning.name(), orderDetail.getOrderId());
    }

    @Override
    public void dealTripFinishState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState) {
        //存入redis
        OrderStatusDTO dto = new OrderStatusDTO(orderDetail.getOrderId(),400,new Date());
        SendOrderContext orderContext = OrderUtils.convertYNCTripFinishOrderCache(orderDetail);
        if(!orderDetail.isDistributionOrder() && StringUtils.isNotBlank(orderContext.getPayAccount())){
            saveScoredSortedSetService.save(RedisKeyConstants.PAY_ACCOUNT_WINDOW+orderContext.getPayAccount(),
                    3 * 24 * 60 * 60L,orderContext,new Date().getTime());
        }
        if(!orderDetail.isDistributionOrder() && StringUtils.isNotBlank(orderContext.getDeviceId())){
            saveScoredSortedSetService.save(RedisKeyConstants.DEVICE_ID_WINDOW+orderContext.getDeviceId(),
                    3 * 24 * 60 * 60L,orderContext,new Date().getTime());
        }
        if(!orderDetail.isDistributionOrder() && StringUtils.isNotBlank(orderContext.getMemberId())){
            saveScoredSortedSetService.save(RedisKeyConstants.USER_MEMBER_WINDOW+orderContext.getMemberId(),
                    3 * 24 * 60 * 60L,orderContext,new Date().getTime());
            saveScoredSortedSetService.save(RedisKeyConstants.USER_ORDER_NUMBER_MEMBER+orderDetail.getMemberId(),
                    3 * 24 * 60 * 60L,dto,new Date().getTime());
        }
        if(!orderDetail.isDistributionOrder() && StringUtils.isNotBlank(orderContext.getUnionId())){
            saveScoredSortedSetService.save(RedisKeyConstants.USER_UNION_ID_WINDOW+orderContext.getUnionId(),
                    3 * 24 * 60 * 60L,orderContext,new Date().getTime());
            saveScoredSortedSetService.save(RedisKeyConstants.USER_ORDER_NUMBER_UNION+orderDetail.getUnionId(),
                    3 * 24 * 60 * 60L,dto,new Date().getTime());
        }
        // 车牌不管分销
        if(StringUtils.isNotBlank(orderContext.getDriverCardNo())){
            saveScoredSortedSetService.save(RedisKeyConstants.DRIVER_CARD_WINDOW+orderContext.getDriverCardNo(),
                    3 * 24 * 60 * 60L,orderContext,new Date().getTime());
        }
    }
}