package com.ly.car.risk.process.service.chain;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.params.FilterParams;

public abstract class FilterChainHandler {

    protected FilterChainHandler nextHandler;

    public void next(FilterChainHandler nextHandler){
        this.nextHandler = nextHandler;
    }

    public abstract UiResult doHandler(FilterParams params);

}
