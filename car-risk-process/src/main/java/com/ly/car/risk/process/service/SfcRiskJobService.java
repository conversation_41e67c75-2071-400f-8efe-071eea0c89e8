package com.ly.car.risk.process.service;

import cn.hutool.core.lang.UUID;
import com.ly.car.risk.process.controller.dto.DriverWithdrawalReq;
import com.ly.car.risk.process.controller.dto.SfcRiskJobExecuteReq;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.repo.riskmetrics.entity.CarRiskOrderDetail;
import com.ly.car.risk.process.repo.riskmetrics.entity.WithdrawalGroupByDriverIdResp;
import com.ly.car.risk.process.repo.riskmetrics.mapper.CarRiskOrderDetailMapper;
import com.ly.car.risk.process.repo.riskmetrics.mapper.DriverWithdrawalMapper;
import com.ly.car.risk.process.strategy.RiskStrategyHandler;
import com.ly.car.risk.process.strategy.model.DriverRiskInfo;
import com.ly.car.risk.process.strategy.model.DriverWithdrawalMqDTO;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.strategy.model.SendRiskResultMqDTO;
import com.ly.car.risk.process.turboMQ.MqTagEnum;
import com.ly.car.risk.process.turboMQ.producer.MqRiskProducer;
import com.ly.car.risk.process.utils.ThreadPoolExecutorManager;
import com.ly.car.utils.JsonUtils;
import com.ly.sof.utils.common.DateUtil;
import com.ly.tcbase.config.AppProfile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * @author: fulin.li
 * @create-date: 2025/9/10 16:42
 */
@Service
@Slf4j
public class SfcRiskJobService {

    @Resource
    private RiskStrategyHandler riskStrategyHandler;

    @Resource
    private CarRiskOrderDetailMapper carRiskOrderDetailMapper;

    @Resource
    private DriverWithdrawalMapper driverWithdrawalMapper;

    @Resource(name = "riskOrderRealCarOwnerHtProducer")
    private MqRiskProducer riskOrderRealCarOwnerHtProducer;

    private final ThreadPoolExecutor executor = ThreadPoolExecutorManager.getInstance("sfc-risk-job-task");

    public void execute(SfcRiskJobExecuteReq req) {

        List<CarRiskOrderDetail> list = carRiskOrderDetailMapper.querySfcByHourFinishOrder(req.getOffsetHour());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> orderIds = list.stream().map(CarRiskOrderDetail::getOrderSerialNo).collect(Collectors.toList());
        log.info("[SfcRiskJobService][execute][][] 需要处理的订单:{}", orderIds);
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        //循环执行
        for (CarRiskOrderDetail carRiskOrderDetail : list) {
            for (String scene : req.getSceneList()) {
                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    try {
                        UnifyCheckRequest request = new UnifyCheckRequest();
                        request.setScene(scene);
                        request.setChannel("10327");
                        request.setProductLine("MDSFC");
                        request.setOrderId(carRiskOrderDetail.getOrderSerialNo());
                        request.setTraceId(UUID.randomUUID().toString());
                        RiskSceneResult result = riskStrategyHandler.unifyCheck(request);
                        if (Objects.nonNull(result)) {
                            SendRiskResultMqDTO sendRiskResultMqDTO = new SendRiskResultMqDTO();
                            BeanUtils.copyProperties(result, sendRiskResultMqDTO);
                            sendRiskResultMqDTO.setOrderId(carRiskOrderDetail.getOrderSerialNo());
                            riskOrderRealCarOwnerHtProducer.send(MqTagEnum.car_risk_real_car_owner_ht_tag, JsonUtils.json(sendRiskResultMqDTO), 0);
                        }
                        log.info("[SfcRiskJobService][execute][{}][{}]调用风控结果:{},策略匹配请求参数:{}", carRiskOrderDetail.getOrderSerialNo(), scene, JsonUtils.json(result),JsonUtils.json(request));
                    } catch (Exception e) {
                        log.error("[SfcRiskJobService][execute][][] 风控调用失败", e);
                    }
                }, executor).exceptionally(e -> {
                    log.error("[SfcRiskJobService][execute][][]调用风控异常", e);
                    return null;
                });
                completableFutureList.add(completableFuture);
            }
        }
//        try {
//            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
//        } catch (Exception e) {
//            log.error("[SfcRiskJobService][execute][][]执行异常", e);
//        }

    }

    public void driverWithdrawal(DriverWithdrawalReq req) {
        String env = AppProfile.getEnvironment();
        log.info("[SfcRiskJobService][driverWithdrawal][][]查询环境:{}", env);
        List<WithdrawalGroupByDriverIdResp> list = driverWithdrawalMapper.selectDriverWithdrawalGroupByDriverIdAndOffsetTime(req.getOffsetHour(), env);
        log.info("[SfcRiskJobService][driverWithdrawal][][]真车主提现分组查询结果:{}", JsonUtils.json(list));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //待发送消息list
        List<DriverWithdrawalMqDTO> waitSendMsgList = new ArrayList<>();
        for (WithdrawalGroupByDriverIdResp withdrawalGroupByDriverIdResp : list) {
            if (withdrawalGroupByDriverIdResp.getCount() >= req.getWithdrawalNum()) {
                DriverWithdrawalMqDTO driverWithdrawalMqDTO = new DriverWithdrawalMqDTO();
                driverWithdrawalMqDTO.setDriverId(withdrawalGroupByDriverIdResp.getDriverId());
                Date todayMorning = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
                Date freezeTime = DateUtil.addMinutes(todayMorning, req.getFreezeTime());
                driverWithdrawalMqDTO.setFreezeTime(DateUtil.getNewFormatDateString(freezeTime));
                waitSendMsgList.add(driverWithdrawalMqDTO);
            }
        }
        if(CollectionUtils.isEmpty(waitSendMsgList)){
            return;
        }
        riskOrderRealCarOwnerHtProducer.send(MqTagEnum.car_risk_real_car_owner_driver_withdrawal_tag, JsonUtils.json(waitSendMsgList), 0);
    }

}
