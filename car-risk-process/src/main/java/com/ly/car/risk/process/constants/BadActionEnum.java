package com.ly.car.risk.process.constants;

public enum BadActionEnum {

    BAD_PLAY_MOBILE(1,"打电话玩手机","同程安全中心提醒，带上耳机接打电话、聊语音，不分心、不分神，不用频繁低头看手机，安全又省心"),
    BAD_SLEEP(2,"打瞌睡闭眼","司机端播报：同程安全中心提醒，长时间行车，开窗换气通通风，一股新鲜空气进车中，提神又醒脑"),
    BAD_OVER_SPEED(3,"车速过快","同程安全中心提醒，前方虽然一路畅通，还是要慢慢开，让乘客和您一起看看沿途美丽的风景，养眼又舒心"),
    BAD_SPEED_UP(4,"急加速/减速","同程安全中心提醒，道路上的司机形形色色，不要因为人和事耽误了你的好心情，一路平缓驾驶，省油又安心"),
    BAD_CHANGE_LINE(5,"频繁变道","同程安全中心提醒，频繁变道超车很有感觉，但事故发生频率增加，驾车不急不躁，安全又舒心"),
    BAD_FOLLOW_NEARLY(6,"跟车过近","同程安全中心提醒，跟车过近容易发生追尾事故，驾车保持安全车距，堵车不堵心"),

    ;

    private Integer code;
    private String name;
    private String voice;

    BadActionEnum(Integer code,String name,String voice){
        this.code = code;
        this.name = name;
        this.voice = voice;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVoice() {
        return voice;
    }

    public void setVoice(String voice) {
        this.voice = voice;
    }

    public static BadActionEnum getNameByCode(Integer code){
        for (BadActionEnum enumItem : BadActionEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem;
            }
        }
        return null;
    }
}
