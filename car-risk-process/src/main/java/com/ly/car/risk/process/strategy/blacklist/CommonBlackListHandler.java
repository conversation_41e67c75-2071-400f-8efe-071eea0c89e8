package com.ly.car.risk.process.strategy.blacklist;

import static com.ly.car.risk.process.constants.StrategySceneEnum.DISPATCHER_ACCEPT_ORDER;
import static com.ly.car.risk.process.constants.StrategySceneEnum.DRIVER_ACCEPT_ORDER;

import cn.hutool.core.collection.CollUtil;
import com.ly.car.risk.common.enums.MetricStrategyChannelEnum;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.StrategySceneEnum;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.dto.CommonCustomerParam;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

/**
 * Description of CommonBlackListHandler
 *
 * <AUTHOR>
 * @date 2025/3/25
 * @desc
 */
@Service
public class CommonBlackListHandler implements BlackListCheck{

    @Resource
    private RiskCustomerService riskCustomerService;

    @Resource
    private RiskHitService riskHitService;


    @Override
    public RiskSceneResult blackListCheck(UnifyCheckRequest request) {
        RiskCustomerRiskTypeEnum blackWhiteCheckResult = blackWhiteCheck(request);
        // 校验-1. 名单
        if (Objects.equals(blackWhiteCheckResult, RiskCustomerRiskTypeEnum.white_list)) {
            return RiskSceneResult.pass(blackWhiteCheckResult.getTip().getMsg());
        } else if (null != blackWhiteCheckResult) {
            RiskSceneResult fail = RiskSceneResult.fail(blackWhiteCheckResult.getTip().getMsg(), blackWhiteCheckResult.getCode());
            riskHitService.initHitRisk(request, fail);
            return fail;
        }
        return null;
    }

    private RiskCustomerRiskTypeEnum blackWhiteCheck(UnifyCheckRequest request) {
        
        // 全参数名单
        List<RiskCustomerManage> allCustomerList = getAllCustomerList(request);
        
        // 全参数名单经过分场景校验过滤后的名单（1v1名单不适用）
        List<RiskCustomerManage> filterCustomerList = filterByScene(request, allCustomerList);
        
        // 在普通黑名单前增加额外校验腾讯名单
        RiskCustomerRiskTypeEnum hitCustomerRiskType = specialBlackJudge(request, filterCustomerList, allCustomerList);
        if (hitCustomerRiskType != null) {
            return hitCustomerRiskType;
        }

        // 如果是司机接单，先判断1V1
        if (StringUtils.isNotBlank(request.getCarNum()) && StringUtils.isNotBlank(request.getPassengerPhone())) {
            Optional<RiskCustomerManage> optional = allCustomerList.stream().filter(p ->
                    Objects.equals(p.getRiskType(), RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode())
                    && Objects.equals(p.getCustomerValue(), request.getCarNum())
                    && Objects.equals(p.getBindUser(), request.getPassengerPhone())).findFirst();
            if (optional.isPresent()) {
                RiskCustomerManage riskCustomerManage = optional.get();
                request.setHitType(1);
                request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
                request.setCustomerValue(riskCustomerManage.getCustomerValue());
                request.setCustomerType(riskCustomerManage.getCustomerType());
                return RiskCustomerRiskTypeEnum.ban_one_to_one_list;
            }
        }

        // 白名单
        if (filterCustomerList.stream().anyMatch(p -> Objects.equals(RiskCustomerRiskTypeEnum.white_list.getCode(), p.getRiskType()))) {
            return RiskCustomerRiskTypeEnum.white_list;
        }

        // 黑名单
        List<Integer> riskTypes = StrategySceneEnum.sceneRiskType(request.getScene(), request.getProductLine());
        List<RiskCustomerManage> matchBlackList = filterCustomerList.stream().filter(p -> riskTypes.contains(p.getRiskType())).sorted(Comparator.comparing(RiskCustomerManage::getRiskType)).collect(Collectors.toList());

        if (CollUtil.isEmpty(matchBlackList)) {
            return null;
        }

        RiskCustomerManage riskCustomerManage = matchBlackList.get(0);

        // 记录命中信息
        request.setHitType(1);
        request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
        request.setCustomerValue(riskCustomerManage.getCustomerValue());
        request.setCustomerType(riskCustomerManage.getCustomerType());

        return RiskCustomerRiskTypeEnum.of(riskCustomerManage.getRiskType());

    }
    
    private List<RiskCustomerManage> getAllCustomerList(UnifyCheckRequest request) {
        CommonCustomerParam customerParam = new CommonCustomerParam();
        customerParam.setUserPhone(request.getUserPhone());
        customerParam.setPassengerCellphone(request.getPassengerPhone());
        customerParam.setMemberId(request.getMemberId());
        customerParam.setDriverCardNo(request.getCarNum());
        customerParam.setUnionId(request.getUnionId());
        customerParam.setIdCardNos(request.getCardNos());
        customerParam.setPayAccount(request.getPayAccount());
        customerParam.setDeviceId(request.getDeviceId());
        return this.riskCustomerService.getListByValueByGroup(customerParam, new Date());
    }
    
    /**
     * 根据不同场景，返回不同适用命中的名单
     */
    private List<RiskCustomerManage> filterByScene(UnifyCheckRequest request, List<RiskCustomerManage> customerManageList) {
        
        if(CollectionUtils.isEmpty(customerManageList)){
            return customerManageList;
        }
        
        StrategySceneEnum sceneEnum = StrategySceneEnum.of(request.getScene());
        if (null == sceneEnum) {
            return customerManageList;
        }
        
        Predicate<RiskCustomerManage> predicate;
        
        switch (sceneEnum) {
            case DRIVER_ACCEPT_ORDER:
            case DISPATCHER_ACCEPT_ORDER:
                predicate = customer -> Objects.equals(customer.getCustomerValue(), request.getCarNum())
                        || Objects.equals(customer.getCustomerValue(), request.getDeviceId());
                break;
            default:
                return customerManageList;
        }
        
        return customerManageList.parallelStream().filter(predicate).collect(Collectors.toList());
    }
    
    @Nullable
    private static RiskCustomerRiskTypeEnum specialBlackJudge(UnifyCheckRequest request, List<RiskCustomerManage> filterCustomerList, List<RiskCustomerManage> allCustomerList) {

        // 腾讯出行额外校验，场景5-1、5-2
        // 腾讯一对一名单->腾讯黑名单->现有名单过滤顺序->风控策略

        if (Objects.equals(request.getChannel(), MetricStrategyChannelEnum.TX_TRAVEL.code)
                && (Objects.equals(request.getScene(), DRIVER_ACCEPT_ORDER.getScene()) || Objects.equals(request.getScene(), DISPATCHER_ACCEPT_ORDER.getScene()))
        ) {
            
            // 1v1
            Optional<RiskCustomerManage> hit1v1Optional = allCustomerList.stream().filter(p ->
                    Objects.equals(RiskCustomerRiskTypeEnum.tx_ban_one_to_one_list.getCode(), p.getRiskType())
                            && (Objects.equals(request.getUserPhone(), p.getBindUser()) || Objects.equals(request.getPassengerPhone(), p.getBindUser()))
                            && Objects.equals(request.getCarNum(), p.getCustomerValue())
            ).findFirst();

            if (hit1v1Optional.isPresent()) {
                RiskCustomerManage riskCustomerManage = hit1v1Optional.get();
                // 记录命中信息
                request.setHitType(1);
                request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
                request.setCustomerValue(riskCustomerManage.getCustomerValue());
                request.setCustomerType(riskCustomerManage.getCustomerType());
                return RiskCustomerRiskTypeEnum.tx_ban_one_to_one_list;
            }

            // 黑名单
            Optional<RiskCustomerManage> hitBlackOptional = filterCustomerList.stream().filter(p ->
                    Objects.equals(RiskCustomerRiskTypeEnum.tx_black_list.getCode(), p.getRiskType()) && Objects.equals(request.getCarNum(), p.getCustomerValue())
            ).findFirst();
            if (hitBlackOptional.isPresent()) {
                RiskCustomerManage riskCustomerManage = hitBlackOptional.get();
                // 记录命中信息
                request.setHitType(1);
                request.setHitRule(RiskCustomerRiskTypeEnum.getMsgByCode(riskCustomerManage.getRiskType()));
                request.setCustomerValue(riskCustomerManage.getCustomerValue());
                request.setCustomerType(riskCustomerManage.getCustomerType());
                return RiskCustomerRiskTypeEnum.tx_black_list;
            }
        }
        return null;
    }
}