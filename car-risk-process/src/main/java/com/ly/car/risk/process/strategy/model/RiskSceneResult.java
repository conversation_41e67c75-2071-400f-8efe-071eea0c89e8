package com.ly.car.risk.process.strategy.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Description of RiskStrategyResult
 *
 * <AUTHOR>
 * @date 2024/6/4
 * @desc
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskSceneResult {

    private boolean riskFlag;

    private List<String> matchedStrategy = new ArrayList<>();

    /**
     * 冻结到期时间 --- yyyy-MM-dd HH:mm:ss
     */
    private String freezeTime;

    /**
     * 司机提现信息
     */
    private DriverRiskInfo driverRiskInfo;

    private String riskMsg;
    
    private Integer customerType;

    private List<RiskStrategyRespResult> matchedStrategyList = new ArrayList<>();

    public static RiskSceneResult pass(String msg){
        RiskSceneResult result = new RiskSceneResult();
        result.setRiskFlag(false);
        result.setRiskMsg(msg);
        return result;
    }

    public static RiskSceneResult fail(String msg){
        RiskSceneResult result = new RiskSceneResult();
        result.setRiskFlag(true);
        result.setRiskMsg(msg);
        return result;
    }
    
    public static RiskSceneResult fail(String msg, Integer customerType){
        RiskSceneResult result = new RiskSceneResult();
        result.setRiskFlag(true);
        result.setRiskMsg(msg);
        result.setCustomerType(customerType);
        return result;
    }
}