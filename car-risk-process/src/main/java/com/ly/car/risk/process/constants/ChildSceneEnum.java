package com.ly.car.risk.process.constants;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;


public enum ChildSceneEnum {
    /**
     * 风险子场景 1-司推乘返佣 2-其他
     */
    REVENUE_SAVE_CENTER("1-1","省钱中心"),
    MILEAGE_CONVERSION("1-2","里程兑换"),
    USER_INVITE_USER("1-3","同程乘客推乘客"),

    OFF_LINE_RAKE_ACK("3-1", "司推乘返佣"),
    OFF_LINE_CHANNEL_BRUSH("3-3","渠道刷单"),
    OFF_LINE_SPECIAL_CAR("3-4","专车场景"),
    OFF_LINE_SFC("3-5","顺风车场景"),

    PLACE_ORDER_RECEIVE_ORDER("2-1","司机接单"),
    PLACE_ORDER_USER_RESERVE("2-2","用户预订"),
    PLACE_ORDER_USER_ORDER("2-3","用户下单"),
    INVITE_DRIVER("2-4","邀请车主"),
    PLACE_ORDER_CREATE_ORDER("2-5","创单"),
    USER_CANCEL_ORDER("2-6","用户取消"),
    USER_CANCEL_ORDER_AFTER("2-7","用户取消后流程"),


    OTHER("4-1", "其他"),
    SEARCH_PRICE_USER("5-1","用户询价"),
    SEARCH_PRICE_USER_LIMIT("5-2","用户询价限制"),
    CREATE_ORDER_USER("6-1","用户创单"),

    SELF_HITCH_REGISTER("7-1","司机注册"),
    SELF_HITCH_CERTIFICATION("7-2","司机认证"),
    SELF_HITCH_LOGIN("7-3","司机登录"),
    SELF_HITCH_RELEASE("7-4","司机发布"),
    SELF_HITCH_RECEIVE_ORDER("7-5","司机接单"),
    SELF_HITCH_FINISH_ORDER("7-6","司机完单"),
    SELF_HITCH_CASH("7-7","司机提现"),
    SELE_HITCH_INVITE("7-8","司机邀请"),
    SELE_HITCH_CASH_BACK("7-9","司机返现"),
    SELF_HITCH_UPDATE_INFO("7-10","修改个人信息"),
    SELF_HITCH_BAND_CARD("7-11","解绑卡"),

    SENSITIVE_INFO_TEXT("8-1","文字涉敏"),
    SENSITIVE_INFO_PIC("8-2","图片涉敏"),
    SENSITIVE_INFO_VOICE("8-3","语音涉敏"),
    SENSITIVE_INFO_FREQUENCY("8-4","敏感信息频次过高"),
    SENSITIVE_INFO_FREQUENCY_LIMIT("8-5","敏感信息限制频次过高"),

    SAFE_WARNING("9-1","安全预警"),

    MT_REGISTER("11-1","司机注册"),
    MT_CERTIFICATION("11-2","司机认证"),
    MT_LOGIN("11-3","司机登录"),
    MT_DRIVER_RECEIVE_ORDER("11-4","司机接单"),
    MT_SAFE("11-5","行中安全"),
    MT_DRIVER_CASH("11-6","司机提现"),
    MT_UPDATE_BLACK("11-7","修改银行卡信息"),

    YANG_ZHAO_CASH("10-1","扬招司机提现"),

    COMMON_DRIVER_CERTIFICATION("12-1","司机认证"),


    ;

    private String code;
    private String msg;

    ChildSceneEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(String code) {
        for (ChildSceneEnum enumItem : ChildSceneEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }

    public static List<String> getAllChildStr(Integer mainStr){
        List<String> childStr = new ArrayList<>();
        for(ChildSceneEnum item : ChildSceneEnum.values()){
            if(Integer.valueOf(item.getCode().split("-")[0]).equals(mainStr)){
                childStr.add(item.getCode());
            }
        }
        return childStr;
    }
}
