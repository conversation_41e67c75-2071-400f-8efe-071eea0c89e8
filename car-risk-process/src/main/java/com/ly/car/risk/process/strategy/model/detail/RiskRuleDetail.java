package com.ly.car.risk.process.strategy.model.detail;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Description of RiskRuleDetail
 *
 * <AUTHOR>
 * @date 2024/6/4
 * @desc 规则
 */
@Data
public class RiskRuleDetail {
    // 规则id
    private Long ruleId;
    // 规则编号
    private String ruleNo;
    // 规则脚本
    private String script;
    // 规则数据 数组，实体为"left":"characteristic" "operator":">" "type":"1" "right":"characteristic"
    private String ruleJson;
    // 规则表达式
    private String expression;
    // 指标
    private List<RiskFieldDetail> riskFields = new ArrayList<>();

}