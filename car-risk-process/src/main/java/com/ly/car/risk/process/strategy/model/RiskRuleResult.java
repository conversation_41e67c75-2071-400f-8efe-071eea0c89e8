package com.ly.car.risk.process.strategy.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description of RiskRuleResult
 *
 * <AUTHOR>
 * @date 2024/6/11
 * @desc
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskRuleResult {
    /**
     * 规则id
     */
    private Long ruleId;
    /**
     * 规则编号
     */
    private String ruleNo;
    /**
     * 规则结果
     */
    private Boolean ruleMatched;
}