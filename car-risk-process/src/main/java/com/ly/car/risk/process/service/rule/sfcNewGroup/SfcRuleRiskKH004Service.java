package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRiskKH004Service extends FilterSfcHandler{
    private static final String ruleNo = "KH-004";

    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        log.info("[FilterRuleChain][SfcRuleRiskKH004Service][][]前置判断已通过，进入规则空号KH004判断{}",context.getUserPhone());
        if(!context.getSfcRiskRuleConfig().getOnOffKH004()){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
            return;
        }
        if(StringUtils.isNotBlank(context.getUserPhone())){
            String phone = context.getUserPhone().substring(context.getUserPhone().length()-8);
            boolean allSameDigit = true; // 是否所有数字相同的标志
            char firstDigit = phone.charAt(0); // 第一个数字
            for (int i = 1; i < phone.length(); i++) {
                if (phone.charAt(i) != firstDigit) { // 如果有数字和第一个数字不同
                    allSameDigit = false; // 标志设为false
                    break; // 不需要再继续比较了
                }
            }
            if(allSameDigit){
                List<String> orderIds = new ArrayList<>();
                orderIds.add(context.getOrderId());
                distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                if(StringUtils.isBlank(context.getRuleNo())){
                    context.setRuleNo(ruleNo);
                } else {
                    context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                }
                riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过KH004",ruleNo,null);
                context.getUiResult().setData(dto);
                context.getUiResult().setMsg("风控不通过");
            }
        }

        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }

    }
}
