package com.ly.car.risk.process.supplier;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.risk.process.supplier.yueyue.YueYueDisposeRsp;
import org.springframework.stereotype.Service;

@Service
public abstract class SupplierYueYueClientAbstract implements SupplierClient{

    public abstract void syncDisposeResult(JSONObject jsonObject);

    public abstract String syncAppeal(JSONObject jsonObject);

    public abstract void syncAppealResult(JSONObject jsonObject);

}
