package com.ly.car.risk.process.controller;

import com.alibaba.fastjson.JSON;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.controller.request.BatchBlackMtQueryRequest;
import com.ly.car.risk.process.controller.request.BatchCarBlackInfoQueryRequest;
import com.ly.car.risk.process.controller.request.BlackSyncRequest;
import com.ly.car.risk.process.controller.request.CarBlackInfo;
import com.ly.car.risk.process.controller.request.CarMtBlackInfo;
import com.ly.car.risk.process.controller.request.DriverBlackListRequest;
import com.ly.car.risk.process.controller.request.DriverBlackRequest;
import com.ly.car.risk.process.controller.request.DriverCheckInRequest;
import com.ly.car.risk.process.controller.request.DriverRemoveRequest;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.service.BlackListService;
import com.ly.car.risk.process.support.UiResultWrapper;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.sof.utils.common.UUID;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 黑名单操作
 *
 * <AUTHOR>
 * @since 2024/3/5 14:42
 **/
@RequestMapping("blackList")
@RestController
@Slf4j
public class BlackListApiController {

    @Resource
    private BlackListService service;

    /**
     * 用户拉黑司机
     * riskType：1v1
     */
    @RequestMapping("/driver/black")
    public UiResultWrapper blackDriver(@RequestBody DriverBlackRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getPassengerCellphone());
        LoggerUtils.info(log, "用户拉黑司机，req:{}", JSON.toJSONString(request));
        try {
            UiResult uiResult = service.blackDriver(request);
            LoggerUtils.info(log, "用户拉黑司机,resp:{}", JSON.toJSONString(uiResult));
            return UiResultWrapper.convert(uiResult);
        } catch (BizException e) {
            LoggerUtils.warn(log, "用户拉黑司机,业务异常", e);
            return UiResultWrapper.fail(e.getCode(), "系统异常:" + e.getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "用户拉黑司机,未知异常", e);
            return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }

    /**
     * 用户取消拉黑司机
     * riskType：1v1
     */
    @RequestMapping("/driver/remove")
    public UiResultWrapper removeDriver(@RequestBody DriverRemoveRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getPassengerCellphone());
        LoggerUtils.info(log, "用户拉黑司机列表, req:{}", JSON.toJSONString(request));
        try {
            UiResult uiResult = service.removeDriver(request);
            LoggerUtils.info(log, "用户拉黑司机列表,resp:{}", JSON.toJSONString(uiResult));
            return UiResultWrapper.convert(uiResult);
        } catch (Exception e) {
            LoggerUtils.error(log, "用户拉黑司机列表,未知异常", e);
            return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }

    /**
     * 用户拉黑司机列表
     * riskType：1v1
     */
    @RequestMapping("/driver/list")
    public UiResultWrapper listDriver(@RequestBody DriverBlackListRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getPassengerCellphone());
        LoggerUtils.info(log, "用户拉黑司机列表, req:{}", JSON.toJSONString(request));
        try {
            UiResult uiResult = service.listDriver(request);
            LoggerUtils.info(log, "用户拉黑司机列表,resp:{}", JSON.toJSONString(uiResult));
            return UiResultWrapper.convert(uiResult);
        } catch (Exception e) {
            LoggerUtils.error(log, "用户拉黑司机列表,未知异常", e);
            return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }

    /**
     * 司机是否被拉黑过
     * riskType：1v1
     */
    @RequestMapping("/driver/checkIn")
    public UiResultWrapper checkDriverIn(@RequestBody DriverCheckInRequest request) {
        LoggerUtils.setFilter(request.getTraceId(), request.getOrderId());
        LoggerUtils.info(log, "用户拉黑司机列表, req:{}", JSON.toJSONString(request));
        try {
            UiResult uiResult = service.checkDriverIn(request);
            LoggerUtils.info(log, "用户拉黑司机列表,resp:{}", JSON.toJSONString(uiResult));
            return UiResultWrapper.convert(uiResult);
        } catch (Exception e) {
            LoggerUtils.error(log, "用户拉黑司机列表,未知异常", e);
            return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }


    /**
     * 司机是否被拉黑过
     */
    @RequestMapping("/batchQueryDriverBlack")
    public UiResultWrapper batchQueryDriverBlack(@RequestBody BatchCarBlackInfoQueryRequest request) {
        String traceId = StringUtils.defaultIfBlank(request.getTraceId(), UUID.generateFormatedRandomUUID());
        LoggerUtils.setFilter(traceId, "");
        LoggerUtils.info(log, "批量车牌黑名单查询, req:{}", JSON.toJSONString(request));
        try {
            List<CarBlackInfo> blackList = service.batchQueryDriverBlack(request);
            LoggerUtils.info(log, "批量车牌黑名单查询,resp:{}", JSON.toJSONString(blackList));
            return UiResultWrapper.ok(blackList);
        } catch (BizException e ){
            LoggerUtils.error(log, "批量车牌黑名单查询,业务异常", e);
            return UiResultWrapper.fail(500, "业务异常:" + e.getMessage());
        }catch (Exception e) {
            LoggerUtils.error(log, "批量车牌黑名单查询,未知异常", e);
            return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }
    
    /**
     * 萌艇司机名单查询接口
     */
    @RequestMapping("/batchQueryMtDriverBlack")
    public UiResultWrapper batchQueryMtDriverBlack(@RequestBody BatchBlackMtQueryRequest request) {
        String traceId = StringUtils.defaultIfBlank(request.getTraceId(), UUID.generateFormatedRandomUUID());
        LoggerUtils.setFilter(traceId, "");
        LoggerUtils.info(log, "萌艇批量车牌黑名单查询, req:{}", JSON.toJSONString(request));
        try {
            List<CarMtBlackInfo> blackList = service.batchQueryMtDriverBlack(request);
            LoggerUtils.info(log, "萌艇批量车牌黑名单查询,resp:{}", JSON.toJSONString(blackList));
            return UiResultWrapper.ok(blackList);
        } catch (BizException e ){
            LoggerUtils.error(log, "萌艇批量车牌黑名单查询,业务异常", e);
            return UiResultWrapper.fail(500, "业务异常:" + e.getMessage());
        }catch (Exception e) {
            LoggerUtils.error(log, "萌艇批量车牌黑名单查询,未知异常", e);
            return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }
    
    /**
     * 同步黑名单
     */
    @RequestMapping("/sync")
    public UiResultWrapper sync(@RequestBody BlackSyncRequest request) {
        String traceId = StringUtils.defaultIfBlank(request.getTraceId(), UUID.generateFormatedRandomUUID());
        LoggerUtils.setFilter(traceId, "");
        LoggerUtils.info(log, "批量同步外部黑名单, req:{}", JSON.toJSONString(request));
        try {
            service.syncDriverBlack(request);
            return UiResultWrapper.ok();
        } catch (Exception e) {
            LoggerUtils.error(log, "批量同步外部黑名单,未知异常", e);
            return UiResultWrapper.fail(500, "系统异常:" + e.getMessage());
        } finally {
            LoggerUtils.removeAll();
        }
    }
}
