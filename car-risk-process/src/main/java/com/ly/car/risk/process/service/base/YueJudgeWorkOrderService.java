package com.ly.car.risk.process.service.base;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.YueJudgeWorkOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.YueJudgeWorkOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class YueJudgeWorkOrderService {

    @Resource
    private YueJudgeWorkOrderMapper yueJudgeWorkOrderMapper;

    public YueJudgeWorkOrder queryByOrderId(String orderId){
        return this.yueJudgeWorkOrderMapper.selectOne(new QueryWrapper<YueJudgeWorkOrder>().eq("tc_order_id",orderId).last("limit 1"));
    }
}
