package com.ly.car.risk.process.service;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.risk.mapper.MetricSceneMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.MetricScene;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MetricSceneService {
    
    @Resource
    private MetricSceneMapper sceneMapper;
    
    public Pair<MetricScene, MetricScene> getByNo(String parentSceneNo, String sceneNo) {
        MetricScene parent = sceneMapper.selectOne(new QueryWrapper<MetricScene>().eq("scene_no", parentSceneNo).eq("parent_id", "0"));
        if (parent == null) {
            return null;
        }
        
        MetricScene child = sceneMapper.selectOne(new QueryWrapper<MetricScene>().eq("parent_id", parent.getId()).eq("scene_no", sceneNo));
        return new Pair<>(parent, child);
    }
}
