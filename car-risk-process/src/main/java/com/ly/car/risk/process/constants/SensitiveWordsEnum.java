package com.ly.car.risk.process.constants;

public enum SensitiveWordsEnum {

    Violence_terrorism(1,"暴恐"),
    Advertising_law(2,"广告法"),
    Gambling_drugs(3,"黄赌毒"),
    superstition_cult(4,"迷信邪教"),
    Sensitive_words(5,"行业禁忌"),
    Jump_platform(6,"跳出平台"),
    Illegal_prohibited(7,"违法违禁"),
    Politics_names(8,"政治相关和人名")
    ;

    public Integer code;
    public String msg;
    SensitiveWordsEnum(Integer code, String msg){
        this.msg = msg;
        this.code = code;
    }


}
