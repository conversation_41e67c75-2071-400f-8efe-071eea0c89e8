package com.ly.car.risk.process.service;

import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_AWARD_BLACK_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_AWARD_WHITE_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_INVITE_BLACK_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_INVITE_WHITE_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_LOGIN_BLACK_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_LOGIN_WHITE_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_PUBLISH_BLACK_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_PUBLISH_WHITE_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_RECEIVE_ORDER_BLACK_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_RECEIVE_ORDER_WHITE_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_REGISTER_BLACK_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_REGISTER_WHITE_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_WITHDRAW_BLACK_LIST;
import static com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum.DISABLE_WITHDRAW_WHITE_LIST;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.ban_one_to_one_list;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.ban_receive_list;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.ban_ync_receive_list;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.black_list;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.tx_ban_one_to_one_list;
import static com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum.tx_black_list;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.order.entity.SfcOrder;
import com.ly.car.order.entity.SfcSupplierOrder;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.constants.BlackTypeEnum;
import com.ly.car.risk.process.constants.HcCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.RiskCustomerCustomerTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerOptionTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskCustomerStatusEnum;
import com.ly.car.risk.process.constants.RiskCustomerTtlEnum;
import com.ly.car.risk.process.controller.dto.BlackInfoManageDTO;
import com.ly.car.risk.process.controller.request.BatchBlackMtQueryRequest;
import com.ly.car.risk.process.controller.request.BatchCarBlackInfoQueryRequest;
import com.ly.car.risk.process.controller.request.BlackSyncRequest;
import com.ly.car.risk.process.controller.request.BlackSyncRequest.DriverBlackVO;
import com.ly.car.risk.process.controller.request.CarBlackInfo;
import com.ly.car.risk.process.controller.request.CarMtBlackInfo;
import com.ly.car.risk.process.controller.request.DriverBlackFromManageRequest;
import com.ly.car.risk.process.controller.request.DriverBlackListRequest;
import com.ly.car.risk.process.controller.request.DriverBlackRequest;
import com.ly.car.risk.process.controller.request.DriverCheckInRequest;
import com.ly.car.risk.process.controller.request.DriverRemoveRequest;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.repo.order.mapper.SfcOrderMapper;
import com.ly.car.risk.process.repo.order.mapper.SfcSupplierOrderMapper;
import com.ly.car.risk.process.repo.risk.mapper.HcCustomerMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerManageMapper;
import com.ly.car.risk.process.repo.risk.mapper.RiskCustomerRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.HcCustomer;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskCustomerRecord;
import com.ly.car.risk.process.service.dto.RiskCustomerManageListDTO;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.utils.CheckUtil;
import com.ly.car.sharding.order.entity.OrderAddress;
import com.ly.car.sharding.order.entity.OrderInfo;
import com.ly.car.sharding.order.mapper.OrderAddressMapper;
import com.ly.car.sharding.order.mapper.OrderInfoMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/**
 * 黑名单操作
 * <AUTHOR>
 * @since 2024/3/5 14:54
 **/
@Slf4j
@Service
public class BlackListService {

    private static final FastDateFormat fullFmt = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
    @Resource
    private RiskCustomerManageMapper riskCustomerManageMapper;
    @Resource
    private SfcOrderMapper           sfcOrderMapper;
    @Resource
    private SfcSupplierOrderMapper   sfcSupplierOrderMapper;
    @Resource
    private RiskCustomerRecordMapper riskCustomerRecordMapper;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient           redissonClient;
    @Resource
    private CarOrderService          carOrderService;
    @Resource
    private OrderInfoMapper          orderInfoMapper;
    @Resource
    private OrderAddressMapper       orderAddressMapper;
    @Resource
    private HcCustomerMapper         hcCustomerMapper;

    private List<Integer> driverBlackTypes = Stream.of(black_list.getCode(),ban_receive_list.getCode(), ban_ync_receive_list.getCode()).collect(Collectors.toList());
    
    private List<Integer> mtDriverBlackTypes = Lists.newArrayList(black_list.getCode(), ban_receive_list.getCode());


    /**
     * 用户拉黑司机
     * riskType：1v1
     */
    public UiResult blackDriver(DriverBlackRequest request) throws BizException {
        UiResult uiResult = UiResult.ok();
        CheckUtil.checkNotBlank(request.getPassengerCellphone(),"乘车人手机号不可为空");
        CheckUtil.checkNotBlank(request.getDriverCardNo(),"车牌号不可为空");
/*

        List<RiskCustomerManage> manages = this.riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .eq("bind_user", request.getPassengerCellphone())
                .eq("customer_value", request.getDriverCardNo())
                .gt("invalid_time", new Date())
        );

        */

        // 查看当前司机车牌是否已存在（车牌、客户类型、名单类型、数据有效期）
        List<RiskCustomerManage> manages = riskCustomerManageMapper.selectList(
                new QueryWrapper<RiskCustomerManage>()
                        .eq("bind_user", request.getPassengerCellphone())
                        .eq("customer_value", request.getDriverCardNo())
                        .eq("risk_type", ban_one_to_one_list.getCode() ) // 客户类型
                        .eq("customer_type", RiskCustomerCustomerTypeEnum.car_number.getCode())// 名单类型
                        .eq("status", 1) // 有效名单
                        .gt("invalid_time", new Date())
        );

        manages = manages.stream().sorted(Comparator.comparing(RiskCustomerManage::getInvalidTime).reversed()).collect(Collectors.toList());
        RiskCustomerManage manage = CollectionUtils.isEmpty(manages) ? null : manages.get(0);

        //有效期内存在，则更新过期时间
        if (manage != null) {
            manage.setInvalidTime(DateUtil.addMonth(new Date(), 12));
            manage.setUpdateTime(new Date());
            manage.setBindOrder(request.getOrderId());
            if(StringUtils.isNotBlank(request.getSupplierName())){
                manage.setSupplierName(request.getSupplierName());
            }
            if(StringUtils.isNotBlank(request.getMemberId())){
                manage.setMemberId(request.getMemberId());
            }
            this.riskCustomerManageMapper.updateById(manage);
            RiskCustomerRecord record = new RiskCustomerRecord();
            record.setCustomerId(manage.getId());
            record.setOperateType(2);//修改
            record.setCreateUser(manage.getCreateUser());
            record.setOperateUser("用户");
            record.setCustomerType(ban_one_to_one_list.getCode());
            record.setRemark(request.getRemark());
            record.setCreateTime(new Date());
            record.setCustomerValue(manage.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
            return uiResult;
        }

        //先看下当前有没有拉黑的
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        riskCustomerManage.setRiskType(ban_one_to_one_list.getCode());
        riskCustomerManage.setCustomerType(RiskCustomerCustomerTypeEnum.car_number.getCode());
        riskCustomerManage.setCustomerValue(request.getDriverCardNo());
        riskCustomerManage.setStatus(RiskCustomerStatusEnum.valid.getCode());
        riskCustomerManage.setTtl(RiskCustomerTtlEnum.one_year.getCode());
        riskCustomerManage.setOptionType(RiskCustomerOptionTypeEnum.user_client.getCode());
        riskCustomerManage.setOptionName("用户");
        riskCustomerManage.setCreateUser("用户");
        riskCustomerManage.setRiskRemark(request.getRemark());
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        riskCustomerManage.setInvalidTime(DateUtil.addMonth(new Date(), 12));
        riskCustomerManage.setBindUser(request.getPassengerCellphone());//手机号
        riskCustomerManage.setBindOrder(request.getOrderId());
        riskCustomerManage.setDriverName(request.getDriverName());
        riskCustomerManage.setStartAddress(request.getStartAddress());
        riskCustomerManage.setEndAddress(request.getEndAddress());
        riskCustomerManage.setUseTime(request.getUseTime());
        riskCustomerManage.setSupplierName(StringUtils.defaultIfBlank(request.getSupplierName(),StringUtils.EMPTY));
        riskCustomerManage.setMemberId(StringUtils.defaultIfBlank(request.getMemberId(),StringUtils.EMPTY));

        //顺风车的去看下有没有driverId
        if (StringUtils.isNotBlank(request.getOrderId())) {
            // 新订单
            if (request.getOrderId().startsWith("YC")) {

                CarOrderDetail carOrderDetail = carOrderService.queryOrderDetail(request.getOrderId());

                if (null != carOrderDetail && null != carOrderDetail.getCarInfo()
                        && Objects.equals(carOrderDetail.getProductLine(), ProductLineEnum.SFC.getCode())) {
                    riskCustomerManage.setDriverId(carOrderDetail.getCarInfo().getDriverCode());
                }

            } else if (request.getOrderId().startsWith("SFC")) {
                // 旧订单
                SfcOrder sfcOrder = this.sfcOrderMapper.queryByOrderId(request.getOrderId());

                if (sfcOrder != null && StringUtils.isNotBlank(request.getOrderId())) {
                    SfcSupplierOrder sfcSupplierOrder = this.sfcSupplierOrderMapper.selectOne(new QueryWrapper<SfcSupplierOrder>()
                            .eq("order_id", sfcOrder.getOrderId())
                            .eq("supplier_order_id", sfcOrder.getSupplierOrderId())
                    );
                    riskCustomerManage.setDriverId(sfcSupplierOrder.getDriverId());
                }
            }

        }
        this.riskCustomerManageMapper.insert(riskCustomerManage);

        RiskCustomerRecord record = new RiskCustomerRecord();
        record.setCustomerId(riskCustomerManage.getId());
        record.setOperateType(1);//修改
        record.setCreateUser("用户");
        record.setOperateUser("用户");
        record.setCustomerType(ban_one_to_one_list.getCode());
        record.setRemark(request.getRemark());
        record.setCreateTime(new Date());
        record.setCustomerValue(riskCustomerManage.getCustomerValue());
        riskCustomerRecordMapper.insert(record);
        return uiResult;
    }

    /**
     * 用户取消拉黑司机
     * riskType：1v1
     */
    public UiResult removeDriver(DriverRemoveRequest request) {
        UiResult result = UiResult.ok();

        RiskCustomerManage riskCustomerManage = this.riskCustomerManageMapper.selectOne(new QueryWrapper<RiskCustomerManage>()
                .eq("bind_user", request.getPassengerCellphone())
                .eq("customer_value", request.getDriverCardNo())
                .gt("invalid_time", new Date())
        );

        if (riskCustomerManage != null) {
            riskCustomerManage.setInvalidTime(DateUtil.addMinute(new Date(), -1));
            riskCustomerManage.setUpdateTime(new Date());
            this.riskCustomerManageMapper.updateById(riskCustomerManage);

            RiskCustomerRecord record = new RiskCustomerRecord();
            record.setCustomerId(riskCustomerManage.getId());
            record.setOperateType(2);//修改
            record.setCreateUser(riskCustomerManage.getCreateUser());
            record.setOperateUser("用户");
            record.setCustomerType(ban_one_to_one_list.getCode());
            record.setRemark(request.getRemark());
            record.setCreateTime(new Date());
            record.setCustomerValue(riskCustomerManage.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
        }
        return result;

    }

    /**
     * 用户拉黑司机列表
     * riskType：1v1
     */
    public UiResult listDriver(DriverBlackListRequest request) {
        if (StringUtils.isBlank(request.getPassengerCellphone())) {
            return UiResult.ok();
        }

        UiResult result = UiResult.ok();

        List<RiskCustomerManage> manageList = this.riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .eq("bind_user", request.getPassengerCellphone())
                .gt("invalid_time", new Date())
        );

        List<RiskCustomerManageListDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(manageList)) {
            for (RiskCustomerManage manage : manageList) {
                if (StringUtils.isBlank(manage.getBindOrder()) || StringUtils.isBlank(manage.getStartAddress())) {
                    continue;
                }
                RiskCustomerManageListDTO dto = new RiskCustomerManageListDTO();
                dto.setInvalidTime(DateUtil.date2String(manage.getInvalidTime()));
                dto.setDriverName(manage.getDriverName());
                dto.setCustomerValue(manage.getCustomerValue());
                dto.setOrderId(manage.getBindOrder());
                dto.setShieldTime(DateUtil.date2String(manage.getUpdateTime()));
                dto.setStartAddress(manage.getStartAddress());
                dto.setEndAddress(manage.getEndAddress());
                dto.setUseTime(manage.getUseTime());
                dto.setDriverCardNo(manage.getCustomerValue());
                dtoList.add(dto);
            }
        }
        result.setData(dtoList);
        return result;
    }

    /**
     * 司机是否被拉黑过
     * riskType：1v1
     */
    public UiResult checkDriverIn(DriverCheckInRequest request) {
        UiResult result = UiResult.ok();
        Assert.notBlank(request.getOrderId(), "订单不能为空");
        List<RiskCustomerManage> manageList = this.riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                .eq("bind_order", request.getOrderId())
                .eq("customer_value", request.getDriverCardNo())
                .gt("invalid_time", new Date())
        );
        if (CollectionUtils.isNotEmpty(manageList)) {
            result.setData(true);
            return result;
        }
        result.setData(false);
        return result;
    }

    /**
     * 客服拉黑司机
     */
    public UiResult blackDriverFromManage(DriverBlackFromManageRequest request) {
        if (null == request || CollectionUtils.isEmpty(request.getBlackList())) {
            log.warn("[客服手动拉黑][没有拉黑对象] request: {}", JsonUtils.json(request));
            return UiResult.fail();
        }

        for (BlackInfoManageDTO dto : request.getBlackList()) {

            String lockName = "lock:blackDriverFromManage:" + dto.getDriverCardNo();
            RLock lock = null;
            try {
                lock = redissonClient.getLock(lockName);
                boolean lockFlag = lock.tryLock(2, TimeUnit.SECONDS);
                if (!lockFlag) {
                    log.warn("[客服手动拉黑][客服拉黑] 获取失败");
                    continue;
                }
                log.info("[客服手动拉黑][客服拉黑] 拉黑请求 :{}", JsonUtils.json(dto));

                int riskType = dto.getCustomerType() == 0 ? ban_one_to_one_list.getCode() : black_list.getCode();

                // 如果添加的是黑名单，需要校验是否已存在白名单，若存在，则不允许添加
                if (RiskCustomerRiskTypeEnum.isBlackList(riskType)
                        && existWhiteList(RiskCustomerCustomerTypeEnum.car_number.getCode(), dto.getDriverCardNo())) {
                    log.warn("[客服手动拉黑][客服拉黑] 白名单已存在，不允许重复添加");
                    continue;
                }


                // 查看当前司机车牌是否已存在（车牌、客户类型、名单类型、数据有效期）
                List<RiskCustomerManage> manageList = riskCustomerManageMapper.selectList(
                        new QueryWrapper<RiskCustomerManage>()
                                .eq("customer_value", dto.getDriverCardNo())
                                .eq("risk_type", riskType) // 客户类型
                                .eq("customer_type", RiskCustomerCustomerTypeEnum.car_number.getCode())// 名单类型
                                .eq("status", 1) // 有效名单
                                .gt("invalid_time", new Date())
                );

                RiskCustomerManage riskCustomerManage = fillProp(dto);

                // 没有历史记录 -> 新增
                if (CollectionUtils.isEmpty(manageList)) {
                    riskCustomerManageMapper.insert(riskCustomerManage);
                    RiskCustomerRecord record = new RiskCustomerRecord();
                    record.setCustomerId(riskCustomerManage.getId());
                    record.setOperateType(1);//新增
                    record.setCreateUser(riskCustomerManage.getCreateUser());
                    record.setOperateUser(riskCustomerManage.getOptionName());
                    record.setCustomerType(riskCustomerManage.getCustomerType());
                    record.setRemark(dto.getRiskRemark());
                    record.setCustomerValue(riskCustomerManage.getCustomerValue());
                    record.setCreateTime(new Date());
                    riskCustomerRecordMapper.insert(record);
                    continue;
                }

                // 入参是全部拉黑类型
                if (Objects.equals(dto.getCustomerType(), black_list.getCode())) {
                    RiskCustomerManage allBlackManage = manageList.stream()
                            .filter(e -> Objects.equals(e.getRiskType(), black_list.getCode())
                                    && StringUtils.isBlank(e.getBindUser()))
                            .findFirst()
                            .orElse(null);

                    //  期限拉黑 -> 更新数据
                    if (allBlackManage != null && !Objects.equals(allBlackManage.getTtl(), RiskCustomerTtlEnum.forever.getCode())) {
                        allBlackManage.setTtl(riskCustomerManage.getTtl());
                        allBlackManage.setUpdateTime(new Date());
                        allBlackManage.setInvalidTime(riskCustomerManage.getInvalidTime());
                        riskCustomerManageMapper.updateById(allBlackManage);

                        RiskCustomerRecord record = new RiskCustomerRecord();
                        record.setCustomerId(riskCustomerManage.getId());
                        record.setOperateType(1);//新增
                        record.setCreateUser(riskCustomerManage.getCreateUser());
                        record.setCustomerType(dto.getCustomerType() == 0 ? ban_one_to_one_list.getCode() : 1);
                        record.setCreateUser(RiskCustomerOptionTypeEnum.getMsgByCode(dto.getOptionType()));
                        record.setOperateUser(RiskCustomerOptionTypeEnum.getMsgByCode(dto.getOptionType()));
                        record.setRemark(dto.getRiskRemark());
                        record.setCreateTime(new Date());
                        record.setCustomerValue(riskCustomerManage.getCustomerValue());
                        riskCustomerRecordMapper.insert(record);
                    } else {

                        riskCustomerManageMapper.insert(riskCustomerManage);
                        RiskCustomerRecord record = new RiskCustomerRecord();
                        record.setCustomerId(riskCustomerManage.getId());
                        record.setOperateType(1);//新增
                        record.setCreateUser(riskCustomerManage.getCreateUser());
                        record.setOperateUser(RiskCustomerOptionTypeEnum.getMsgByCode(dto.getOptionType()));
                        record.setCustomerType(RiskCustomerCustomerTypeEnum.car_number.getCode());
                        record.setRemark(dto.getRiskRemark());
                        record.setCreateTime(new Date());
                        record.setCustomerValue(riskCustomerManage.getCustomerValue());
                        riskCustomerRecordMapper.insert(record);
                    }
                    continue;
                }

                // 已经在黑名单，类型为1V1
                RiskCustomerManage allBlackManage = manageList.stream()
                        .filter(e -> Objects.equals(e.getRiskType(), ban_one_to_one_list.getCode())
                                && StringUtils.isNotBlank(e.getBindUser()))
                        .filter(e -> e.getBindUser().equals(dto.getBindUser()))
                        .findFirst()
                        .orElse(null);

                if (allBlackManage == null) {
                    riskCustomerManageMapper.insert(riskCustomerManage);
                    RiskCustomerRecord record = new RiskCustomerRecord();
                    record.setCustomerId(riskCustomerManage.getId());
                    record.setOperateType(1);//修改
                    record.setCreateUser(riskCustomerManage.getCreateUser());
                    record.setOperateUser(RiskCustomerOptionTypeEnum.getMsgByCode(dto.getOptionType()));
                    record.setCustomerType(dto.getCustomerType() == 0 ? ban_one_to_one_list.getCode() : 1);
                    record.setRemark(dto.getRiskRemark());
                    record.setCreateTime(new Date());
                    record.setCustomerValue(riskCustomerManage.getCustomerValue());
                    riskCustomerRecordMapper.insert(record);
                    continue;
                } else {
                    //不是永久黑名单才更新
                    allBlackManage.setTtl(riskCustomerManage.getTtl());
                    allBlackManage.setUpdateTime(new Date());
                    allBlackManage.setInvalidTime(riskCustomerManage.getInvalidTime());
                    riskCustomerManageMapper.updateById(allBlackManage);

                    RiskCustomerRecord record = new RiskCustomerRecord();
                    record.setCustomerId(allBlackManage.getId());
                    record.setOperateType(2);//修改
                    record.setCreateUser(riskCustomerManage.getCreateUser());
                    record.setOperateUser(RiskCustomerOptionTypeEnum.getMsgByCode(dto.getOptionType()));
                    record.setCustomerType(dto.getCustomerType() == 0 ? ban_one_to_one_list.getCode() : 1);
                    record.setRemark(dto.getRiskRemark());
                    record.setCreateTime(new Date());
                    record.setCustomerValue(riskCustomerManage.getCustomerValue());
                    riskCustomerRecordMapper.insert(record);
                    continue;
                }
            } catch (Exception e) {
                log.error("[客服手动拉黑][客服拉黑] 拉黑异常 :{}", JsonUtils.json(dto), e);
            } finally {
                if (lock != null && lock.isLocked()) {
                    lock.unlock();
                }
            }
        }
        return UiResult.ok();
    }

    /**
     * 校验是否存在白名单数据，存在则返回true ,否则返回false
     * @param customerType
     * @param customerValue
     * @return
     */
    private boolean existWhiteList(int customerType, String customerValue) {
        return existList(customerType, customerValue,RiskCustomerRiskTypeEnum.white_list.getCode());
    }

    /**
     * 校验是是否存在指定数据，存在则返回true ,否则返回false
     * @param customerType
     * @param customerValue
     * @return
     */
    private boolean existList(int customerType, String customerValue, int riskType) {
        long totalCount = riskCustomerManageMapper.getValidCount(customerType, customerValue, riskType);
        return totalCount > 0;
    }

    private RiskCustomerManage fillProp(BlackInfoManageDTO dto) {
        String startAddress = "";
        String endAddress = "";
        Date useTime = null;
        String driverId = "";
        RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
        if (StringUtils.isNotBlank(dto.getOrderId())) {
            // 新订单
            if (dto.getOrderId().startsWith("YC")) {
                CarOrderDetail order = carOrderService.queryOrderDetail(dto.getOrderId());
                if (null != order) {
                    if (null != order.getOrderTrip()) {
                        startAddress = order.getOrderTrip().getDepartureAddress();
                        endAddress = order.getOrderTrip().getArrivalAddress();
                    }
                    if (null != order.getBaseInfo()) {
                        useTime = order.getBaseInfo().getGmtUsage();
                    }
                    if (null != order.getCarInfo()) {
                        driverId = order.getCarInfo().getDriverCode();
                    }
                }
            } else {
                // 旧订单
                if (dto.getOrderId().startsWith("YNC") || dto.getOrderId().startsWith("GNC")) {
                    OrderInfo orderInfo = orderInfoMapper.findByOrderId(dto.getOrderId());
                    OrderAddress orderAddress = orderAddressMapper.findByOrderId(dto.getOrderId());
                    if (orderAddress != null) {
                        startAddress = orderAddress.getStartAddress();
                        endAddress = orderAddress.getEndAddress();
                    }
                    if (orderInfo != null) {
                        useTime = orderInfo.getUseTime();
                    }
                } else {
                    SfcOrder sfcOrder = sfcOrderMapper.queryByOrderId(dto.getOrderId());
                    if(null != sfcOrder){
                        OrderAddress orderAddress = orderAddressMapper.findByOrderId(dto.getOrderId());
                        SfcSupplierOrder sfcSupplierOrder = sfcSupplierOrderMapper.selectOne(new QueryWrapper<SfcSupplierOrder>()
                                .eq("order_id", sfcOrder.getOrderId()).eq("supplier_order_id", sfcOrder.getSupplierOrderId())
                        );
                        if (orderAddress != null) {
                            startAddress = orderAddress.getStartAddress();
                            endAddress = orderAddress.getEndAddress();
                        }
                        useTime = sfcOrder.getUseTime();
                        if (sfcSupplierOrder != null) {
                            driverId = sfcSupplierOrder.getDriverId();
                        }
                    }
                }
            }
        }

        riskCustomerManage.setRiskType(dto.getCustomerType() == 0 ? ban_one_to_one_list.getCode() : black_list.getCode());
        riskCustomerManage.setCustomerType(RiskCustomerCustomerTypeEnum.car_number.getCode());
        riskCustomerManage.setCustomerValue(dto.getDriverCardNo());
        riskCustomerManage.setStatus(RiskCustomerStatusEnum.valid.getCode());
        riskCustomerManage.setTtl(dto.getCustomerType() == 0 ? RiskCustomerTtlEnum.one_year.getCode() : RiskCustomerTtlEnum.forever.getCode());
        riskCustomerManage.setOptionType(RiskCustomerOptionTypeEnum.user_server.getCode());
        riskCustomerManage.setOptionName(StringUtils.isNotBlank(dto.getOptionName()) ? dto.getOptionName() : RiskCustomerOptionTypeEnum.user_server.getMsg());
        riskCustomerManage.setRiskRemark(dto.getRiskRemark());
        riskCustomerManage.setCreateTime(new Date());
        riskCustomerManage.setUpdateTime(new Date());
        riskCustomerManage.setInvalidTime(dto.getCustomerType() == 0 ? DateUtil.addMonth(new Date(), 12) : DateUtil.string2Date("2099-12-31 23:59:59"));
        riskCustomerManage.setBindUser(dto.getBindUser());
        riskCustomerManage.setBindOrder(dto.getOrderId());
        riskCustomerManage.setBlackType(Integer.parseInt(dto.getShieldingType()));
        riskCustomerManage.setBlackTypeName(BlackTypeEnum.getNameByCode(riskCustomerManage.getBlackType()));
        riskCustomerManage.setBlackChildType(dto.getShieldingTypeChild());
        riskCustomerManage.setDriverName(dto.getDriverName());
        riskCustomerManage.setStartAddress(startAddress);
        riskCustomerManage.setEndAddress(endAddress);
        riskCustomerManage.setUseTime(null == useTime ? null : DateUtil.date2String(useTime));
        riskCustomerManage.setDriverId(driverId);
        riskCustomerManage.setCreateUser(riskCustomerManage.getOptionName());
        riskCustomerManage.setSupplierName(dto.getSupplierName());
        riskCustomerManage.setMemberId(dto.getMemberId());
        return riskCustomerManage;
    }

    public List<CarBlackInfo> batchQueryDriverBlack(BatchCarBlackInfoQueryRequest request) throws BizException {
        if(CollUtil.isEmpty(request.getCarNums())){
            return Lists.newArrayList();
        }
        if(request.getCarNums().size() > 500){
            throw new BizException(-1,"查询数量过大");
        }
        Date now = new Date();
        //先查下用户端的名单
        List<String> carNums = request.getCarNums();
        List<RiskCustomerManage> customerManageList;
        if(BooleanUtil.isTrue(request.getQueryInvalidFlag())){
            customerManageList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                    .in("customer_value", carNums));
        }else{
            customerManageList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                    .in("customer_value", carNums)
                    .gt("invalid_time", now));
        }

        //筛选出有效白名单
        List<RiskCustomerManage> whiteTcCustomerList = customerManageList.stream()
                .filter(data->data.getRiskType()==2 && data.getInvalidTime().after(now))
                .collect(Collectors.toList());
        List<String> whiteCustomerList = whiteTcCustomerList.stream().map(RiskCustomerManage::getCustomerValue).collect(Collectors.toList());

        //找出黑名单且不包含白名单的
        return customerManageList.stream()
                .filter(data -> (driverBlackTypes.contains(data.getRiskType()) && !whiteCustomerList.contains(data.getCustomerValue()))
                        || (data.getRiskType() == 7))
                .map(data -> assembleBlackInfo(data)).collect(Collectors.toList());

    }


    private CarBlackInfo assembleBlackInfo(RiskCustomerManage data) {
        CarBlackInfo blackInfo = new CarBlackInfo();
        blackInfo.setId(data.getId());
        blackInfo.setCarNum(data.getCustomerValue());
        blackInfo.setShieldTime(fullFmt.format(data.getCreateTime()));
        blackInfo.setInvalidTime(fullFmt.format(data.getInvalidTime()));
        blackInfo.setTtl(data.getTtl());
        blackInfo.setRiskRemark(data.getRiskRemark());
        blackInfo.setRiskType(data.getRiskType());
        blackInfo.setOptionType(data.getOptionType());
        blackInfo.setStatus(data.getStatus());
        blackInfo.setBlackChildType(data.getBlackChildType());
        blackInfo.setBindOrder(data.getBindOrder());
        return blackInfo;
    }
    
    public void syncDriverBlack(BlackSyncRequest request) {
        
        if (CollectionUtils.isEmpty(request.getDriverBlacklist()) || request.getOperationType() == null) {
            return;
        }
        
        // 操作类型
        Integer operationType = request.getOperationType();
        Integer customerType = 6;
        
        for (DriverBlackVO driverBlackVO : request.getDriverBlacklist()) {
            
            if (StringUtils.isBlank(driverBlackVO.getPlateNo())
                || null == driverBlackVO.getExpireTime()) {
                continue;
            }
            
            Integer riskType = StringUtils.isNotBlank(driverBlackVO.getPlateNo()) && StringUtils.isNotBlank(driverBlackVO.getPassengerPhone())
                    ? tx_ban_one_to_one_list.getCode() : tx_black_list.getCode();
            String customerValue = driverBlackVO.getPlateNo();
            String bindUser = StringUtils.defaultString(driverBlackVO.getPassengerPhone());
            
            List<RiskCustomerManage> oldList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                    .eq("risk_type", riskType)
                    .eq("customer_value", customerValue)
                    .eq("customer_type", customerType)
                    .eq("bind_user", bindUser)
            );
            
            switch (operationType) {
                // 新增/更新
                case 2:
                    change(driverBlackVO, customerType, customerValue, riskType, bindUser, oldList);
                    break;
                
                // 删除
                case 3:
                    delete(oldList);
                    break;
                
                default:
                    continue;
            }
        }
    }
    
    private void delete(List<RiskCustomerManage> oldList) {
        for (RiskCustomerManage riskCustomerManage : oldList) {
            if (riskCustomerManage != null && riskCustomerManage.getStatus().equals(RiskCustomerStatusEnum.valid.getCode())) {
                riskCustomerManage.setStatus(RiskCustomerStatusEnum.invalid.getCode());
                riskCustomerManage.setOptionName("腾讯出行");
                riskCustomerManage.setUpdateTime(new Date());
                riskCustomerManage.setInvalidTime(new Date());
                riskCustomerManageMapper.updateById(riskCustomerManage);
                
                RiskCustomerRecord record = new RiskCustomerRecord();
                record.setCustomerId(riskCustomerManage.getId());
                record.setOperateType(2);
                record.setCreateUser(riskCustomerManage.getCreateUser());
                record.setOperateUser(riskCustomerManage.getOptionName());
                record.setCustomerType(riskCustomerManage.getCustomerType());
                record.setRemark("同步腾讯出行删除");
                record.setCreateTime(new Date());
                record.setCustomerValue(riskCustomerManage.getCustomerValue());
                riskCustomerRecordMapper.insert(record);
            }
        }
    }
    
    private void change(DriverBlackVO driverBlackVO, Integer customerType, String customerValue, Integer riskType, String bindUser, List<RiskCustomerManage> oldList) {
        
        Optional<RiskCustomerManage> optional = oldList.stream().filter(o -> Objects.equals(o.getStatus(), RiskCustomerStatusEnum.valid.getCode())).findFirst();
        boolean updated = optional.isPresent();
        
        if (updated) {
            
            RiskCustomerManage riskCustomerManage = optional.get();
            
            if (riskCustomerManage.getInvalidTime().getTime() == driverBlackVO.getExpireTime()) {
                return;
            }
            
            riskCustomerManage.setCustomerValue(customerValue);
            riskCustomerManage.setCustomerType(customerType);
            riskCustomerManage.setRiskRemark(StringUtils.defaultString(driverBlackVO.getControlReason(), "同步腾讯"));
            riskCustomerManage.setTtl(0);
            riskCustomerManage.setRiskType(riskType);
            riskCustomerManage.setOptionType(RiskCustomerOptionTypeEnum.system.getCode());
            riskCustomerManage.setOptionName("腾讯出行");
            riskCustomerManage.setStatus(RiskCustomerStatusEnum.valid.getCode());
            riskCustomerManage.setInvalidTime(Objects.equals(driverBlackVO.getExpireTime(), -1L)
                    ? DateUtil.string2Date("2099-01-01 00:00:00")
                    : new Date(driverBlackVO.getExpireTime()));
            riskCustomerManage.setUpdateTime(new Date());
            riskCustomerManage.setBindUser(StringUtils.defaultString(bindUser));
            riskCustomerManage.setMemberId(StringUtils.EMPTY);
            riskCustomerManage.setSupplierName(StringUtils.EMPTY);
            riskCustomerManageMapper.updateById(riskCustomerManage);
            
            RiskCustomerRecord record = new RiskCustomerRecord();
            record.setCustomerId(riskCustomerManage.getId());
            record.setOperateType(2);
            record.setCreateUser(riskCustomerManage.getCreateUser());
            record.setOperateUser(riskCustomerManage.getOptionName());
            record.setCustomerType(riskCustomerManage.getCustomerType());
            record.setRemark(riskCustomerManage.getRiskRemark());
            record.setCreateTime(new Date());
            riskCustomerRecordMapper.insert(record);
        } else {
            RiskCustomerManage riskCustomerManage = new RiskCustomerManage();
            riskCustomerManage.setCustomerValue(customerValue);
            riskCustomerManage.setCustomerType(customerType);
            riskCustomerManage.setRiskRemark(StringUtils.defaultString(driverBlackVO.getControlReason(), "同步腾讯"));
            riskCustomerManage.setTtl(0);
            riskCustomerManage.setRiskType(riskType);
            riskCustomerManage.setOptionType(RiskCustomerOptionTypeEnum.system.getCode());
            riskCustomerManage.setOptionName("腾讯出行");
            riskCustomerManage.setInvalidTime(Objects.equals(driverBlackVO.getExpireTime(), -1L)
                    ? DateUtil.string2Date("2099-01-01 00:00:00")
                    : new Date(driverBlackVO.getExpireTime()));
            riskCustomerManage.setCreateUser("腾讯出行");
            riskCustomerManage.setBindUser(StringUtils.defaultString(bindUser));
            riskCustomerManage.setMemberId(StringUtils.EMPTY);
            riskCustomerManage.setSupplierName(StringUtils.EMPTY);
            riskCustomerManageMapper.insert(riskCustomerManage);
            
            RiskCustomerRecord record = new RiskCustomerRecord();
            record.setCustomerId(riskCustomerManage.getId());
            record.setOperateType(1);
            record.setCreateUser(riskCustomerManage.getCreateUser());
            record.setOperateUser(riskCustomerManage.getOptionName());
            record.setCustomerType(riskCustomerManage.getCustomerType());
            record.setRemark(riskCustomerManage.getRiskRemark());
            record.setCreateTime(new Date());
            record.setCustomerValue(riskCustomerManage.getCustomerValue());
            riskCustomerRecordMapper.insert(record);
        }
    }
    
    public List<CarMtBlackInfo> batchQueryMtDriverBlack(BatchBlackMtQueryRequest request) throws BizException {
        if (CollUtil.isEmpty(request.getCarNums())) {
            return Lists.newArrayList();
        }
        if (request.getCarNums().size() > 500) {
            throw new BizException(-1, "查询数量过大");
        }
        
        Date now = new Date();

        // mt司机黑名单 + 风控黑名单
        List<String> carNums = request.getCarNums();
        List<RiskCustomerManage> customerManageList;
        List<HcCustomer> hcCustomerList;
        if (BooleanUtil.isTrue(request.getQueryInvalidFlag())) {
            customerManageList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                    .in("customer_value", carNums));
            
            hcCustomerList = hcCustomerMapper.selectList(new QueryWrapper<HcCustomer>()
                    .in("driver_card_no", carNums));
        } else {
            customerManageList = riskCustomerManageMapper.selectList(new QueryWrapper<RiskCustomerManage>()
                    .in("customer_value", carNums)
                    .eq("status", 1)
                    .gt("invalid_time", now));
            
            hcCustomerList = hcCustomerMapper.selectList(new QueryWrapper<HcCustomer>()
                    .in("driver_card_no", carNums)
                    .gt("invalid_time", now));
        }
        
        // 筛选出有效白名单
        Set<String> whiteCustomerList = new HashSet<>();
        Map<String, List<Integer>> hcRiskTypeMap;
        customerManageList.stream()
                .filter(data -> Objects.equals(data.getRiskType(), RiskCustomerRiskTypeEnum.white_list.getCode()) && data.getInvalidTime().after(now))
                .map(RiskCustomerManage::getCustomerValue)
                .forEach(whiteCustomerList::add);
        hcCustomerList.stream()
                .filter(data -> Objects.equals(data.getCustomerType(), HcCustomerRiskTypeEnum.WHITE_LIST.getCode()) && data.getInvalidTime().after(now))
                .map(HcCustomer::getDriverCardNo)
                .forEach(whiteCustomerList::add);
        hcRiskTypeMap = hcCustomerList.stream().collect(Collectors.groupingBy(HcCustomer::getDriverCardNo, Collectors.mapping(HcCustomer::getCustomerType, Collectors.toList())));
        
        // 找出黑名单且不包含白名单的
        List<CarMtBlackInfo> resultList = new ArrayList<>();
        
        customerManageList.stream()
                .filter(data -> mtDriverBlackTypes.contains(data.getRiskType()) && !whiteCustomerList.contains(data.getCustomerValue()))
                .map(this::assembleCustomerBlackInfo)
                .forEach(resultList::add);
        
        hcCustomerList.stream()
                .filter(data -> !whiteCustomerList.contains(data.getDriverCardNo()))
                .filter(date -> {
                    List<Integer> riskTypes = hcRiskTypeMap.get(date.getDriverCardNo());
                    // 如果命中xx黑名单，又在xx白名单，则不返回
                    if (riskTypes.contains(DISABLE_WITHDRAW_BLACK_LIST.getCode()) && riskTypes.contains(DISABLE_WITHDRAW_WHITE_LIST.getCode())) {
                        return false;
                    }
                    if (riskTypes.contains(DISABLE_PUBLISH_BLACK_LIST.getCode()) && riskTypes.contains(DISABLE_PUBLISH_WHITE_LIST.getCode())) {
                        return false;
                    }
                    if (riskTypes.contains(DISABLE_RECEIVE_ORDER_BLACK_LIST.getCode()) && riskTypes.contains(DISABLE_RECEIVE_ORDER_WHITE_LIST.getCode())) {
                        return false;
                    }
                    if (riskTypes.contains(DISABLE_LOGIN_BLACK_LIST.getCode()) && riskTypes.contains(DISABLE_LOGIN_WHITE_LIST.getCode())) {
                        return false;
                    }
                    if (riskTypes.contains(DISABLE_REGISTER_BLACK_LIST.getCode()) && riskTypes.contains(DISABLE_REGISTER_WHITE_LIST.getCode())) {
                        return false;
                    }
                    if (riskTypes.contains(DISABLE_INVITE_BLACK_LIST.getCode()) && riskTypes.contains(DISABLE_INVITE_WHITE_LIST.getCode())) {
                        return false;
                    }
                    if (riskTypes.contains(DISABLE_AWARD_BLACK_LIST.getCode()) && riskTypes.contains(DISABLE_AWARD_WHITE_LIST.getCode())) {
                        return false;
                    }
                    return true;
                })
                .map(this::assembleMtBlackInfo)
                .forEach(resultList::add);
        
        return resultList;
    }
    
    private CarMtBlackInfo assembleMtBlackInfo(HcCustomer data) {
        CarMtBlackInfo blackInfo = new CarMtBlackInfo();
        blackInfo.setCarNum(data.getDriverCardNo());
        blackInfo.setShieldTime(fullFmt.format(data.getCreateTime()));
        blackInfo.setInvalidTime(fullFmt.format(data.getInvalidTime()));
        blackInfo.setTtl(data.getTtl());
        blackInfo.setRiskRemark(data.getRemark());
        blackInfo.setRiskType(data.getCustomerType());
        blackInfo.setOptionType(2);
        blackInfo.setStatus(new Date().before(data.getInvalidTime()) ? 1 : 2);
        blackInfo.setSource(2);
        return blackInfo;
    }
    
    private CarMtBlackInfo assembleCustomerBlackInfo(RiskCustomerManage data) {
        CarMtBlackInfo blackInfo = new CarMtBlackInfo();
        blackInfo.setCarNum(data.getCustomerValue());
        blackInfo.setShieldTime(fullFmt.format(data.getCreateTime()));
        blackInfo.setInvalidTime(fullFmt.format(data.getInvalidTime()));
        blackInfo.setTtl(data.getTtl());
        blackInfo.setRiskRemark(data.getRiskRemark());
        blackInfo.setRiskType(data.getRiskType());
        blackInfo.setOptionType(data.getOptionType());
        blackInfo.setStatus(data.getStatus());
        blackInfo.setSource(1);
        return blackInfo;
    }
}
