package com.ly.car.risk.process.handler.orderstate;

import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.travel.car.tradecore.model.enums.OrderState;

public interface OrderStateHandler {

    String supportType();

    void dealDraftState(CarOrderDetail orderDetail, OrderState fromState,OrderState toState);

    void dealDispatchingState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState);

    void dealCancelState(CarOrderDetail orderDetail, OrderState fromState,OrderState toState);

    void dealInTripState(CarOrderDetail orderDetail, OrderState fromState,OrderState toState);

    void dealTripFinishState(CarOrderDetail orderDetail, OrderState fromState,OrderState toState);


    void dealReceiveOrderState(CarOrderDetail orderDetail, OrderState fromState, OrderState toState);
}
