package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 2-2
 * */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk024Service extends FilterSfcHandler{

    private static final String ruleNo = "024";
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        if(context.getRuleOnOff().get(ruleNo) == null || !context.getRuleOnOff().get(ruleNo)){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
            return;
        }
        log.info("[FilterRuleChain][SfcRuleRisk024Service][][]前置判断已通过，进入规则024判断");
        //当天6分钟内的订单
        List<OrderRiskContext> orderRiskContextList = context.getUserContextList().stream().filter(data->data.getIntervalTime() != null)
                .filter(data->data.getIntervalTime() < context.getSfcRiskRuleConfig().getTime024() )
                .filter(data-> DateUtil.string2Date(data.getFinishTime()).after(TimeUtil.currentDay()))
                .collect(Collectors.toList());

        List<OrderRiskContext> orderRiskContextListCurr = context.getUserContextList().stream()
                .filter(data-> DateUtil.string2Date(data.getFinishTime()).after(TimeUtil.currentDay()))
                .collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(orderRiskContextList) && orderRiskContextList.size() > context.getSfcRiskRuleConfig().getOrderNum024()){
            List<String> stringList = orderRiskContextList.stream().map(OrderRiskContext::getDriverCardNo).distinct().collect(Collectors.toList());
            List<OrderRiskContext> orderRiskContextListThree = context.getUserContextList().stream()
                    .filter(data-> DateUtil.string2Date(data.getFinishTime()).after(TimeUtil.threeDay()))
                    .collect(Collectors.toList());
            String orderIds = StringUtils.join(orderRiskContextListThree.stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList()), ",");
            if(stringList.size() == 1 && (orderRiskContextListCurr.size() > context.getSfcRiskRuleConfig().getOrderNumCurrent024()
                    || orderRiskContextListThree.size() > context.getSfcRiskRuleConfig().getOrderNumThreeDay024())){
                log.info("[SfcRuleRisk024Service][doHandler][][]命中024规则，关联用户为{},关联订单为{}",context.getMemberId(), JsonUtils.json(orderIds));
                distributionRiskManageService.addByRuleChain(orderRiskContextList,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                RiskResultDTO dto = new RiskResultDTO(405,"风控不通过024",null,null);
                context.getUiResult().setData(dto);
                context.getUiResult().setMsg("风控不通过");

                if(StringUtils.isBlank(context.getRuleNo())){
                    context.setRuleNo(ruleNo);
                } else {
                    context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                }
                List<String> linkOrderIds = orderRiskContextList.stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList());
                riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,linkOrderIds));
            }
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
