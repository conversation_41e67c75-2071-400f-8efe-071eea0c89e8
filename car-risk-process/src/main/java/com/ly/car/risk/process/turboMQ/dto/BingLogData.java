package com.ly.car.risk.process.turboMQ.dto;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: chunming.zhou
 * @create: 2021-07-15 17:30
 **/
public class BingLogData {
    private String eventType;
    private Long executeTime;
    private Integer indicator;
    private Integer lastIndicator;
    private String schema;
    private String tableName;
    private List<Map<String, Node>> rowData;

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public Long getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Long executeTime) {
        this.executeTime = executeTime;
    }

    public Integer getIndicator() {
        return indicator;
    }

    public void setIndicator(Integer indicator) {
        this.indicator = indicator;
    }

    public Integer getLastIndicator() {
        return lastIndicator;
    }

    public void setLastIndicator(Integer lastIndicator) {
        this.lastIndicator = lastIndicator;
    }

    public String getSchema() {
        return schema;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<Map<String, Node>> getRowData() {
        return rowData;
    }

    public void setRowData(List<Map<String, Node>> rowData) {
        this.rowData = rowData;
    }

    public class Node{
        private String v;       //当前值
        private String b;       //更新前的值
        private Boolean u;      //是否更新

        public String getV() {
            return v;
        }

        public void setV(String v) {
            this.v = v;
        }

        public String getB() {
            return b;
        }

        public void setB(String b) {
            this.b = b;
        }

        public Boolean getU() {
            return u;
        }

        public void setU(Boolean u) {
            this.u = u;
        }
    }
}
