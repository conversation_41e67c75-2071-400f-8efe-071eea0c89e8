package com.ly.car.risk.process.service.rule.mtGroup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.mtticket.entity.DriverAccountRecord;
import com.ly.car.risk.process.repo.mtticket.mapper.DriverAccountRecordMapper;
import com.ly.car.risk.process.service.dto.MtDriverConfig;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class MtDriverCashService extends MtFilterHandler{

    @Resource
    private DriverAccountRecordMapper driverAccountRecordMapper;

    @Override
    public void doHandler(MtFilterContext context) {
        if(context.getParam().getString("productLine").equals("MT")){
            //查询当前账号是否在多个设备上登录
            List<DriverAccountRecord> driverAccountRecords = this.driverAccountRecordMapper.selectList(new QueryWrapper<DriverAccountRecord>()
                    .between("create_time", DateUtil.addHour(new Date(),-24),new Date())
                    .eq("driver_id", context.getParam().getString("driverId"))
            );
            List<String> deviceIds = driverAccountRecords.stream()
                    .filter(data-> StringUtils.isNotBlank(data.getDeviceId()))
                    .map(DriverAccountRecord::getDeviceId)
                    .distinct()
                    .collect(Collectors.toList());
            MtDriverConfig config = context.getParam().getObject("config",MtDriverConfig.class);
            if(deviceIds != null && deviceIds.size() > config.getLoginTimes()){
                context.getDto().setCode(1);
                context.getDto().setMessage("当前账户存在多设备登录状况，暂不允许提现");
            }
        } else {

        }

        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }

    }
}
