package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.redis.OrderPassengerCellPhone;
import com.ly.car.risk.process.service.redis.OrderStatusCancelDTO;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk041Service extends FilterSfcHandler{

    private static final String ruleNo = "041";
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;


    @Override
    public void doHandler(FilterSfcContext context) {
        log.info("[FilterRuleChain][SfcRuleRisk041Service][][]前置判断已通过，进入规则041判断{}", JsonUtils.json(context.getPhoneContextList()));
        try {
            if(!context.getSfcRiskRuleConfig().getOnOff041()){
                if(this.nextHandler != null){
                    this.nextHandler.doHandler(context);
                }
                return;
            }
            List<OrderPassengerCellPhone> dealList = context.getPhoneContextList().stream()
                    .filter(data->data.getCreateTime().after(DateUtil.addMinute(new Date(),-context.getSfcRiskRuleConfig().getTime041())))
                    .filter(data->data.getStartCityId() != null)
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(context.getPhoneContextList())){
                List<String> memberIds = context.getPhoneContextList().stream().map(OrderPassengerCellPhone::getMemberId).distinct().collect(Collectors.toList());
                List<Integer> cityNums = context.getPhoneContextList().stream().map(OrderPassengerCellPhone::getStartCityId).distinct().collect(Collectors.toList());
                if(memberIds.size() >= context.getSfcRiskRuleConfig().getMemberNum041() &&
                 cityNums.size() >= context.getSfcRiskRuleConfig().getCityNum041()){
                    List<String> orderIds = dealList.stream().map(OrderPassengerCellPhone::getOrderId).collect(Collectors.toList());
                    distributionRiskManageService.addManageCommon(orderIds,ruleNo,context.getMainScene(),context.getChildScene(),0,null, RiskLevelEnum.HIGH.getCode());
                    log.info("[SfcRuleRisk041Service][doHandler][{}][{}]命中041规则{}",context.getMemberId(),context.getUnionId(),orderIds);
                    RiskResultDTO dto = new RiskResultDTO(405,"风控不通过041",null,null);
                    context.getUiResult().setData(dto);
                    context.getUiResult().setMsg("风控不通过");

                    if(StringUtils.isBlank(context.getRuleNo())){
                        context.setRuleNo(ruleNo);
                    } else {
                        context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                    }
                    riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,orderIds));
                }
            }
        } catch (Exception e) {
            log.error("[SfcRuleRisk041Service][doHandler][{}][{}]报错信息:{}",context.getMemberId(),context.getUnionId(),e);
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
