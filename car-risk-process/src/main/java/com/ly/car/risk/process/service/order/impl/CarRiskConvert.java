package com.ly.car.risk.process.service.order.impl;

import cn.hutool.core.lang.Assert;
import com.ly.car.risk.common.enums.UnifyCheckModuleEnum;
import com.ly.car.risk.process.client.SupplierApiClient;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.StrategySceneEnum;
import com.ly.car.risk.process.controller.request.IsRiskUserRequest;
import com.ly.car.risk.process.controller.request.OrderAcceptCheckRequest;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.travel.car.ordercore.facade.model.RiskOrder;
import java.util.Collections;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class CarRiskConvert {
    
    @Resource
    private CarOrderService   carOrderService;
    @Resource
    private SupplierApiClient supplierApiClient;
    
    public UnifyCheckRequest fillParam(OrderAcceptCheckRequest request) throws BizException {
        UnifyCheckRequest req = new UnifyCheckRequest();
        // 如果查询不到订单信息，放过风控
        RiskOrder simpleCarOrder = carOrderService.queryOrderDetail(request.getOrderId(), request.getTraceId());
        if(null == simpleCarOrder){
            return null;
        }

        req.setScene(StrategySceneEnum.DRIVER_ACCEPT_ORDER.getScene());
        req.setRightsOrderFlag(OrderUtils.isRightsOrder(simpleCarOrder.getOrderTags()));
        req.setDistributionFlag(OrderUtils.isDistributionOrder(simpleCarOrder.getOrderTags()));
        req.setMemberId(simpleCarOrder.getMemberId());
        req.setUnionId(simpleCarOrder.getUnionId());
        req.setChannel(String.valueOf(simpleCarOrder.getOrderChannel()));
        req.setProductLine(StringUtils.isBlank(request.getProductLine()) ? OrderUtils.getProductLineByOrderPrefix(request.getOrderId()) : request.getProductLine());
        req.setOrderId(request.getOrderId());
        req.setUserPhone(simpleCarOrder.getContactPhone());
        req.setPassengerPhone(simpleCarOrder.getPassengerPhone());
        req.setCarNum(request.getCarNum());
        req.setIp("");
        if (StringUtils.isBlank(request.getSupplierName())) {
            req.setSupplierCode("");
        } else {
            String supplierCode = supplierApiClient.getSupplierNameMap().getOrDefault(request.getSupplierName(),"");
            req.setSupplierCode(supplierCode);
            req.setSupplierName(request.getSupplierName());
        }
        req.setTraceId(req.getTraceId());
        return req;
    }
    
    public UnifyCheckRequest fillParam(IsRiskUserRequest request) throws BizException {
        UnifyCheckRequest req = new UnifyCheckRequest();
        
        ProductLineEnum productLineEnum = ProductLineEnum.valueOf(request.getProductLine());
        Assert.notNull(productLineEnum, () -> new BizException(-1, "业务线不存在"));
        
        req.setScene(StrategySceneEnum.USER_CREATE_ORDER.getScene());
        req.setMemberId(request.getMemberId());
        req.setUnionId(request.getUnionId());
        req.setChannel(String.valueOf(request.getChannel()));
        req.setProductLine(request.getProductLine());
        req.setOrderId(request.getOrderId());
        req.setUserPhone(request.getUserPhone());
        req.setPassengerPhone(request.getPassengerCellphone());
        req.setCarNum("");
        req.setIp("");
        req.setSupplierCode("");
        req.setDistributionFlag(false);
        req.setCheckModule(Collections.singletonList(UnifyCheckModuleEnum.CUSTOMER.getCode()));
        req.setTraceId(request.getTraceId());
        return req;
    }
}
