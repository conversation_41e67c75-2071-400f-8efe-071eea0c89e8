package com.ly.car.risk.process.service.rule.mtGroup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.process.repo.hitchorder.entity.DriverLoginRecord;
import com.ly.car.risk.process.repo.hitchorder.mapper.DriverLoginRecordMapper;
import com.ly.car.risk.process.repo.mtticket.entity.DriverAccountRecord;
import com.ly.car.risk.process.repo.mtticket.mapper.DriverAccountRecordMapper;
import com.ly.car.risk.process.service.dto.MtDriverConfig;
import com.ly.car.risk.process.service.dto.RiskResultNewDTO;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope("prototype")
public class MtDriverLoginService extends MtFilterHandler{

    @Resource
    private DriverAccountRecordMapper driverAccountRecordMapper;
    @Resource
    private DriverLoginRecordMapper driverLoginRecordMapper;

    @Override
    public void doHandler(MtFilterContext context) {
        if(context.getParam().getString("productLine").equals("MT")){
            //查询当前账号是否在多个设备上登录
            List<DriverAccountRecord> driverAccountRecords = this.driverAccountRecordMapper.selectList(new QueryWrapper<DriverAccountRecord>()
                    .between("create_time", DateUtil.addHour(new Date(),-24),new Date())
                    .eq("driver_id", context.getParam().getString("driverId"))
            );
            List<String> deviceIds = driverAccountRecords.stream()
                    .filter(data->StringUtils.isNotBlank(data.getDeviceId()))
                    .map(DriverAccountRecord::getDeviceId)
                    .distinct()
                    .collect(Collectors.toList());
            MtDriverConfig config = context.getParam().getObject("config",MtDriverConfig.class);
            if(deviceIds != null && deviceIds.size() > config.getLoginTimes()){
                context.getDto().setCode(1);
                context.getDto().setMessage("当前账户存在多设备登录状况，请核实");
            }
        } else {
            List<DriverLoginRecord> driverLoginRecords = this.driverLoginRecordMapper.selectList(
                    new QueryWrapper<DriverLoginRecord>().eq("device_id", context.getParam().getString("deviceId"))
                            .gt("create_time", DateUtil.addHour(new Date(), -context.getHcSceneConfig().getScene7_3Time()))

            );
            List<DriverLoginRecord> collect = driverLoginRecords.stream().collect(
                    Collectors.collectingAndThen(Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(DriverLoginRecord::getMemberId))), ArrayList::new)
            );
            if(CollectionUtils.isNotEmpty(collect) && collect.size() > context.getHcSceneConfig().getScene7_3Num()){
                context.getDto().setCode(1);
                context.getDto().setMessage("当前账户存在多设备登录状况，请核实");
            }
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        }

    }

}
