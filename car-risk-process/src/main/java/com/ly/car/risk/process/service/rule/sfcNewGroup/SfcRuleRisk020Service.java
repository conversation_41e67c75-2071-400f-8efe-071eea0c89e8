package com.ly.car.risk.process.service.rule.sfcNewGroup;

import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.service.DistributionRiskManageService;
import com.ly.car.risk.process.service.RiskHitLinkService;
import com.ly.car.risk.process.service.RiskHitService;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.dto.HitInfoDTO;
import com.ly.car.risk.process.service.dto.OrderRiskContext;
import com.ly.car.risk.process.service.dto.RiskHitLinkDTO;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.utils.CoordUtil;
import com.ly.car.risk.process.utils.TimeUtil;
import com.ly.car.utils.JsonUtils;
import com.ly.dal.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 2-1
 * */
@Service
@Slf4j
@Scope("prototype")
public class SfcRuleRisk020Service extends FilterSfcHandler{

    private static final String ruleNo = "020";
    @Resource
    private DistributionRiskManageService distributionRiskManageService;
    @Resource
    private RiskHitService riskHitService;
    @Resource
    private RiskHitLinkService riskHitLinkService;

    @Override
    public void doHandler(FilterSfcContext context) {
        if(context.getRuleOnOff().get(ruleNo) == null || !context.getRuleOnOff().get(ruleNo)){
            if(this.nextHandler != null){
                this.nextHandler.doHandler(context);
            }
            return;
        }
        log.info("[FilterRuleChain][SfcRuleRisk020Service][][]前置判断已通过，进入规则020判断");
        //计算开始时间戳
        List<OrderRiskContext> orderRiskContextList = context.getDriverContextList().stream()
                .filter(data-> DateUtil.string2Date(data.getFinishTime()).after(TimeUtil.currentDay()))
                .collect(Collectors.toList());
        Map<String, List<OrderRiskContext>> collect = orderRiskContextList.stream()
                .filter(data -> StringUtils.isNotBlank(data.getMemberId()) && !data.getMemberId().equals("0"))
                .collect(Collectors.groupingBy(OrderRiskContext::getMemberId));

        for(Map.Entry<String,List<OrderRiskContext>> entry : collect.entrySet()){
            //计算用户下面是否有完单距离都小于2000米的 ,只要有一个大于2000米就让过
            if(entry.getValue().size() > context.getSfcRiskRuleConfig().getOrderNum020()){//大于两单
                boolean flag = false;
                for(OrderRiskContext orderRiskContext : entry.getValue()){
                    Double distance = CoordUtil.getDistance(orderRiskContext.getStartLng(),orderRiskContext.getStartLat(),orderRiskContext.getEndLng(),orderRiskContext.getEndLat());
                    if(new BigDecimal(distance).compareTo(new BigDecimal(context.getSfcRiskRuleConfig().getDistance020())) > 0){
                        //有一个不满足情况说明是允许的，跳出这组用户，进行下一组用户的判断
                        flag = true;
                        break;
                    }
                }
                if(!flag) {
                    String orderIds = StringUtils.join(entry.getValue().stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList()), ",");
                    log.info("[SfcRuleRisk020Service][doHandler][][]命中020规则，司机关联用户为{},关联订单为{}", entry.getKey(), JsonUtils.json(orderIds));
                    distributionRiskManageService.addByRuleChain(entry.getValue(), ruleNo, context.getMainScene(), context.getChildScene(), 0, null, RiskLevelEnum.HIGH.getCode());
                    RiskResultDTO dto = new RiskResultDTO(405, "风控不通过020", null, null);
                    context.getUiResult().setData(dto);
                    context.getUiResult().setMsg("风控不通过");

                    List<String> linkOrderIds = entry.getValue().stream().map(OrderRiskContext::getOrderId).collect(Collectors.toList());
                    if(StringUtils.isBlank(context.getRuleNo())){
                        context.setRuleNo(ruleNo);
                    } else {
                        context.setRuleNo(context.getRuleNo() + "," + ruleNo);
                    }
                    riskHitLinkService.saveHitLink(new RiskHitLinkDTO(context.getParams().getRequestId(), context.getOrderId(), ruleNo,linkOrderIds));
                    break;
                }
            }
        }
        if(this.nextHandler != null){
            this.nextHandler.doHandler(context);
        } else {
            if(StringUtils.isNotBlank(context.getRuleNo())){
                riskHitService.initHitRisk(context.getParams(),new HitInfoDTO(context.getRuleNo(),
                        RiskLevelEnum.HIGH.getCode(),0,null,context.getUiResult()));
            }
        }
    }
}
