package com.ly.car.risk.process.service.order.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ly.car.common.bean.model.UiResult;
import com.ly.car.risk.process.client.CancelDutyJudgeClient;
import com.ly.car.risk.process.client.model.cancel.SfcOrderCancelDutyJudgeRequest;
import com.ly.car.risk.process.constants.ProductLineEnum;
import com.ly.car.risk.process.constants.StrategySceneEnum;
import com.ly.car.risk.process.controller.request.AssignDutyRequest;
import com.ly.car.risk.process.controller.request.CashRateQueryRequest;
import com.ly.car.risk.process.controller.request.DriverCancelRequest;
import com.ly.car.risk.process.controller.request.IsRiskUserRequest;
import com.ly.car.risk.process.controller.request.OrderAcceptCheckRequest;
import com.ly.car.risk.process.controller.request.RiskLevelQueryRequest;
import com.ly.car.risk.process.controller.request.UnifyCheckRequest;
import com.ly.car.risk.process.model.enums.SFCCancelReasonEnum;
import com.ly.car.risk.process.model.exception.BizException;
import com.ly.car.risk.process.model.risk.DutyResultDTO;
import com.ly.car.risk.process.model.risk.IMInfo;
import com.ly.car.risk.process.model.risk.UserComplainCache;
import com.ly.car.risk.process.model.risk.UserOrCarFinishCache;
import com.ly.car.risk.process.model.risk.VirtualCallInfo;
import com.ly.car.risk.process.repo.risk.mapper.RiskDriverCancelRecordMapper;
import com.ly.car.risk.process.repo.risk.mapper.entity.RiskDriverCancelRecord;
import com.ly.car.risk.process.service.ConfigCenterService;
import com.ly.car.risk.process.service.context.FilterSceneContext;
import com.ly.car.risk.process.service.context.FilterSendOrderContext;
import com.ly.car.risk.process.service.context.FilterSfcContext;
import com.ly.car.risk.process.service.core.RiskQueryService;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.service.dto.order.CarInfo;
import com.ly.car.risk.process.service.dto.order.CarOrderDetail;
import com.ly.car.risk.process.service.dto.order.OrderTripInfo;
import com.ly.car.risk.process.service.order.CarOrderService;
import com.ly.car.risk.process.service.order.CarRiskService;
import com.ly.car.risk.process.service.rule.SfcConvertHandlerService;
import com.ly.car.risk.process.service.rule.YncConvertHandlerService;
import com.ly.car.risk.process.service.rule.priceGroup.FilterCheckPriceHandler;
import com.ly.car.risk.process.service.rule.sendGroup.FilterSendOrderHandler;
import com.ly.car.risk.process.service.rule.sfcNewGroup.FilterSfcHandler;
import com.ly.car.risk.process.strategy.RiskStrategyHandler;
import com.ly.car.risk.process.strategy.model.RiskSceneResult;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import com.ly.car.utils.JsonUtils;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.stereotype.Service;

/**
 * Description of CarRiskServiceImpl
 *
 * <AUTHOR>
 * @date 2024/3/15
 * @desc
 */
@Service
@Slf4j
public class CarRiskServiceImpl implements CarRiskService {

    private static final FastDateFormat dateTimeFmt = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");


    @Resource
    private CarOrderService carOrderService;
    
    @Resource
    private CarRiskConvert carRiskConvert;

    @Resource
    private RiskDriverCancelRecordMapper cancelRecordMapper;

    @Resource
    private RiskStrategyHandler riskStrategyHandler;

    @Resource
    private CancelDutyJudgeClient cancelDutyJudgeClient;

    @Resource
    private YncConvertHandlerService yncConvertHandlerService;
    @Resource
    private SfcConvertHandlerService sfcConvertHandlerService;

    @Resource
    private RiskQueryService riskQueryService;

    @Override
    public void driverCancel(DriverCancelRequest request) {
        Assert.notBlank(request.getOrderId());
        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(request.getOrderId());
        if (null == orderDetail) {
            return;
        }
        RiskDriverCancelRecord cancelRecord = new RiskDriverCancelRecord();
        cancelRecord.setOrderId(orderDetail.getOrderId());
        CarInfo carInfo = orderDetail.getCarInfo();
        if (null != carInfo) {
            cancelRecord.setCarNum(orderDetail.getCarInfo().getCarNum());
            cancelRecord.setDriverCode(orderDetail.getCarInfo().getDriverCode());
        }

        cancelRecord.setCancelReason(request.getCancelReason());
        cancelRecord.setCancelTime(new Date());
        cancelRecordMapper.insert(cancelRecord);
    }

    @Override
    public DutyResultDTO assignDuty(AssignDutyRequest request) throws BizException {
        String orderId = request.getOrderId();
        if (StringUtils.isBlank(orderId)) {
            throw new RuntimeException("orderId不可为空");
        }
        CarOrderDetail orderDetail = carOrderService.queryOrderDetail(orderId);
        if (null == orderDetail) {
            throw new RuntimeException("无orderId对应订单");
        }
        // 如果没有接单过
        if (StringUtils.isBlank(orderDetail.getCarInfo().getCarNum())) {
            throw new BizException(-1, "未查询到车辆信息");
        }
        // todo 什么状态的订单能取消, 现在是未取消的订单，会来调用该接口
        ProductLineEnum productLine = ProductLineEnum.getByCode(orderDetail.getProductLine());
        switch (productLine) {
            case SFC:
                return sfcAssignDuty(orderDetail, request);
            case YNC:
                return yncAssignDuty(orderDetail, request);
            default:
                break;
        }
        return DutyResultDTO.builder().judgeResult(1).build();
    }

    private DutyResultDTO yncAssignDuty(CarOrderDetail orderDetail, AssignDutyRequest request) throws BizException {

        BigDecimal score = cancelDutyJudgeClient.yncCancelJudgeScore(orderDetail, request, 3);

        String scoreThreshold = ConfigCenterService.getDefault("YNC_CANCEL_DUTY_SCORE_THRESHOLD", "0.4");
        DutyResultDTO resultDTO = DutyResultDTO.builder().judgeScore(score.toPlainString()).thresholdScore(scoreThreshold).build();
        LoggerUtils.info(log,"网约车判责结果，获取到的用户分数:{},阈值:{}",score,scoreThreshold);
        //  网约车score ≤ 0.4 则说明乘客有责需付违约金，也就是 score >0.4，则用户无责
        if (score.compareTo(new BigDecimal(scoreThreshold)) > 0) {
            resultDTO.setJudgeResult(0);
            return resultDTO;
        }
        resultDTO.setJudgeResult(1);
        return resultDTO;
    }

    private DutyResultDTO sfcAssignDuty(CarOrderDetail orderDetail, AssignDutyRequest request) throws BizException {
        // 从缓存获取T+1数据，如果没有，则当作0值处理
        String memberId = orderDetail.getMemberId();
        String unionId = orderDetail.getUnionId();
        String carNum = orderDetail.getCarInfo().getCarNum();
        // 用户完单情况
        UserOrCarFinishCache userFinishCache = carOrderService.queryUserFinishCache(memberId);
        // 司机完单情况
        UserOrCarFinishCache carFinishCache = carOrderService.queryCarFinishCache(carNum);
        // 用户投诉情况
        UserComplainCache userComplainCache = carOrderService.queryUserComplainCache(unionId);

        // 虚拟通话记录
        VirtualCallInfo virtualCallInfo = carOrderService.queryVirtualCallInfo(orderDetail.getOrderId(), orderDetail.getCarInfo().getDriverVirtualPhone());
        // IM记录
        IMInfo imInfo = carOrderService.queryImInfo(orderDetail.getOrderId(), orderDetail.getCarInfo().getSupplierCode(),
                orderDetail.getUnionId(), orderDetail.getMemberId(), orderDetail.getCarInfo().getCarNum());

        String penaltyInquiry = carOrderService.queryPenaltyInquiry(orderDetail.getOrderId(), orderDetail.getMemberId());

        // 构建请求
        SfcOrderCancelDutyJudgeRequest judgeRequest = buildSfcCancelJudgeReq(request.getCancelReason(), orderDetail, userFinishCache, carFinishCache, userComplainCache, virtualCallInfo, imInfo, penaltyInquiry);

        // 判分
        BigDecimal score = cancelDutyJudgeClient.sfcCancelJudeScore(judgeRequest, 2);
        String scoreThreshold = ConfigCenterService.getDefault("SFC_CANCEL_DUTY_SCORE_THRESHOLD", "0.5");

        LoggerUtils.info(log,"顺风车判责结果，获取到的用户分数:{},阈值:{}",score,scoreThreshold);

        DutyResultDTO resultDTO = DutyResultDTO.builder().judgeScore(score.toPlainString()).thresholdScore(scoreThreshold).build();
        //  顺风车score ≤ 0.5 则说明乘客有责需付违约金，也就是 score >0.5，则用户无责
        if (score.compareTo(new BigDecimal(scoreThreshold)) > 0) {
            resultDTO.setJudgeResult(0);
            return resultDTO;
        }
        resultDTO.setJudgeResult(1);
        return resultDTO;
    }

    private SfcOrderCancelDutyJudgeRequest buildSfcCancelJudgeReq(String cancelReason, CarOrderDetail orderDetail, UserOrCarFinishCache userFinishCache, UserOrCarFinishCache carFinishCache, UserComplainCache userComplainCache, VirtualCallInfo virtualCallInfo, IMInfo imInfo, String penaltyInquiry) {
        OrderTripInfo orderTrip = orderDetail.getOrderTrip();
        SfcOrderCancelDutyJudgeRequest req = new SfcOrderCancelDutyJudgeRequest();
        req.setBizScene("cancel_order_fee_judge");
        req.setMark("A");
        req.setRealAmount(Float.valueOf(orderDetail.getBaseInfo().getAmount()));
        req.setLossAmount(Float.valueOf(penaltyInquiry));
        req.setPayType(orderDetail.getBaseInfo().getPayCategory() == 1 ? "前付" : "后付");
        req.setStartCityId(orderTrip.getDepartureCityCode());
        req.setEndCityId(orderTrip.getArrivalCityCode());
        req.setCallMinutes(virtualCallInfo.getTotalCallMinutes());

        req.setCancelReason(SFCCancelReasonEnum.getTypeByReason(cancelReason));
        req.setEarliestTime(getEarliestTime(orderDetail));

        req.setCancelTime(dateTimeFmt.format(new Date()));
        req.setPayTime(getPayTime(orderDetail));
        req.setCreateTime(dateTimeFmt.format(orderDetail.getBaseInfo().getGmtCreate()));
        req.setDecisionTime(dateTimeFmt.format(orderDetail.getBaseInfo().getGmtAccept()));
        req.setUseTime(dateTimeFmt.format(orderDetail.getBaseInfo().getGmtUsage()));
        req.setLatestCallTime(virtualCallInfo.getLatestCallTime());
        req.setLatestContactTime(StringUtils.EMPTY);
        req.setSucCnt(virtualCallInfo.getSucCnt());
        req.setFailCnt(virtualCallInfo.getFailCnt());
        req.setSupplierCode(orderDetail.getCarInfo().getSupplierCode());
        req.setTsCount(userComplainCache.getTsCount());
        req.setTsCountOther(userComplainCache.getTsCountOther());
        req.setUserMsgSend(imInfo.getUserMsgSend());
        req.setUserMsgRead(imInfo.getUserMsgRead());
        req.setDriverMsgSend(imInfo.getDriverMsgSend());
        req.setDriverMsgRead(imInfo.getDriverMsgRead());
        req.setDriverCancelNum(carFinishCache.getCancelCount());
        req.setDriverCancelRefundNum(carFinishCache.getCancelBackCnt());
        req.setUserCancelNum(userFinishCache.getCancelCount());
        req.setUserCancelRefundNum(userFinishCache.getCancelBackCnt());
        req.setUserDecisionCnt(userFinishCache.getDecisionCount());
        req.setUserFinishCnt(userFinishCache.getFinishCount());
        req.setDriverDecisionCnt(carFinishCache.getDecisionCount());
        req.setDriverFinishCnt(carFinishCache.getFinishCount());
        return req;
    }

    private String getPayTime(CarOrderDetail orderDetail) {
        int payCategory = orderDetail.getBaseInfo().getPayCategory();
        // 1:在线支付/2:支付分支付
        Date payTime;
        if (payCategory == 1) {
            payTime = orderDetail.getBaseInfo().getGmtPaid();
        } else {
            payTime = orderDetail.getBaseInfo().getGmtPayScoreDeduct();
        }
        if (null == payTime || payTime.before(OrderUtils.limitTime)) {
            return dateTimeFmt.format(orderDetail.getBaseInfo().getGmtCreate());
        }
        return dateTimeFmt.format(payTime);
    }

    private String getEarliestTime(CarOrderDetail orderDetail) {
        if (orderDetail.getBaseInfo().getGmtAccept().after(OrderUtils.limitTime)) {
            return dateTimeFmt.format(orderDetail.getBaseInfo().getGmtAccept());
        }
        return dateTimeFmt.format(orderDetail.getBaseInfo().getGmtCreate());
    }
    
    @Override
    public UiResult orderAcceptCheck(OrderAcceptCheckRequest request) throws BizException {
        if(StringUtils.isBlank(request.getOrderId())){
            throw new BizException(-1,"orderId不可为空");
        }

        UnifyCheckRequest unifyCheckRequest = carRiskConvert.fillParam(request);
        if(null == unifyCheckRequest){
            return UiResult.ok(new RiskResultDTO(0, ""));
        }
        
        RiskSceneResult riskSceneResult = riskStrategyHandler.unifyCheck(unifyCheckRequest);
        
        return UiResult.ok(new RiskResultDTO(riskSceneResult.isRiskFlag() ? 1 : 0, riskSceneResult.getRiskMsg()));
    }

    @Override
    public UiResult checkIsRiskUser(IsRiskUserRequest request) throws BizException {
        ProductLineEnum productLineEnum = ProductLineEnum.getByCode(request.getProductLine());
        Objects.requireNonNull(productLineEnum, "产品线不存在");
        switch (productLineEnum) {
            case YNC:
                // 2-3 网约车下单
                request.setMainScene(2);
                request.setChildScene(3);
                FilterSendOrderContext context = yncConvertHandlerService.convertSendOrder(request);

                try {
                    UiResult<RiskResultDTO> resultYNC = riskQueryService.queryResult(context.getParams());
                    if (resultYNC.getData() != null && resultYNC.getData().getCode() == 1) {
                        LoggerUtils.info(log, "[司机接单判定风控用户]  专车规则引擎命中返回 {}", JsonUtils.json(resultYNC));
                        return resultYNC;
                    }
                } catch (Exception e) {
                    LoggerUtils.error(log, "[司机接单判定风控用户]  专车规则引擎启动错误 ", e);
                }

                FilterSendOrderHandler handler = yncConvertHandlerService.getSendOrderHandler(ListUtil.of("2-3"));
                handler.doHandler(context);
                UiResult result = yncConvertHandlerService.convertResult(context);

                return result;

            case SFC:
            case XCAR:
                // 2-2 顺风车预订
                request.setMainScene(2);
                request.setChildScene(2);
                request.setSourceId("CC8G0ZVJdOKfsUMP77DlZFzLXeeh3N9p");

                FilterSfcHandler sfcHandler = sfcConvertHandlerService.getHandler(ListUtil.of("2-2"));
                FilterSfcContext sfcContext = sfcConvertHandlerService.convertParams(request);
                sfcHandler.doHandler(sfcContext);
                return sfcContext.getUiResult();
            
            case BUS:
            case LINE:
                // 16-1 汽车票下单
                UnifyCheckRequest unifyCheckRequest = carRiskConvert.fillParam(request);
                
                RiskSceneResult riskSceneResult = riskStrategyHandler.unifyCheck(unifyCheckRequest);
                
                return UiResult.ok(new RiskResultDTO(riskSceneResult.isRiskFlag() ? 1 : 0, riskSceneResult.getRiskMsg()));
            
            default:
                throw new BizException(-1, "不支持的产品线");
        }
    }


    @Override
    public UiResult queryRiskLevel(RiskLevelQueryRequest request) throws Exception {

        ProductLineEnum productLineEnum = ProductLineEnum.getByCode(request.getProductLine());
        Objects.requireNonNull(productLineEnum, "产品线不存在");

        switch (productLineEnum) {
            case YNC:
                // 5-1 询价场景-专车询价
                FilterCheckPriceHandler handler = yncConvertHandlerService.getCheckRiskLevelHandler();
                FilterSceneContext context = yncConvertHandlerService.convertCheckRiskLevelCtx(request);
                UiResult result = handler.doHandler(context);
                return yncConvertHandlerService.convertRiskFlag(result);
            case SFC:
            default:
                throw new BizException(-1, "不支持的产品线");
        }
    }

    @Override
    public UiResult queryCashRate(CashRateQueryRequest request) throws Exception {
        ProductLineEnum productLineEnum = ProductLineEnum.getByCode(request.getProductLine());
        Objects.requireNonNull(productLineEnum, "产品线不存在");
        Assert.notNull(request.getEsAmount(), "金额不能为空");

        switch (productLineEnum) {
            case YNC:
                // 5-1 询价场景-专车询价
                FilterCheckPriceHandler handler = yncConvertHandlerService.getHandler(ListUtil.of("5-1"));
                UiResult<RiskResultDTO> uiResult = handler.doHandler(yncConvertHandlerService.convertCheckPrice(request));
                if (uiResult.isSuccess() && null != uiResult.getData()) {
                    if (StringUtils.isBlank(uiResult.getData().getCashRate()) || new BigDecimal(uiResult.getData().getCashRate()).compareTo(BigDecimal.ONE) < 0) {
                        uiResult.getData().setCashRate("1.0");
                    }
                }
                return uiResult;
            case SFC:
            default:
                throw new BizException(-1, "不支持的产品线");
        }
    }
}