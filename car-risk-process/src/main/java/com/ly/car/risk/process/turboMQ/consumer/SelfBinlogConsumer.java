package com.ly.car.risk.process.turboMQ.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.car.risk.process.component.SpringContextUtil;
import com.ly.car.risk.process.handler.HandlerChooseFactory;
import com.ly.car.risk.process.handler.selfbin.SelfBinHandler;
import com.ly.car.risk.process.utils.LoggerUtils;
import com.ly.car.risk.process.utils.OrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SelfBinlogConsumer implements MessageListenerConcurrently {

    private HandlerChooseFactory handlerChooseFactory;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {

        for (MessageExt messageExt : list) {
            LoggerUtils.initLogMap("selfConsumer", messageExt.getTags(), messageExt.getMsgId(), "");
            String tag = messageExt.getTags();
            try {
                String body = new String(messageExt.getBody(), "utf-8");
                LoggerUtils.info(log, "接收到消息:{},tag:{}", body, tag);
                // 从body中分开处理
                String orderId = getOrderIdFromBody(body);
                LoggerUtils.getLogMap().put("filter2",orderId);

                // 新老订单处理分开
                handlerChooseFactory = SpringContextUtil.getBean("handlerChooseFactory");
                SelfBinHandler selfBinHandler = handlerChooseFactory.chooseSelfBinHandler(OrderUtils.isNewOrder(orderId) ? "new" : "old");
                selfBinHandler.doHandler(body, tag);
                LoggerUtils.info(log, "消息处理结束,orderId:{},tag:{}", orderId, tag);
            } catch (Exception e) {
                LoggerUtils.error(log, "selfConsumer消费失败", e);
            } finally {
                LoggerUtils.removeAll();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private String getOrderIdFromBody(String body) {
        try {
            JSONObject jsonObject = JSON.parseObject(body);
            return jsonObject.getString("orderId");
        } catch (Exception e) {
            return body;
        }
    }


}
