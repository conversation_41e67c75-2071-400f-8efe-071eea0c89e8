package com.ly.car.risk.process.service.groovy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.car.risk.entity.RiskCustomerManage;
import com.ly.car.risk.process.api.PublicServiceClient;
import com.ly.car.risk.process.constants.RiskCustomerRiskTypeEnum;
import com.ly.car.risk.process.constants.RiskLevelEnum;
import com.ly.car.risk.process.controller.params.RedisParam;
import com.ly.car.risk.process.repo.risk.mapper.*;
import com.ly.car.risk.process.repo.risk.mapper.entity.*;
import com.ly.car.risk.process.service.RiskCustomerService;
import com.ly.car.risk.process.service.core.CategoryRelationCache;
import com.ly.car.risk.process.service.core.RiskEngineRuleCache;
import com.ly.car.risk.process.service.core.RiskRuleRelationCache;
import com.ly.car.risk.process.service.dto.CommonCustomerParam;
import com.ly.car.risk.process.service.dto.RiskResultDTO;
import com.ly.car.risk.process.utils.GroovyScriptUtil;
import com.ly.car.risk.process.utils.OkHttpClientUtil;
import com.ly.car.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskEngineImpl implements RiskEngine{

    @Resource
    private RiskCustomerService riskCustomerService;
    @Resource(name = "riskProcessRedisson")
    private RedissonClient redissonClient;
    @Resource
    private CategoryRelationCache categoryRelationCache;
    @Resource
    private RiskEngineRuleCache riskEngineRuleCache;
    @Resource
    private RiskRuleRelationCache riskRuleRelationCache;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private PublicServiceClient publicServiceClient;

    @Override
    public void executeCustomer(RiskScene riskScene, Map<String, String> data,Map<String,List<HitStrategyDTO>> hitInfo) {
        List<HitStrategyDTO> strategyDTOS = new ArrayList<>();
        HitStrategyDTO strategyDTO = new HitStrategyDTO();
        //参与名单管理的固定只有几个
        CommonCustomerParam customerParam = new CommonCustomerParam();
        customerParam.setUserPhone(data.get("userPhone"));
        customerParam.setPassengerCellphone(data.get("passengerCellphone"));
        customerParam.setDeviceId(data.get("deviceId"));
        customerParam.setMemberId(data.get("memberId"));
        customerParam.setDriverCardNo(data.get("driverCardNo"));
        customerParam.setPayAccount(data.get("payAccount"));
        customerParam.setUnionId(data.get("unionId"));
        customerParam.setDriverId(data.get("driverId"));
        if(data.get("driverList") != null){
            customerParam.setDriverCardNos(Arrays.asList(data.get("driverList").split(",")));
        }
        log.info("[][][][]规则引擎查询名单参数{}",JsonUtils.json(customerParam));
        List<RiskCustomerManage> listByValueByGroup = this.riskCustomerService.getListByValueByGroup(customerParam, new Date());
        log.info("[][][][]规则引擎查询名单返回{}",JsonUtils.json(listByValueByGroup));
        if(listByValueByGroup == null || listByValueByGroup.size() == 0){
            return;
        }
        //一对一名单
        RiskCustomerManage oneToOneManage = listByValueByGroup.stream().filter(e -> e.getRiskType().equals(RiskCustomerRiskTypeEnum.ban_one_to_one_list.getCode())
                && (StringUtils.isNotBlank(customerParam.getPassengerCellphone()) && customerParam.getPassengerCellphone().equals(e.getBindUser()) ||
                    StringUtils.isNotBlank(customerParam.getUserPhone()) && customerParam.getUserPhone().equals(e.getBindUser())
                )).findFirst().orElse(null);
        if(oneToOneManage != null){
            strategyDTO.setStrategyNo(String.valueOf(oneToOneManage.getRiskType()));
            strategyDTO.setValue(oneToOneManage.getCustomerValue());
            strategyDTOS.add(strategyDTO);
            hitInfo.put("customer",strategyDTOS);
            return;
        }
        log.info("[][][][]名单决策一对一{}",JsonUtils.json(oneToOneManage));
        //白名单直接过
        RiskCustomerManage whiteManage = listByValueByGroup.stream().filter(value -> value.getRiskType() == 2).findFirst().orElse(null);
        if(whiteManage != null){
            strategyDTO.setStrategyNo(String.valueOf(whiteManage.getRiskType()));
            strategyDTO.setValue(whiteManage.getCustomerValue());
            strategyDTOS.add(strategyDTO);
            hitInfo.put("customer",strategyDTOS);
            return;
        }
        log.info("[][][][]名单决策白{}",JsonUtils.json(whiteManage));
        //黑名单直接拦截
        RiskCustomerManage  blackManage = listByValueByGroup.stream().filter(value -> value.getRiskType() == 1).findFirst().orElse(null);
        if(blackManage != null){
            strategyDTO.setStrategyNo(String.valueOf(blackManage.getRiskType()));
            strategyDTO.setValue(blackManage.getCustomerValue());
            strategyDTOS.add(strategyDTO);
            hitInfo.put("customer",strategyDTOS);
            return;
        }
        log.info("[][][][]名单决策黑{}",JsonUtils.json(blackManage));
        // todo 后续优化为各种局部白名单，添加名单类型区分是白还是黑
        for(RiskCustomerManage manage : listByValueByGroup){
            if(StringUtils.isBlank(riskScene.getFilterCustomerType())){
                continue;
            }
            if(riskScene.getFilterCustomerType().contains(String.valueOf(manage.getRiskType()))){
                strategyDTO.setStrategyNo(String.valueOf(manage.getRiskType()));
                strategyDTO.setValue(manage.getCustomerValue());
                strategyDTOS.add(strategyDTO);
                hitInfo.put("customer",strategyDTOS);
                return;
            }
        }
    }

    @Override
    public void executeAbstraction(RiskScene riskScene, Map<String, String> data,Map<String,Object> returnMap,List<RiskField> riskFields) {
        //查询场景
        if(riskScene == null){
            log.warn("[RiskEngineImpl][executeAbstraction][][]该场景为空，不进行后续匹配");
            return;
        }
        if(riskScene.getIsDeleted() == 1){
            log.warn("[RiskEngineImpl][executeAbstraction][][]该场景已删除，不进行后续匹配");
            return;
        }
        //查询当前的指标都有哪些是加密的
        List<RiskField> encryptFields = riskFields.stream().filter(field->field.getEncryptFlag()==1).collect(Collectors.toList());
        Map<String,String> encryptData = new HashMap<>();
        encryptData.put("passengerCellphone",data.get("passengerCellphone")==null?data.get("userPhone"):"");
        encryptData.put("plateNumber",data.get("driverCardNo"));
        if(CollectionUtils.isNotEmpty(encryptFields)){
            String passengerCellphone = data.get("passengerCellphone")==null?data.get("userPhone"):"";
            String plateNumber = data.get("driverCardNo");
            if(StringUtils.isNotBlank(passengerCellphone)){
                passengerCellphone = publicServiceClient.encrypt(1,passengerCellphone);
                encryptData.put("plateNumber",passengerCellphone);
            }
            if(StringUtils.isNotBlank(plateNumber)){
                plateNumber = publicServiceClient.encrypt(6,plateNumber);
                encryptData.put("plateNumber",plateNumber);
            }
        }

        //封装指标命名
        for(RiskField field : riskFields){
            String original = field.getFieldNo();
            String characteristicNo = field.getFieldNo();
            String userId = StringUtils.isNotBlank(data.get("unionId"))?data.get("unionId"):data.get("memberId");
            //判断是否加密
            String passengerCellphone = encryptData.get("passengerCellphone");
            String plateNumber = encryptData.get("plateNumber");
            log.info("[][][][]获取缓存指标是否加密{},{},{}，{}",field.getEncryptFlag(),field.getCategory(),passengerCellphone,plateNumber);

            if(field.getCategory() == 0){
                characteristicNo = characteristicNo + "_"+ data.get("memberId");
            } else if(field.getCategory() == 1){
                characteristicNo = characteristicNo + "_"+ userId+"_"+plateNumber;
            } else if(field.getCategory() == 3){
                characteristicNo = characteristicNo + "_"+ plateNumber+"_"+data.get("memberId");
            } else if(field.getCategory() == 9){
                characteristicNo = characteristicNo + "_"+ userId;
            } else if(field.getCategory() == 2){
                characteristicNo = characteristicNo + "_"+ plateNumber;
            }
            log.info("[][][][]获取缓存指标的key{}",characteristicNo);
            RMap<String, Object> map = redissonClient.getMap(characteristicNo);
            log.info("[][][][]获取缓存指标的key:{},value:{}",characteristicNo,JsonUtils.json(map));
            if(map == null || map.isEmpty()){
                if(env!=null && !env.equals("product")){
                    //请求线上，获取map
                    RedisParam redisParam = new RedisParam();
                    redisParam.setKey(characteristicNo);
                    String post = OkHttpClientUtil.getInstance().post("http://tcwireless.17usoft.com/car_risk_process/redis/queryRedis", JsonUtils.json(redisParam), null);
                    log.info("[][][][]预发获取生产redis,key{},值{}",JsonUtils.json(redisParam),post);
                    if(StringUtils.isNotBlank(post)){
                        Map resultMap = JSONObject.parseObject(post,Map.class);
                        if(!resultMap.isEmpty()){
                            //用原始的脚本列
                            returnMap.put(original,resultMap);
                        } else {
                            Map<String,Object> emptyMap = new HashMap<>();
                            emptyMap.put("num",0);
                            returnMap.put(original,emptyMap);
                        }
                    } else {
                        Map<String,Object> emptyMap = new HashMap<>();
                        emptyMap.put("num",0);
                        returnMap.put(original,emptyMap);
                    }
                } else {
                    Map<String,Object> emptyMap = new HashMap<>();
                    emptyMap.put("num",0);
                    returnMap.put(original,emptyMap);
                }

            } else {
                //用原始的脚本列
                returnMap.put(original,map);
            }
        }
        log.info("[][][][]获取指标计算结果:{}", JsonUtils.json(returnMap));
    }

    @Override
    public void executeRule(List<RiskStrategy> strategyList, Map<String, Object> data,Map<String,List<HitStrategyDTO>> hitInfo,List<RiskField> riskFields) {
        log.info("开始执行规则脚本，所有指标值：{}",JsonUtils.json(data));
        //对data集合进行拆分
        Map<String,Object> paramMap = new HashMap<>();
        Map<String,List<String>> orderNoMap = new HashMap<>();
        for(Map.Entry<String,Object> entry : data.entrySet()){
            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(entry.getValue());
            RiskField field = riskFields.stream().filter(riskField -> riskField.getFieldNo().equals(entry.getKey())).findFirst().orElse(null);
            if(field != null){
                if(field.getFieldType().equals("NUMBER")){
                    paramMap.put(entry.getKey(),jsonObject.getInteger("num"));
                } else if(field.getFieldType().equals("DECIMALS")){
                    paramMap.put(entry.getKey(),jsonObject.getDouble("num"));
                }
            } else {
                paramMap.put(entry.getKey(),jsonObject.getInteger("num"));
            }
            String orderNos = (String) jsonObject.get("orderNos");
            if(StringUtils.isNotBlank(orderNos)){
                orderNoMap.put(entry.getKey(),Arrays.asList(orderNos.split(",")));
            }
        }
        //查询规则集合
        Map<String,Boolean> ruleResult = new HashMap<>();

        Map<String,List<String>> linkOrder = new HashMap<>();

        List<HitStrategyDTO> hitStrategyDTOS = new ArrayList<>();
        for(RiskStrategy strategy : strategyList){
            List<CategoryRelation> cacheCategoryRelationList = categoryRelationCache.loadRelation(strategy.getStrategyNo(), 0);
            if(CollectionUtils.isEmpty(cacheCategoryRelationList)){
                continue;
            }

            List<String> ruleNos = cacheCategoryRelationList.stream().map(CategoryRelation::getRuleSceneNo).collect(Collectors.toList());
            List<RiskEngineRule> ruleList = new ArrayList<>();
            for(String ruleNo : ruleNos){
                ruleList.add(riskEngineRuleCache.loadRule(ruleNo));
            }
            if(CollectionUtils.isEmpty(ruleList)){
                continue;
            }
            //这边是当前策略下的所有规则命中
            List<HitInfoDTO> dtos = new ArrayList<>();
            for(RiskEngineRule rule : ruleList){
                List<String> orderIds = new ArrayList<>();
                String script = rule.getScript();
                boolean matched = checkActivationScript(script, paramMap);
                log.info("[][][][]当前规则:{},执行结果{}",rule.getRuleName(),matched);
                ruleResult.put("rule"+rule.getId(),matched);
                if(matched){
                    //这边获取规则下的指标，指标相关的订单先存下
                    List<String> ruleRelation = riskRuleRelationCache.loadRuleRelation(String.valueOf(rule.getId()))
                            .stream()
                            .map(RiskRuleRelation::getLeftField)
                            .collect(Collectors.toList());
                    for(String str : ruleRelation){
                        List<String> orderNos = orderNoMap.get(str);
                        if(CollectionUtils.isNotEmpty(orderNos)){
                            orderIds.addAll(orderNos);
                        }
                    }
                    log.info("[][][][]当前命中规则{}关联订单{}",rule.getId(),JsonUtils.json(orderIds));
                    HitInfoDTO dto = new HitInfoDTO();
                    dto.setHit(StringUtils.isNotBlank(rule.getRuleNo())?rule.getRuleNo():String.valueOf(rule.getId()));
                    dto.setValue(StringUtils.join(orderIds,","));
                    dtos.add(dto);
                }
            }
            //当前策略的所有规则表达式结果
            String strategyScript = strategy.getScript();
            log.info("[][][][]执行策略脚本参数：{}",JsonUtils.json(strategyScript));
            log.info("[][][][]该策略下{}，所有规则执行结果{}",strategy.getStrategyName(),JsonUtils.json(ruleResult));
            boolean matched = checkActivationScript(strategyScript,ruleResult);
            log.info("[][][][]当前策略:{}执行结果{}",strategy.getStrategyName(),matched);
            if(matched){
                HitStrategyDTO hitStrategyDTO = new HitStrategyDTO();
                hitStrategyDTO.setStrategyNo(strategy.getStrategyNo());
                hitStrategyDTO.setHitInfoDTO(dtos);
                hitStrategyDTOS.add(hitStrategyDTO);
            }
        }
        if(hitStrategyDTOS.size() > 0){
            hitInfo.put("rule",hitStrategyDTOS);
        }
        //计算风险级别
        //save to db
    }

    @Override
    public void executeSpecialRule() {

    }

    private Boolean checkActivationScript(String ruleScript, Map data) {
        Object[] args = { data };
        Boolean ret = false;
        try {
            ret = (Boolean) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
        } catch (Exception e) {
        }
        return ret;
    }


    private Boolean checkAbstractionScript(String ruleScript,Map entry){
        Object[] args = {entry};
        Boolean ret = false;
        try {
            ret = (Boolean) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
        } catch (Exception e) {
            log.error("验证特征脚本错误:",e);
        }
        return ret;
    }

    public static String buildScript(){
        String str = "class checkRule{    public boolean check(def data) { if(data.sfc_driver_and_user_24h_finish>3||data.sfc_driver_and_user_1h_finish>3||data.sfc_driver_and_user_24h_left_10_finish>=3){  return true ; } else {  return false ;  }}}";
        return str;
    }

    public static void main(String[] args) {
        String jsonStr = "{\"sfc_driver_and_user_24h_finish\":{\"orderNos\":\"SFC64B10A01801348A95U\",\"num\":\"1\"},\"sfc_driver_and_user_1h_finish\":{\"orderNos\":\"SFC64B10A01801348A95U\",\"num\":\"1\"},\"sfc_driver_and_user_24h_left_10_finish\":{\"num\":0}}";
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        JSONObject jsonObject1 = jsonObject.getJSONObject("sfc_driver_and_user_24h_finish");
        System.out.println("-===="+jsonObject1.getInteger("num"));
        Map<String,Object> map = new HashMap<>();
        map.put("sfc_driver_and_user_24h_finish",1);
        map.put("sfc_driver_and_user_1h_finish",1);
        map.put("sfc_driver_and_user_24h_left_10_finish",0);
        System.out.println(execute(buildScript(),map));
    }

    private static Boolean execute(String ruleScript,Map entry){
        Object[] args = {entry};
        Boolean ret = false;
        try {
            ret = (Boolean) GroovyScriptUtil.invokeMethod(ruleScript, "check", args);
        } catch (Exception e) {
            log.error("验证特征脚本错误:",e);
        }
        return ret;
    }



}
