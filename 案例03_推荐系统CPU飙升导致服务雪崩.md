# 案例03：推荐系统CPU飙升导致服务雪崩问题

## 🚨 **问题背景**

### 业务场景
- **项目**：美团推荐系统核心服务
- **功能**：为用户提供个性化商家和商品推荐
- **服务规模**：50台服务器，每台16核32G，日均请求量2亿+
- **业务特点**：实时特征计算、复杂算法模型、高并发读写

### 问题现象
- **时间**：2023年8月20日 12:15开始，午餐高峰期
- **现象**：推荐服务CPU使用率从30%瞬间飙升至95%+
- **影响范围**：
  - 推荐接口响应时间从50ms飙升至10秒+
  - 服务可用性从99.9%下降至60%
  - 引发上游服务超时，导致整个推荐链路雪崩
  - 用户首页推荐位显示空白，影响用户体验

### 告警信息
```bash
# 系统告警（12:16开始密集告警）
[ALERT] CPU使用率异常
服务: recommendation-service
CPU使用率: 96% (正常值: 30-50%)
负载: 48.5 (16核服务器)
告警级别: P0

[ALERT] 服务响应时间异常
服务: recommendation-service
平均响应时间: 12.3s (正常值: 50ms)
P99响应时间: 25s
超时率: 85%
告警级别: P0

[ALERT] 线程池队列积压
服务: recommendation-service
队列长度: 50000+ (正常值: <1000)
活跃线程: 200/200 (线程池满载)
拒绝任务数: 10000+/分钟
告警级别: P0
```

## 🔍 **问题排查过程**

### 第一阶段：紧急定位（12:16-12:30）

#### 1. 系统资源检查
```bash
# 查看CPU使用情况
top -p $(pgrep -f recommendation-service)
PID    USER      PR  NI    VIRT    RES    SHR S  %CPU %MEM     TIME+ COMMAND
12345  app       20   0  8.2g    6.1g   32m S  950.0 19.5  125:43 java

# 查看负载情况
uptime
12:17:23 up 15 days,  3:42,  2 users,  load average: 48.52, 35.21, 18.67

# 查看线程情况
ps -eLf | grep recommendation-service | wc -l
2847  # 线程数异常多

# 查看网络连接
netstat -an | grep :8080 | wc -l
15000  # 连接数异常高
```

#### 2. JVM状态检查
```bash
# 查看JVM进程状态
jstat -gc 12345 1s 5
 S0C    S1C    S0U    S1U      EC       EU        OC         OU       MC     MU    CCSC   CCSU   YGC     YGCT    FGC    FGCT     GCT   
34944.0 34944.0  0.0   22528.0 279616.0 245760.0 699072.0   456789.0  87424.0 82156.8 10624.0 9876.3    1234   12.456   45   8.789   21.245

# 查看线程栈信息
jstack 12345 > thread_dump_$(date +%Y%m%d_%H%M%S).txt

# 发现大量线程处于RUNNABLE状态，CPU密集计算
```

#### 3. 应用日志分析
```bash
# 查看错误日志
tail -f /app/logs/recommendation-service.log | grep -E "ERROR|WARN"

# 关键日志信息
2023-08-20 12:15:23.456 WARN  [recommendation-thread-pool-123] c.m.r.s.FeatureCalculator - 特征计算耗时异常: userId=123456, costTime=8500ms
2023-08-20 12:15:45.789 ERROR [recommendation-thread-pool-156] c.m.r.s.ModelInference - 模型推理超时: modelId=user_cf_v2, timeout=5000ms
2023-08-20 12:16:12.345 WARN  [recommendation-thread-pool-189] c.m.r.s.RankingService - 排序算法执行异常缓慢: itemCount=50000, costTime=12000ms
```

### 第二阶段：深度分析（12:30-13:00）

#### 1. 线程栈分析
```bash
# 分析线程栈，发现热点方法
grep -A 10 -B 2 "RUNNABLE" thread_dump_20230820_121630.txt | head -50

# 发现问题线程栈：
"recommendation-thread-pool-123" #123 prio=5 os_prio=0 tid=0x... nid=0x... runnable [0x...]
   java.lang.Thread.State: RUNNABLE
        at com.meituan.recommendation.algorithm.CollaborativeFiltering.calculateSimilarity(CollaborativeFiltering.java:156)
        at com.meituan.recommendation.algorithm.CollaborativeFiltering.computeUserSimilarity(CollaborativeFiltering.java:89)
        at com.meituan.recommendation.service.RecommendationService.generateRecommendations(RecommendationService.java:234)

# 大量线程卡在协同过滤算法的相似度计算上
```

#### 2. 性能分析工具
```bash
# 使用async-profiler分析CPU热点
java -jar async-profiler.jar -e cpu -d 60 -f profile_cpu.html 12345

# CPU热点分析结果：
方法调用                                           CPU占比    调用次数
CollaborativeFiltering.calculateSimilarity()      45.2%     2,340,000
MatrixOperations.dotProduct()                     23.8%     8,920,000  
FeatureExtractor.extractUserFeatures()            12.1%       456,000
RankingAlgorithm.sortByScore()                     8.9%       234,000
```

#### 3. 问题代码定位
```java
@Service
public class CollaborativeFiltering {
    
    // 问题代码：低效的相似度计算算法
    public double calculateSimilarity(long userId1, long userId2) {
        // 问题1：每次都重新获取用户行为数据，无缓存
        List<UserBehavior> behaviors1 = userBehaviorService.getUserBehaviors(userId1);
        List<UserBehavior> behaviors2 = userBehaviorService.getUserBehaviors(userId2);
        
        // 问题2：使用低效的嵌套循环算法，时间复杂度O(n²)
        double similarity = 0.0;
        for (UserBehavior behavior1 : behaviors1) {
            for (UserBehavior behavior2 : behaviors2) {
                if (behavior1.getItemId().equals(behavior2.getItemId())) {
                    // 问题3：复杂的相似度计算，包含大量浮点运算
                    double score1 = behavior1.getScore();
                    double score2 = behavior2.getScore();
                    double weight = calculateWeight(behavior1, behavior2);
                    
                    // 问题4：使用了耗时的数学函数
                    similarity += Math.pow(score1 * score2 * weight, 2.0) / 
                                 (Math.sqrt(score1) + Math.sqrt(score2) + 1.0);
                }
            }
        }
        
        return similarity;
    }
    
    // 问题5：权重计算包含复杂的时间衰减算法
    private double calculateWeight(UserBehavior behavior1, UserBehavior behavior2) {
        long timeDiff = Math.abs(behavior1.getTimestamp() - behavior2.getTimestamp());
        
        // 复杂的时间衰减函数
        return Math.exp(-timeDiff / (24 * 3600 * 1000.0)) * 
               Math.log(1.0 + behavior1.getActionType().getWeight()) *
               Math.log(1.0 + behavior2.getActionType().getWeight());
    }
    
    // 问题6：批量计算用户相似度时，没有并发控制
    public Map<Long, Double> computeUserSimilarity(long targetUserId, List<Long> candidateUserIds) {
        Map<Long, Double> similarities = new HashMap<>();
        
        // 串行计算，没有利用多核优势
        for (Long candidateUserId : candidateUserIds) {
            double similarity = calculateSimilarity(targetUserId, candidateUserId);
            similarities.put(candidateUserId, similarity);
        }
        
        return similarities;
    }
}
```

### 第三阶段：根因分析（13:00-13:30）

#### 1. 算法复杂度分析
```mermaid
graph TD
    A[推荐请求] --> B[用户相似度计算]
    B --> C[获取用户行为数据]
    C --> D[嵌套循环比较]
    D --> E[复杂数学运算]
    E --> F[时间衰减计算]
    
    G[性能瓶颈分析] --> H[数据获取: O(n)]
    G --> I[算法复杂度: O(n²)]
    G --> J[数学运算: 高CPU消耗]
    G --> K[无缓存: 重复计算]
    G --> L[无并发: 串行执行]
```

#### 2. 数据量分析
```bash
# 分析用户行为数据规模
SELECT 
    COUNT(*) as total_behaviors,
    COUNT(DISTINCT user_id) as total_users,
    AVG(behavior_count) as avg_behaviors_per_user
FROM (
    SELECT user_id, COUNT(*) as behavior_count 
    FROM user_behaviors 
    WHERE created_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    GROUP BY user_id
) t;

# 结果：
# total_behaviors: 50,000,000
# total_users: 2,000,000  
# avg_behaviors_per_user: 25

# 计算复杂度分析：
# 单次相似度计算: 25 * 25 = 625次比较
# 单个推荐请求需要计算: 1000个候选用户
# 总计算量: 625 * 1000 = 625,000次运算
# 高峰期QPS: 5000
# 总CPU运算量: 625,000 * 5000 = 31.25亿次/秒
```

## 💡 **根因分析**

### 核心问题识别

#### 1. 算法效率问题
- **时间复杂度过高**：O(n²)的嵌套循环算法
- **重复计算**：相同用户对的相似度被重复计算
- **数学运算密集**：大量浮点运算和数学函数调用
- **数据获取低效**：每次都重新查询数据库

#### 2. 系统设计问题
- **缓存缺失**：计算结果没有缓存机制
- **并发设计不当**：串行计算，未利用多核优势
- **资源控制缺失**：没有限制并发计算的资源消耗
- **降级机制缺失**：高负载时没有降级策略

#### 3. 数据结构问题
- **数据结构选择不当**：使用List进行频繁查找操作
- **内存使用低效**：大量临时对象创建
- **数据预处理不足**：原始数据没有预处理和索引

### 问题触发原因分析

```mermaid
sequenceDiagram
    participant U as 用户请求
    participant R as 推荐服务
    participant A as 算法模块
    participant D as 数据服务
    participant DB as 数据库

    Note over U,DB: CPU飙升触发链路

    U->>R: 推荐请求(QPS突增)
    R->>A: 计算用户相似度
    A->>D: 获取用户行为数据
    D->>DB: 查询数据库
    DB-->>D: 返回大量数据
    D-->>A: 用户行为列表

    Note over A: 开始O(n²)算法计算
    loop 1000次候选用户
        loop 25*25次行为比较
            A->>A: 复杂数学运算
        end
    end

    Note over A: CPU使用率飙升
    A-->>R: 计算超时
    R-->>U: 服务超时

    Note over R: 线程池耗尽，服务雪崩
```

## 🛠️ **解决方案实施**

### 阶段一：紧急止损（13:30-14:00）

#### 1. 立即降级策略
```java
@Component
public class RecommendationCircuitBreaker {

    private final AtomicInteger cpuUsageCounter = new AtomicInteger(0);
    private volatile boolean circuitOpen = false;

    @Scheduled(fixedRate = 5000) // 每5秒检查一次
    public void checkSystemHealth() {
        double cpuUsage = getCpuUsage();

        if (cpuUsage > 80) {
            cpuUsageCounter.incrementAndGet();
            if (cpuUsageCounter.get() >= 3) { // 连续3次超过80%
                circuitOpen = true;
                log.warn("推荐服务熔断开启，CPU使用率: {}%", cpuUsage);
            }
        } else if (cpuUsage < 60) {
            cpuUsageCounter.set(0);
            if (circuitOpen) {
                circuitOpen = false;
                log.info("推荐服务熔断关闭，CPU使用率恢复: {}%", cpuUsage);
            }
        }
    }

    public boolean isCircuitOpen() {
        return circuitOpen;
    }

    private double getCpuUsage() {
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        return osBean.getProcessCpuLoad() * 100;
    }
}

@Service
public class RecommendationServiceV1 {

    @Autowired
    private RecommendationCircuitBreaker circuitBreaker;

    @Autowired
    private FallbackRecommendationService fallbackService;

    public List<RecommendationItem> getRecommendations(long userId) {
        // 熔断检查
        if (circuitBreaker.isCircuitOpen()) {
            log.info("推荐服务熔断中，使用降级策略: userId={}", userId);
            return fallbackService.getHotRecommendations(userId);
        }

        try {
            return generatePersonalizedRecommendations(userId);
        } catch (Exception e) {
            log.error("个性化推荐失败，降级到热门推荐: userId={}", userId, e);
            return fallbackService.getHotRecommendations(userId);
        }
    }
}
```

#### 2. 线程池紧急调优
```java
@Configuration
public class ThreadPoolConfig {

    // 紧急调整线程池配置
    @Bean("recommendationExecutor")
    public ThreadPoolTaskExecutor recommendationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 减少核心线程数，控制CPU使用
        executor.setCorePoolSize(20);  // 原来50
        executor.setMaxPoolSize(40);   // 原来100
        executor.setQueueCapacity(500); // 原来2000

        // 设置拒绝策略，快速失败
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        executor.setThreadNamePrefix("recommendation-");
        executor.initialize();

        return executor;
    }

    // 添加CPU密集任务专用线程池
    @Bean("cpuIntensiveExecutor")
    public ThreadPoolTaskExecutor cpuIntensiveExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // CPU密集型任务，线程数=CPU核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(cpuCores);
        executor.setMaxPoolSize(cpuCores);
        executor.setQueueCapacity(100);

        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix("cpu-intensive-");
        executor.initialize();

        return executor;
    }
}
```

#### 3. 紧急缓存策略
```java
@Service
public class EmergencyCacheService {

    // 使用本地缓存快速缓存计算结果
    private final Cache<String, Double> similarityCache = Caffeine.newBuilder()
            .maximumSize(100000)
            .expireAfterWrite(Duration.ofMinutes(10))
            .build();

    private final Cache<Long, List<RecommendationItem>> recommendationCache = Caffeine.newBuilder()
            .maximumSize(50000)
            .expireAfterWrite(Duration.ofMinutes(5))
            .build();

    public Double getCachedSimilarity(long userId1, long userId2) {
        String key = Math.min(userId1, userId2) + ":" + Math.max(userId1, userId2);
        return similarityCache.getIfPresent(key);
    }

    public void cacheSimilarity(long userId1, long userId2, double similarity) {
        String key = Math.min(userId1, userId2) + ":" + Math.max(userId1, userId2);
        similarityCache.put(key, similarity);
    }

    public List<RecommendationItem> getCachedRecommendations(long userId) {
        return recommendationCache.getIfPresent(userId);
    }

    public void cacheRecommendations(long userId, List<RecommendationItem> recommendations) {
        recommendationCache.put(userId, recommendations);
    }
}
```

### 阶段二：算法优化（次日开始）

#### 1. 高效相似度计算算法
```java
@Service
public class OptimizedCollaborativeFiltering {

    @Autowired
    private EmergencyCacheService cacheService;

    // 优化1：使用向量化计算，预处理用户行为数据
    public Map<Long, SparseVector> preprocessUserBehaviors(List<Long> userIds) {
        Map<Long, SparseVector> userVectors = new HashMap<>();

        // 批量获取用户行为数据
        Map<Long, List<UserBehavior>> userBehaviorsMap =
            userBehaviorService.batchGetUserBehaviors(userIds);

        for (Map.Entry<Long, List<UserBehavior>> entry : userBehaviorsMap.entrySet()) {
            Long userId = entry.getKey();
            List<UserBehavior> behaviors = entry.getValue();

            // 转换为稀疏向量，提高计算效率
            SparseVector vector = convertToSparseVector(behaviors);
            userVectors.put(userId, vector);
        }

        return userVectors;
    }

    // 优化2：使用余弦相似度算法，时间复杂度O(n)
    public double calculateCosineSimilarity(SparseVector vector1, SparseVector vector2) {
        // 检查缓存
        String cacheKey = vector1.getUserId() + ":" + vector2.getUserId();
        Double cached = cacheService.getCachedSimilarity(vector1.getUserId(), vector2.getUserId());
        if (cached != null) {
            return cached;
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        // 只遍历非零元素，大幅减少计算量
        Set<Integer> commonItems = new HashSet<>(vector1.getIndices());
        commonItems.retainAll(vector2.getIndices());

        for (Integer itemId : commonItems) {
            double value1 = vector1.getValue(itemId);
            double value2 = vector2.getValue(itemId);
            dotProduct += value1 * value2;
        }

        // 计算向量模长
        for (Integer itemId : vector1.getIndices()) {
            double value = vector1.getValue(itemId);
            norm1 += value * value;
        }

        for (Integer itemId : vector2.getIndices()) {
            double value = vector2.getValue(itemId);
            norm2 += value * value;
        }

        double similarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));

        // 缓存结果
        cacheService.cacheSimilarity(vector1.getUserId(), vector2.getUserId(), similarity);

        return similarity;
    }

    // 优化3：并行计算用户相似度
    @Async("cpuIntensiveExecutor")
    public CompletableFuture<Map<Long, Double>> computeUserSimilarityAsync(
            long targetUserId, List<Long> candidateUserIds) {

        return CompletableFuture.supplyAsync(() -> {
            // 预处理用户向量
            List<Long> allUserIds = new ArrayList<>(candidateUserIds);
            allUserIds.add(targetUserId);
            Map<Long, SparseVector> userVectors = preprocessUserBehaviors(allUserIds);

            SparseVector targetVector = userVectors.get(targetUserId);
            Map<Long, Double> similarities = new ConcurrentHashMap<>();

            // 并行计算相似度
            candidateUserIds.parallelStream().forEach(candidateUserId -> {
                SparseVector candidateVector = userVectors.get(candidateUserId);
                if (candidateVector != null) {
                    double similarity = calculateCosineSimilarity(targetVector, candidateVector);
                    similarities.put(candidateUserId, similarity);
                }
            });

            return similarities;
        });
    }

    // 优化4：稀疏向量实现
    public static class SparseVector {
        private final long userId;
        private final Map<Integer, Double> values;
        private final Set<Integer> indices;

        public SparseVector(long userId, Map<Integer, Double> values) {
            this.userId = userId;
            this.values = values;
            this.indices = values.keySet();
        }

        public long getUserId() { return userId; }
        public Set<Integer> getIndices() { return indices; }
        public double getValue(Integer index) { return values.getOrDefault(index, 0.0); }
    }

    private SparseVector convertToSparseVector(List<UserBehavior> behaviors) {
        Map<Integer, Double> values = new HashMap<>();

        for (UserBehavior behavior : behaviors) {
            Integer itemId = behavior.getItemId().intValue();
            double score = behavior.getScore();

            // 应用时间衰减权重
            double timeWeight = calculateTimeWeight(behavior.getTimestamp());
            double weightedScore = score * timeWeight;

            values.merge(itemId, weightedScore, Double::sum);
        }

        return new SparseVector(behaviors.get(0).getUserId(), values);
    }

    // 优化5：简化时间权重计算
    private double calculateTimeWeight(long timestamp) {
        long currentTime = System.currentTimeMillis();
        long daysDiff = (currentTime - timestamp) / (24 * 3600 * 1000);

        // 使用简单的线性衰减，避免复杂数学函数
        return Math.max(0.1, 1.0 - daysDiff * 0.1);
    }
}
```

#### 2. 分布式计算架构
```java
@Service
public class DistributedRecommendationService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    // 分布式相似度计算
    public List<RecommendationItem> getRecommendationsDistributed(long userId) {
        // 1. 检查Redis缓存
        String cacheKey = "recommendations:" + userId;
        List<RecommendationItem> cached = (List<RecommendationItem>)
            redisTemplate.opsForValue().get(cacheKey);

        if (cached != null) {
            return cached;
        }

        // 2. 异步分布式计算
        CompletableFuture<List<RecommendationItem>> future =
            computeRecommendationsAsync(userId);

        try {
            List<RecommendationItem> recommendations = future.get(2, TimeUnit.SECONDS);

            // 3. 缓存结果
            redisTemplate.opsForValue().set(cacheKey, recommendations, Duration.ofMinutes(30));

            return recommendations;

        } catch (TimeoutException e) {
            log.warn("推荐计算超时，使用降级策略: userId={}", userId);
            return fallbackService.getHotRecommendations(userId);
        } catch (Exception e) {
            log.error("推荐计算异常: userId={}", userId, e);
            return fallbackService.getHotRecommendations(userId);
        }
    }

    @Async("cpuIntensiveExecutor")
    private CompletableFuture<List<RecommendationItem>> computeRecommendationsAsync(long userId) {
        return CompletableFuture.supplyAsync(() -> {
            // 分片计算用户相似度
            List<Long> candidateUsers = getCandidateUsers(userId);
            int shardSize = 200; // 每个分片200个用户

            List<CompletableFuture<Map<Long, Double>>> futures = new ArrayList<>();

            for (int i = 0; i < candidateUsers.size(); i += shardSize) {
                int endIndex = Math.min(i + shardSize, candidateUsers.size());
                List<Long> shard = candidateUsers.subList(i, endIndex);

                CompletableFuture<Map<Long, Double>> future =
                    collaborativeFiltering.computeUserSimilarityAsync(userId, shard);
                futures.add(future);
            }

            // 合并分片结果
            Map<Long, Double> allSimilarities = new HashMap<>();
            for (CompletableFuture<Map<Long, Double>> future : futures) {
                try {
                    allSimilarities.putAll(future.get());
                } catch (Exception e) {
                    log.error("分片计算失败", e);
                }
            }

            // 生成推荐结果
            return generateRecommendations(userId, allSimilarities);
        });
    }
}
```

## 📊 **优化效果对比**

### 性能指标对比

| 指标 | 优化前 | 紧急优化后 | 深度优化后 | 改善幅度 |
|------|--------|------------|------------|----------|
| CPU使用率 | 96% | 65% | 35% | **63.5%** ↓ |
| 平均响应时间 | 12.3s | 2.1s | 45ms | **99.6%** ↓ |
| P99响应时间 | 25s | 5.2s | 120ms | **99.5%** ↓ |
| 服务可用性 | 60% | 85% | 99.9% | **66.5%** ↑ |
| QPS处理能力 | 500 | 2000 | 8000 | **1500%** ↑ |
| 算法计算时间 | 8.5s | 1.8s | 15ms | **99.8%** ↓ |

### 算法复杂度对比

```mermaid
graph LR
    subgraph "优化前"
        A1[O(n²)嵌套循环]
        A2[重复数据获取]
        A3[复杂数学运算]
        A4[串行计算]
    end

    subgraph "优化后"
        B1[O(n)向量计算]
        B2[批量数据预处理]
        B3[简化算法逻辑]
        B4[并行分布式计算]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
```

### 资源使用对比

```bash
# 服务器资源使用情况
优化前:
- CPU: 96% (16核心全部满载)
- 内存: 28GB (大量临时对象)
- 线程数: 2847 (线程池耗尽)
- 网络连接: 15000+ (连接积压)

优化后:
- CPU: 35% (合理利用多核)
- 内存: 18GB (对象复用优化)
- 线程数: 200 (线程池稳定)
- 网络连接: 3000 (连接正常)
```

## 🔧 **监控与告警优化**

### CPU性能监控

```java
@Component
public class CPUPerformanceMonitor {

    private final MeterRegistry meterRegistry;
    private final OperatingSystemMXBean osBean;

    public CPUPerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.osBean = ManagementFactory.getOperatingSystemMXBean();
        initCPUMetrics();
    }

    private void initCPUMetrics() {
        // CPU使用率监控
        Gauge.builder("system.cpu.usage")
            .description("系统CPU使用率")
            .register(meterRegistry, osBean, bean -> bean.getProcessCpuLoad());

        // 系统负载监控
        Gauge.builder("system.load.average")
            .description("系统平均负载")
            .register(meterRegistry, osBean, bean -> bean.getSystemLoadAverage());

        // 线程数监控
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        Gauge.builder("jvm.threads.count")
            .description("JVM线程数")
            .register(meterRegistry, threadBean, ThreadMXBean::getThreadCount);
    }

    @Scheduled(fixedRate = 10000) // 每10秒检查一次
    public void checkCPUHealth() {
        double cpuUsage = osBean.getProcessCpuLoad();
        double loadAverage = osBean.getSystemLoadAverage();
        int availableProcessors = osBean.getAvailableProcessors();

        // CPU使用率告警
        if (cpuUsage > 0.8) { // CPU使用率超过80%
            log.warn("CPU使用率告警: {}%", cpuUsage * 100);
            sendCPUAlert(cpuUsage);
        }

        // 系统负载告警
        if (loadAverage > availableProcessors * 2) { // 负载超过CPU核心数的2倍
            log.warn("系统负载告警: {}, CPU核心数: {}", loadAverage, availableProcessors);
            sendLoadAlert(loadAverage, availableProcessors);
        }
    }

    private void sendCPUAlert(double cpuUsage) {
        AlertMessage alert = AlertMessage.builder()
            .title("CPU使用率告警")
            .content(String.format("当前CPU使用率: %.2f%%", cpuUsage * 100))
            .level(AlertLevel.WARNING)
            .build();

        alertService.sendAlert(alert);
    }

    private void sendLoadAlert(double loadAverage, int cpuCores) {
        AlertMessage alert = AlertMessage.builder()
            .title("系统负载告警")
            .content(String.format("当前系统负载: %.2f, CPU核心数: %d", loadAverage, cpuCores))
            .level(AlertLevel.WARNING)
            .build();

        alertService.sendAlert(alert);
    }
}
```

### 算法性能监控

```java
@Component
public class AlgorithmPerformanceMonitor {

    private final Timer similarityCalculationTimer;
    private final Timer recommendationGenerationTimer;
    private final Counter cacheHitCounter;
    private final Counter cacheMissCounter;

    public AlgorithmPerformanceMonitor(MeterRegistry meterRegistry) {
        this.similarityCalculationTimer = Timer.builder("algorithm.similarity.calculation.time")
            .description("相似度计算耗时")
            .register(meterRegistry);

        this.recommendationGenerationTimer = Timer.builder("algorithm.recommendation.generation.time")
            .description("推荐生成耗时")
            .register(meterRegistry);

        this.cacheHitCounter = Counter.builder("algorithm.cache.hit")
            .description("缓存命中次数")
            .register(meterRegistry);

        this.cacheMissCounter = Counter.builder("algorithm.cache.miss")
            .description("缓存未命中次数")
            .register(meterRegistry);
    }

    public void recordSimilarityCalculation(long duration) {
        similarityCalculationTimer.record(duration, TimeUnit.MILLISECONDS);

        if (duration > 100) { // 相似度计算超过100ms
            log.warn("相似度计算耗时异常: {}ms", duration);
        }
    }

    public void recordRecommendationGeneration(long duration) {
        recommendationGenerationTimer.record(duration, TimeUnit.MILLISECONDS);

        if (duration > 500) { // 推荐生成超过500ms
            log.warn("推荐生成耗时异常: {}ms", duration);
        }
    }

    public void recordCacheHit() {
        cacheHitCounter.increment();
    }

    public void recordCacheMiss() {
        cacheMissCounter.increment();
    }

    @Scheduled(fixedRate = 60000) // 每分钟统计一次
    public void reportCacheStatistics() {
        double hitCount = cacheHitCounter.count();
        double missCount = cacheMissCounter.count();
        double totalCount = hitCount + missCount;

        if (totalCount > 0) {
            double hitRate = hitCount / totalCount;
            log.info("缓存命中率: {:.2f}%, 命中次数: {}, 未命中次数: {}",
                hitRate * 100, (long)hitCount, (long)missCount);

            if (hitRate < 0.8) { // 缓存命中率低于80%
                log.warn("缓存命中率过低: {:.2f}%", hitRate * 100);
            }
        }
    }
}
```

### 实时性能大盘

```yaml
# Grafana Dashboard配置
dashboard:
  title: "推荐系统性能监控"
  panels:
    - title: "CPU使用率趋势"
      type: "graph"
      targets:
        - expr: "system_cpu_usage * 100"
          legendFormat: "CPU使用率(%)"
        - expr: "system_load_average"
          legendFormat: "系统负载"

    - title: "算法性能指标"
      type: "graph"
      targets:
        - expr: "histogram_quantile(0.99, algorithm_similarity_calculation_time_bucket)"
          legendFormat: "P99相似度计算时间"
        - expr: "histogram_quantile(0.99, algorithm_recommendation_generation_time_bucket)"
          legendFormat: "P99推荐生成时间"

    - title: "缓存性能"
      type: "stat"
      targets:
        - expr: "rate(algorithm_cache_hit_total[1m]) / (rate(algorithm_cache_hit_total[1m]) + rate(algorithm_cache_miss_total[1m])) * 100"
          legendFormat: "缓存命中率(%)"

    - title: "服务可用性"
      type: "graph"
      targets:
        - expr: "rate(http_requests_total{status=~\"2..\"}[1m]) / rate(http_requests_total[1m]) * 100"
          legendFormat: "成功率(%)"
```

## 🎯 **问题复盘与SOP**

### 问题复盘总结

#### 技术层面反思
1. **算法设计缺陷**：
   - 选择了时间复杂度过高的算法
   - 没有考虑大数据量下的性能表现
   - 缺乏算法性能基准测试

2. **系统架构问题**：
   - 缺乏有效的缓存策略
   - 没有设计降级和熔断机制
   - 资源控制和并发管理不当

3. **监控体系不完善**：
   - 缺乏算法级别的性能监控
   - CPU使用率告警阈值设置不合理
   - 没有建立性能基线和趋势分析

#### 流程层面反思
1. **性能测试不充分**：
   - 没有进行大数据量的性能测试
   - 缺乏CPU密集型场景的压力测试
   - 算法复杂度分析不够深入

2. **代码审查标准**：
   - 缺乏算法复杂度的审查标准
   - 对CPU密集型代码的审查不够严格
   - 没有建立性能回归检测机制

### CPU问题排查SOP

#### 发现阶段
```mermaid
flowchart TD
    A[收到CPU告警] --> B{确认告警真实性}
    B -->|是| C[评估影响范围]
    B -->|否| D[调整告警阈值]

    C --> E[查看系统负载]
    E --> F[分析进程状态]
    F --> G{CPU使用类型}

    G -->|用户态高| H[应用程序问题]
    G -->|系统态高| I[系统调用问题]
    G -->|等待IO高| J[IO瓶颈问题]

    H --> K[生成线程栈]
    I --> L[查看系统调用]
    J --> M[分析IO状况]

    K --> N[CPU热点分析]
    L --> N
    M --> N

    N --> O[定位问题代码]
```

#### 处理阶段
```bash
# 1. 紧急响应（10分钟内）
- 确认CPU使用率和系统负载
- 查看应用程序状态
- 评估是否需要重启服务
- 启动降级和熔断机制

# 2. 快速诊断（30分钟内）
- 生成线程栈快照
- 使用性能分析工具
- 识别CPU热点方法
- 分析算法复杂度

# 3. 临时修复（60分钟内）
- 调整线程池配置
- 启用缓存机制
- 优化关键算法
- 发布临时版本

# 4. 根本解决（1-3天）
- 算法重构优化
- 架构调整
- 性能测试验证
- 监控告警完善
```

#### 预防阶段
```bash
# 1. 开发阶段
- 算法复杂度分析
- CPU密集型代码审查
- 性能基准测试
- 并发安全性验证

# 2. 测试阶段
- 大数据量性能测试
- CPU使用率压力测试
- 长时间稳定性测试
- 性能回归测试

# 3. 上线阶段
- CPU监控配置
- 性能基线建立
- 降级机制验证
- 应急预案准备

# 4. 运维阶段
- 定期性能分析
- CPU使用趋势监控
- 算法性能优化
- 容量规划调整
```

### 最佳实践总结

#### 算法优化最佳实践
1. **复杂度分析**：
   - 在设计阶段就要分析算法复杂度
   - 考虑数据规模增长对性能的影响
   - 选择适合业务场景的算法
   - 建立算法性能基准测试

2. **数据结构优化**：
   - 根据访问模式选择合适的数据结构
   - 使用稀疏数据结构处理大规模稀疏数据
   - 预处理数据以提高计算效率
   - 考虑内存局部性优化

3. **并行计算**：
   - 识别可并行化的计算任务
   - 合理设计并行度，避免过度并行
   - 使用分治算法减少计算复杂度
   - 考虑NUMA架构的影响

#### 系统架构最佳实践
1. **缓存策略**：
   - 多级缓存设计
   - 合理的缓存过期策略
   - 缓存预热和更新机制
   - 缓存穿透和雪崩防护

2. **降级熔断**：
   - 基于CPU使用率的自动熔断
   - 多级降级策略
   - 快速恢复机制
   - 降级效果监控

3. **资源控制**：
   - CPU密集型任务的资源隔离
   - 线程池合理配置
   - 任务队列容量控制
   - 背压机制设计

---

**总结**：通过这次CPU飙升问题的深度优化，不仅解决了当前的性能瓶颈，还建立了完善的算法性能监控和优化体系。系统的CPU使用率降低了63.5%，响应时间提升了99.6%，为推荐系统的稳定运行和业务发展提供了强有力的技术保障。
