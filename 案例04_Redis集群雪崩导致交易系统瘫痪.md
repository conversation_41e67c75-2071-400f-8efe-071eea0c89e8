# 案例04：Redis集群雪崩导致交易系统瘫痪问题

## 🚨 **问题背景**

### 业务场景
- **项目**：美团交易系统核心服务
- **功能**：订单支付、库存扣减、优惠券核销等核心交易流程
- **依赖**：Redis集群作为缓存和分布式锁，承载90%+的读写请求
- **服务规模**：30台应用服务器，Redis集群6主6从，日交易量1000万+

### 问题现象
- **时间**：2023年9月10日 14:30开始，周末购物高峰期
- **现象**：Redis集群中3个主节点同时宕机，引发系统雪崩
- **影响范围**：
  - 交易成功率从99.8%骤降至15%
  - 订单支付接口响应时间从100ms飙升至30秒+
  - 数据库连接池瞬间耗尽，CPU使用率达到100%
  - 用户无法完成支付，客服投诉激增

### 告警信息
```bash
# 系统告警（14:31开始密集告警）
[ALERT] Redis集群节点宕机
时间: 2023-09-10 14:30:45
节点: redis-cluster-1, redis-cluster-3, redis-cluster-5
状态: 连接超时，节点不可达
影响: 50%的Redis分片不可用
告警级别: P0

[ALERT] 数据库连接池耗尽
服务: transaction-service
连接池状态: 500/500 (100%使用率)
等待连接数: 2000+
平均等待时间: 15秒
告警级别: P0

[ALERT] 服务响应时间异常
服务: transaction-service
平均响应时间: 32.5s (正常值: 100ms)
超时率: 85%
错误率: 78%
告警级别: P0
```

## 🔍 **问题排查过程**

### 第一阶段：紧急定位（14:31-14:45）

#### 1. Redis集群状态检查
```bash
# 检查Redis集群状态
redis-cli --cluster check redis-cluster-1:6379
>>> Performing Cluster Check (using node redis-cluster-1:6379)
[ERR] Sorry, can't connect to node redis-cluster-1:6379

# 检查其他节点
redis-cli -h redis-cluster-2 -p 6379 cluster nodes
07c37dfeb235213a872192d90877d0cd55635b91 redis-cluster-1:6379@16379 master,fail - 1694329845000 1694329843000 1 disconnected
e7d1eecce10fd6bb5eb35b9f99a5dc4b3bb44e65 redis-cluster-3:6379@16379 master,fail - 1694329845000 1694329843000 3 disconnected  
2e48b6b16396933d017358b8ca40a50db2cd25bf redis-cluster-5:6379@16379 master,fail - 1694329845000 1694329843000 5 disconnected

# 发现3个主节点同时失联，集群进入fail状态
```

#### 2. 应用服务状态检查
```bash
# 查看应用服务日志
kubectl logs -f transaction-service-xxx | grep -E "ERROR|Redis"

# 关键错误日志
2023-09-10 14:30:47.123 ERROR [http-nio-8080-exec-123] c.m.t.s.PaymentService - Redis连接失败: redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool
2023-09-10 14:30:48.456 ERROR [http-nio-8080-exec-156] c.m.t.s.InventoryService - 获取库存信息失败，回退到数据库查询
2023-09-10 14:30:49.789 ERROR [http-nio-8080-exec-189] c.m.t.s.CouponService - 分布式锁获取失败: org.redisson.client.RedisConnectionException: Unable to connect to Redis server
```

#### 3. 数据库压力检查
```sql
-- 查看数据库连接数
SHOW PROCESSLIST;
-- 发现大量连接处于Sleep状态，连接池耗尽

-- 查看慢查询
SELECT * FROM information_schema.PROCESSLIST 
WHERE COMMAND != 'Sleep' AND TIME > 5;
-- 发现大量原本由Redis承载的查询直接打到数据库
```

### 第二阶段：根因分析（14:45-15:30）

#### 1. Redis节点宕机原因分析
```bash
# 检查Redis服务器状态
ssh redis-cluster-1
# 发现服务器正常，Redis进程异常退出

# 查看Redis日志
tail -f /var/log/redis/redis-server.log
# 关键日志信息
[12345] 10 Sep 14:30:43.123 # WARNING: The TCP backlog setting of 511 cannot be enforced because /proc/sys/net/core/somaxconn is set to the lower value of 128.
[12345] 10 Sep 14:30:44.456 # Server started, Redis version 6.2.6
[12345] 10 Sep 14:30:45.789 * Background saving started by pid 12346
[12345] 10 Sep 14:30:45.890 # Out of memory: Kill process 12345 (redis-server) score 950 or sacrifice child

# 发现Redis进程被OOM Killer杀死
```

#### 2. 内存使用分析
```bash
# 分析Redis内存使用
redis-cli -h redis-cluster-2 -p 6379 info memory
used_memory:15728640000  # 约14.6GB
used_memory_human:14.64G
used_memory_rss:16106127360  # 约15GB
used_memory_peak:16106127360
maxmemory:16106127360  # 最大内存15GB

# 分析内存使用分布
redis-cli -h redis-cluster-2 -p 6379 --bigkeys
# 发现大量大key存在
```

#### 3. 应用代码问题分析
```java
@Service
public class TransactionService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 问题1：缓存设计不当，存储大对象
    public OrderInfo getOrderInfo(String orderId) {
        String cacheKey = "order_info:" + orderId;
        OrderInfo orderInfo = (OrderInfo) redisTemplate.opsForValue().get(cacheKey);
        
        if (orderInfo == null) {
            orderInfo = orderService.getOrderFromDB(orderId);
            
            // 问题：直接缓存完整订单对象，包含大量冗余数据
            redisTemplate.opsForValue().set(cacheKey, orderInfo, Duration.ofHours(24));
        }
        
        return orderInfo;
    }
    
    // 问题2：批量操作没有分页，一次性加载大量数据
    public List<ProductInfo> getHotProducts() {
        String cacheKey = "hot_products";
        List<ProductInfo> products = (List<ProductInfo>) redisTemplate.opsForValue().get(cacheKey);
        
        if (products == null) {
            // 一次性加载10万+商品数据到Redis
            products = productService.getAllHotProducts();
            redisTemplate.opsForValue().set(cacheKey, products, Duration.ofMinutes(30));
        }
        
        return products;
    }
    
    // 问题3：分布式锁没有超时机制
    public boolean processPayment(String orderId) {
        String lockKey = "payment_lock:" + orderId;
        
        try {
            // 获取锁，但没有设置超时时间
            Boolean lockResult = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, "locked");
            
            if (!lockResult) {
                return false;
            }
            
            // 处理支付逻辑
            return doPayment(orderId);
            
        } finally {
            // 问题：finally块中删除锁，如果Redis宕机会导致锁无法释放
            redisTemplate.delete(lockKey);
        }
    }
    
    // 问题4：缓存穿透没有防护
    public UserInfo getUserInfo(Long userId) {
        String cacheKey = "user_info:" + userId;
        UserInfo userInfo = (UserInfo) redisTemplate.opsForValue().get(cacheKey);
        
        if (userInfo == null) {
            userInfo = userService.getUserFromDB(userId);
            
            // 如果用户不存在，没有缓存空值，导致缓存穿透
            if (userInfo != null) {
                redisTemplate.opsForValue().set(cacheKey, userInfo, Duration.ofMinutes(30));
            }
        }
        
        return userInfo;
    }
}
```

### 第三阶段：雪崩链路分析（15:30-16:00）

#### 雪崩传播路径
```mermaid
sequenceDiagram
    participant U as 用户请求
    participant A as 应用服务
    participant R as Redis集群
    participant DB as 数据库
    participant M as 监控告警

    Note over U,M: Redis雪崩传播链路
    
    U->>A: 支付请求
    A->>R: 获取缓存数据
    R-->>A: 连接超时/节点宕机
    
    Note over A: Redis不可用，回退到数据库
    
    A->>DB: 直接查询数据库
    DB-->>A: 查询缓慢(大量并发)
    
    Note over A: 数据库压力激增
    
    A->>DB: 更多查询请求
    Note over DB: 连接池耗尽
    DB-->>A: 连接超时
    
    A-->>U: 服务超时
    
    Note over A: 应用服务雪崩
    M->>M: 告警风暴
```
